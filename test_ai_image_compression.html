<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Image Compression Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        
        .image-comparison {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .image-container {
            flex: 1;
            text-align: center;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .size-info {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>🗜️ AI Image Compression Test</h1>
    <p>This test verifies that AI-generated images are properly compressed and exported.</p>

    <div class="test-section">
        <h2>Test 1: Image Compression Utility</h2>
        <button onclick="testImageCompression()">Test Compression</button>
        <div id="compression-results"></div>
        <div id="image-comparison" class="image-comparison" style="display: none;">
            <div class="image-container">
                <h4>Original Image</h4>
                <img id="original-image" alt="Original">
                <div id="original-size" class="size-info"></div>
            </div>
            <div class="image-container">
                <h4>Compressed Image</h4>
                <img id="compressed-image" alt="Compressed">
                <div id="compressed-size" class="size-info"></div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Test 2: Export Service AI Image Detection</h2>
        <button onclick="testExportDetection()">Test Export Detection</button>
        <div id="export-results"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Document Size Impact</h2>
        <button onclick="testDocumentSize()">Test Document Size</button>
        <div id="size-results"></div>
    </div>

    <div class="test-section">
        <h2>🔍 Debug: AI Image Attribute Detection</h2>
        <p>This test helps debug the missing <code>data-ai-generated="true"</code> attribute issue.</p>
        <button onclick="testAttributeDetection()">Test Attribute Detection</button>
        <div id="attribute-results"></div>

        <h3>Manual Test Instructions:</h3>
        <ol>
            <li>Generate an AI image in the DocForge editor</li>
            <li>Right-click the image → "Inspect Element"</li>
            <li>Look for <code>data-ai-generated="true"</code> in the img tag</li>
            <li>If missing, the Enhanced Image Extension needs the attribute definitions</li>
            <li>Try exporting - AI images should now appear instead of placeholder text</li>
        </ol>
    </div>

    <script type="module">
        // Sample base64 image data (small test image)
        const sampleBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==';
        
        // Mock the compression function for testing
        window.testImageCompression = async function() {
            const resultsDiv = document.getElementById('compression-results');
            const comparisonDiv = document.getElementById('image-comparison');
            
            try {
                resultsDiv.innerHTML = '<div class="test-result info">Testing image compression...</div>';
                
                // Test with a larger sample image (create a canvas with some content)
                const canvas = document.createElement('canvas');
                canvas.width = 800;
                canvas.height = 600;
                const ctx = canvas.getContext('2d');
                
                // Draw some content to make it more realistic
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(0, 0, 800, 600);
                ctx.fillStyle = '#FFF';
                ctx.font = '48px Arial';
                ctx.fillText('AI Generated Image', 150, 300);
                
                const originalDataUrl = canvas.toDataURL('image/png', 1.0);
                const originalBase64 = originalDataUrl.split(',')[1];
                const originalSize = Math.ceil(originalBase64.length * 0.75);
                
                // Show original image
                document.getElementById('original-image').src = originalDataUrl;
                document.getElementById('original-size').textContent = `Size: ${(originalSize / 1024).toFixed(1)} KB`;
                
                // Simulate compression (create a smaller version)
                const compressedDataUrl = canvas.toDataURL('image/webp', 0.75);
                const compressedBase64 = compressedDataUrl.split(',')[1];
                const compressedSize = Math.ceil(compressedBase64.length * 0.75);
                
                // Show compressed image
                document.getElementById('compressed-image').src = compressedDataUrl;
                document.getElementById('compressed-size').textContent = `Size: ${(compressedSize / 1024).toFixed(1)} KB`;
                
                const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);
                
                resultsDiv.innerHTML = `
                    <div class="test-result success">✅ Compression test completed</div>
                    <div class="test-result info">Original size: ${(originalSize / 1024).toFixed(1)} KB</div>
                    <div class="test-result info">Compressed size: ${(compressedSize / 1024).toFixed(1)} KB</div>
                    <div class="test-result success">Compression ratio: ${compressionRatio}% reduction</div>
                `;
                
                comparisonDiv.style.display = 'flex';
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-result error">❌ Compression test failed: ${error.message}</div>`;
            }
        };
        
        window.testExportDetection = function() {
            const resultsDiv = document.getElementById('export-results');
            
            try {
                resultsDiv.innerHTML = '<div class="test-result info">Testing export detection...</div>';
                
                // Test HTML samples
                const aiImageHTML = '<img src="data:image/webp;base64,UklGRiQAAABXRUJQVlA4..." data-ai-generated="true" alt="AI image">';
                const regularImageHTML = '<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB..." alt="Regular image">';
                
                // Simulate the export detection logic
                const detectAIImage = (html) => {
                    return /data-ai-generated\s*=\s*["']true["']/i.test(html);
                };
                
                const aiDetected = detectAIImage(aiImageHTML);
                const regularDetected = detectAIImage(regularImageHTML);
                
                let results = '';
                if (aiDetected && !regularDetected) {
                    results += '<div class="test-result success">✅ AI image detection working correctly</div>';
                    results += '<div class="test-result info">AI images will be preserved in exports</div>';
                    results += '<div class="test-result info">Regular base64 images will be replaced with placeholders</div>';
                } else {
                    results += '<div class="test-result error">❌ AI image detection not working properly</div>';
                }
                
                resultsDiv.innerHTML = results;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-result error">❌ Export detection test failed: ${error.message}</div>`;
            }
        };
        
        window.testDocumentSize = function() {
            const resultsDiv = document.getElementById('size-results');
            
            try {
                resultsDiv.innerHTML = '<div class="test-result info">Testing document size impact...</div>';
                
                // Simulate document with multiple AI images
                const createMockDocument = (imageCount, imageSize) => {
                    const mockImages = Array(imageCount).fill().map((_, i) => ({
                        id: i,
                        base64Data: 'x'.repeat(imageSize), // Simulate base64 data
                        compressed: false
                    }));
                    
                    return {
                        title: 'Test Document',
                        content: 'Document content...',
                        images: mockImages
                    };
                };
                
                // Before compression (large images)
                const docBefore = createMockDocument(3, 1000000); // 3 images, ~1MB each
                const sizeBefore = JSON.stringify(docBefore).length;
                
                // After compression (smaller images)
                const docAfter = createMockDocument(3, 300000); // 3 images, ~300KB each
                const sizeAfter = JSON.stringify(docAfter).length;
                
                const reduction = ((sizeBefore - sizeAfter) / sizeBefore * 100).toFixed(1);
                
                resultsDiv.innerHTML = `
                    <div class="test-result info">Document with 3 AI images:</div>
                    <div class="test-result warning">Before compression: ${(sizeBefore / 1024 / 1024).toFixed(2)} MB</div>
                    <div class="test-result success">After compression: ${(sizeAfter / 1024 / 1024).toFixed(2)} MB</div>
                    <div class="test-result success">Size reduction: ${reduction}%</div>
                    <div class="test-result ${sizeAfter < 1048576 ? 'success' : 'warning'}">
                        ${sizeAfter < 1048576 ? '✅ Under 1MB limit' : '⚠️ Still over 1MB limit'}
                    </div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-result error">❌ Document size test failed: ${error.message}</div>`;
            }
        };

        window.testAttributeDetection = function() {
            const resultsDiv = document.getElementById('attribute-results');

            try {
                resultsDiv.innerHTML = '<div class="test-result info">Testing AI image attribute detection...</div>';

                // Test different HTML samples
                const testCases = [
                    {
                        name: 'Correct AI Image (should be preserved)',
                        html: '<img src="data:image/webp;base64,UklGRiQAAABXRUJQ..." data-ai-generated="true" alt="AI image">',
                        expected: true
                    },
                    {
                        name: 'AI Image without attribute (current bug)',
                        html: '<img src="data:image/webp;base64,UklGRiQAAABXRUJQ..." alt="AI generated: image of donkey">',
                        expected: false
                    },
                    {
                        name: 'Regular base64 image (should be replaced)',
                        html: '<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB..." alt="Regular image">',
                        expected: false
                    },
                    {
                        name: 'AI Image with different quote style',
                        html: '<img src="data:image/webp;base64,UklGRiQAAABXRUJQ..." data-ai-generated=\'true\' alt="AI image">',
                        expected: true
                    }
                ];

                // Test the regex pattern from export service
                const detectAIImage = (html) => {
                    return /data-ai-generated\s*=\s*["']true["']/i.test(html);
                };

                let results = '<div class="test-result info">Testing regex pattern: /data-ai-generated\\s*=\\s*["\'"]true["\'"]]/i</div>';
                let allPassed = true;

                testCases.forEach((testCase, index) => {
                    const detected = detectAIImage(testCase.html);
                    const passed = detected === testCase.expected;
                    allPassed = allPassed && passed;

                    results += `<div class="test-result ${passed ? 'success' : 'error'}">
                        ${passed ? '✅' : '❌'} ${testCase.name}: ${detected ? 'Detected as AI' : 'Not detected as AI'}
                    </div>`;
                });

                if (allPassed) {
                    results += '<div class="test-result success">🎉 All attribute detection tests passed!</div>';
                    results += '<div class="test-result info">The export service should now preserve AI images correctly.</div>';
                } else {
                    results += '<div class="test-result error">❌ Some tests failed. Check the regex pattern or HTML structure.</div>';
                }

                resultsDiv.innerHTML = results;

            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-result error">❌ Attribute detection test failed: ${error.message}</div>`;
            }
        };
    </script>
</body>
</html>
