/**
 * Manual test script to verify export template integration
 * Run this in the browser console to test the integration
 */

// Test data
const mockTemplate = {
  id: 'test-template',
  name: 'Test Template',
  category: 'business',
  background_image_url: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
  background_image_width: 1200,
  background_image_height: 1600,
  text_overlays: {
    overlays: [
      {
        id: 'title',
        placeholder: '{{title}}',
        position: { x: 100, y: 400 },
        styling: { fontSize: 48, color: '#000000', fontFamily: 'Arial', fontWeight: 'bold' }
      },
      {
        id: 'author',
        placeholder: '{{author}}',
        position: { x: 100, y: 500 },
        styling: { fontSize: 24, color: '#666666', fontFamily: 'Arial' }
      }
    ]
  }
};

const mockDocumentData = {
  title: 'Test Document with Template',
  author: 'Test Author',
  description: 'This is a test document to verify template integration in exports.',
  contentDetails: {
    customCoverImage: {
      enabled: false
    }
  }
};

const mockGeneratedContent = {
  title: 'Test Document with Template',
  chapters: [
    {
      id: 'chapter-1',
      title: 'Introduction',
      content: 'This is the introduction chapter of our test document.'
    },
    {
      id: 'chapter-2', 
      title: 'Main Content',
      content: 'This is the main content of our test document with some sample text to verify the export functionality.'
    }
  ]
};

// Test functions
async function testCoverPreviewGeneration() {
  console.log('🧪 Testing cover preview generation...');
  
  try {
    // Import the cover preview service
    const { default: coverPreviewService } = await import('../src/services/coverPreviewService.js');
    
    // Generate cover preview
    const coverPreview = await coverPreviewService.generateCoverPreview(
      mockTemplate, 
      mockDocumentData,
      { quality: 0.9, format: 'png' }
    );
    
    console.log('✅ Cover preview generated successfully:', {
      hasHTML: !!coverPreview.coverHTML,
      hasImageData: !!coverPreview.coverImageData,
      templateId: coverPreview.metadata.templateId,
      templateName: coverPreview.metadata.templateName
    });
    
    return coverPreview;
    
  } catch (error) {
    console.error('❌ Cover preview generation failed:', error);
    return null;
  }
}

async function testHtmlExportWithTemplate() {
  console.log('🧪 Testing HTML export with template...');
  
  try {
    // Import the export service
    const { exportAsHtml } = await import('../src/services/exportService.js');
    
    // Test HTML export with template
    const result = await exportAsHtml(
      mockDocumentData,
      mockGeneratedContent,
      { selectedTemplate: mockTemplate }
    );
    
    console.log('✅ HTML export completed:', result);
    return result;
    
  } catch (error) {
    console.error('❌ HTML export failed:', error);
    return null;
  }
}

async function testPdfExportWithTemplate() {
  console.log('🧪 Testing PDF export with template...');
  
  try {
    // Import the export service
    const { exportAsPdf } = await import('../src/services/exportService.js');
    
    // Test PDF export with template
    const result = await exportAsPdf(
      mockDocumentData,
      mockGeneratedContent,
      { selectedTemplate: mockTemplate }
    );
    
    console.log('✅ PDF export completed:', result);
    return result;
    
  } catch (error) {
    console.error('❌ PDF export failed:', error);
    return null;
  }
}

async function testDocxCoverGeneration() {
  console.log('🧪 Testing DOCX cover generation...');
  
  try {
    // Import the DOCX service
    const { createCoverTemplatePage } = await import('../src/services/docxGenerationService.js');
    
    // Test cover template page creation
    const coverPage = await createCoverTemplatePage(
      mockTemplate,
      mockDocumentData,
      {}
    );
    
    console.log('✅ DOCX cover page generated:', {
      isArray: Array.isArray(coverPage),
      length: coverPage?.length,
      hasContent: coverPage && coverPage.length > 0
    });
    
    return coverPage;
    
  } catch (error) {
    console.error('❌ DOCX cover generation failed:', error);
    return null;
  }
}

// Main test runner
async function runIntegrationTests() {
  console.log('🚀 Starting Export Template Integration Tests...');
  console.log('=====================================');
  
  const results = {
    coverPreview: null,
    htmlExport: null,
    pdfExport: null,
    docxCover: null
  };
  
  // Test 1: Cover Preview Generation
  results.coverPreview = await testCoverPreviewGeneration();
  
  // Test 2: HTML Export with Template
  results.htmlExport = await testHtmlExportWithTemplate();
  
  // Test 3: PDF Export with Template
  results.pdfExport = await testPdfExportWithTemplate();
  
  // Test 4: DOCX Cover Generation
  results.docxCover = await testDocxCoverGeneration();
  
  // Summary
  console.log('=====================================');
  console.log('📊 Test Results Summary:');
  console.log('Cover Preview:', results.coverPreview ? '✅ PASS' : '❌ FAIL');
  console.log('HTML Export:', results.htmlExport ? '✅ PASS' : '❌ FAIL');
  console.log('PDF Export:', results.pdfExport ? '✅ PASS' : '❌ FAIL');
  console.log('DOCX Cover:', results.docxCover ? '✅ PASS' : '❌ FAIL');
  
  const passCount = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passCount}/${totalTests} tests passed`);
  
  if (passCount === totalTests) {
    console.log('🎉 All tests passed! Export template integration is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check the errors above for details.');
  }
  
  return results;
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.testExportIntegration = runIntegrationTests;
  window.testCoverPreview = testCoverPreviewGeneration;
  window.testHtmlExport = testHtmlExportWithTemplate;
  window.testPdfExport = testPdfExportWithTemplate;
  window.testDocxCover = testDocxCoverGeneration;
  
  console.log('🔧 Export integration test functions loaded!');
  console.log('Run window.testExportIntegration() to start all tests');
  console.log('Or run individual tests: testCoverPreview(), testHtmlExport(), testPdfExport(), testDocxCover()');
}

export { runIntegrationTests, testCoverPreviewGeneration, testHtmlExportWithTemplate, testPdfExportWithTemplate, testDocxCoverGeneration };
