/**
 * <PERSON><PERSON><PERSON> to fix the thumbnail extraction bug for editorHTML content
 * This addresses the issue where thumbnails disappear after editing projects
 */

const fs = require("fs");
const path = require("path");
const { createClient } = require("@supabase/supabase-js");
const dotenv = require("dotenv");

// Load environment variables
dotenv.config();

// Connect to Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("Missing required environment variables");
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { persistSession: false },
});

async function fixThumbnailExtraction() {
  try {
    console.log("🔧 Fixing thumbnail extraction for editorHTML content...");

    // Read the fix SQL file
    const sqlFilePath = path.join(
      process.cwd(),
      "database/fix-thumbnail-editorhtml.sql"
    );

    if (!fs.existsSync(sqlFilePath)) {
      console.error("❌ Fix SQL file not found:", sqlFilePath);
      process.exit(1);
    }

    const sql = fs.readFileSync(sqlFilePath, "utf8");

    console.log("📝 Executing thumbnail extraction fix...");

    // Execute the SQL directly
    const { error } = await supabase.rpc("exec_sql", { sql });

    if (error) {
      console.error("❌ Error executing fix:", error);
      process.exit(1);
    }

    console.log("✅ Thumbnail extraction function updated successfully!");

    // Now trigger a re-extraction for projects that have editorHTML but no thumbnail
    console.log("🔄 Re-extracting thumbnails for projects with editorHTML...");

    const { error: updateError } = await supabase.rpc("exec_sql", {
      sql: `
        UPDATE public.projects 
        SET extracted_thumbnail_url = NULL 
        WHERE generated_content->>'editorHTML' IS NOT NULL 
        AND extracted_thumbnail_url IS NULL;
      `,
    });

    if (updateError) {
      console.error(
        "❌ Error triggering thumbnail re-extraction:",
        updateError
      );
    } else {
      console.log("✅ Triggered thumbnail re-extraction for affected projects");
    }

    console.log("🎉 Thumbnail extraction fix completed!");
    console.log(
      "💡 Projects with editorHTML content should now have their thumbnails restored"
    );
  } catch (error) {
    console.error("❌ Error during fix:", error);
    process.exit(1);
  }
}

// Run the fix
fixThumbnailExtraction().finally(() => {
  console.log("🏁 Fix script finished");
  process.exit(0);
});
