/**
 * <PERSON><PERSON><PERSON> to run project thumbnail migration
 */

import dotenv from "dotenv";
import { createClient } from "@supabase/supabase-js";
import { extractProjectThumbnail } from "../src/utils/projectThumbnails.js";

// Load environment variables
dotenv.config();

// Supabase client setup
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("Missing required environment variables");
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { persistSession: false },
});

/**
 * Extracts and stores thumbnails for all existing projects
 */
async function migrateProjectThumbnails() {
  try {
    console.log("Starting thumbnail migration process...");

    // Get all projects with content but without extracted thumbnails
    const { data: projects, error } = await supabase
      .from("projects")
      .select("id, title, generated_content")
      .is("deleted_at", null)
      .is("extracted_thumbnail_url", null)
      .not("generated_content", "is", null);

    if (error) {
      console.error("Failed to fetch projects for thumbnail migration:", error);
      return;
    }

    console.log(`Found ${projects?.length || 0} projects to process...`);

    let updated = 0;
    let errors = 0;

    // Process each project
    for (const project of projects || []) {
      try {
        // Extract thumbnail using our utility function
        const thumbnailUrl = extractProjectThumbnail(project);

        if (thumbnailUrl) {
          console.log(
            `Extracted thumbnail for project ${project.id} (${project.title}): ${thumbnailUrl}`
          );

          // Update the project with the extracted thumbnail
          const { error: updateError } = await supabase
            .from("projects")
            .update({ extracted_thumbnail_url: thumbnailUrl })
            .eq("id", project.id);

          if (updateError) {
            console.error(
              `Failed to update thumbnail for project ${project.id}:`,
              updateError
            );
            errors++;
          } else {
            updated++;
          }
        } else {
          console.log(
            `No thumbnail found for project ${project.id} (${project.title})`
          );
        }
      } catch (projectError) {
        console.error(`Error processing project ${project.id}:`, projectError);
        errors++;
      }
    }

    console.log("Thumbnail migration completed:");
    console.log(`- Processed: ${projects?.length || 0} projects`);
    console.log(`- Updated: ${updated} projects`);
    console.log(`- Errors: ${errors} projects`);
  } catch (error) {
    console.error("Error during thumbnail migration:", error);
  }
}

// Run the migration
migrateProjectThumbnails().finally(() => {
  console.log("Migration script finished");
  process.exit(0);
});
