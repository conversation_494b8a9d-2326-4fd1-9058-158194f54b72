/**
 * Test script to verify template isolation between documents
 * Run this in browser console to test the fix
 */

// Test template isolation
function testTemplateIsolation() {
  console.log('🧪 Testing Template Isolation Between Documents');
  console.log('='.repeat(50));
  
  // Simulate Document A with template
  const documentA = 'doc-a-123';
  const documentB = 'doc-b-456';
  
  const testTemplate = {
    id: 'test-template-001',
    name: 'Test Template',
    category: 'test'
  };
  
  console.log('1️⃣ Setting up Document A with template...');
  
  // Save template for Document A
  sessionStorage.setItem(`selectedTemplate_${documentA}`, JSON.stringify(testTemplate));
  localStorage.setItem('lastSelectedTemplate', JSON.stringify(testTemplate));
  
  console.log('✅ Document A template saved:', {
    sessionKey: `selectedTemplate_${documentA}`,
    hasSessionTemplate: !!sessionStorage.getItem(`selectedTemplate_${documentA}`),
    hasLocalTemplate: !!localStorage.getItem('lastSelectedTemplate')
  });
  
  console.log('\n2️⃣ Testing Document B (should have NO template)...');
  
  // Test Document B loading logic (simulating our fixed logic)
  const documentBTemplate = sessionStorage.getItem(`selectedTemplate_${documentB}`);
  
  console.log('Document B template check:', {
    sessionKey: `selectedTemplate_${documentB}`,
    hasDocumentSpecificTemplate: !!documentBTemplate,
    shouldShowTemplate: !!documentBTemplate // This should be false
  });
  
  if (documentBTemplate) {
    console.log('❌ BUG: Document B incorrectly has template:', JSON.parse(documentBTemplate));
  } else {
    console.log('✅ CORRECT: Document B has no template (as expected)');
  }
  
  console.log('\n3️⃣ Testing localStorage isolation...');
  
  // Test that localStorage doesn't bleed into document-specific loading
  const localTemplate = localStorage.getItem('lastSelectedTemplate');
  console.log('LocalStorage template exists:', !!localTemplate);
  console.log('Should NOT be used for Document B:', true);
  
  console.log('\n4️⃣ Cleanup test data...');
  
  // Clean up test data
  sessionStorage.removeItem(`selectedTemplate_${documentA}`);
  sessionStorage.removeItem(`selectedTemplate_${documentB}`);
  localStorage.removeItem('lastSelectedTemplate');
  
  console.log('✅ Test data cleaned up');
  
  console.log('\n📊 TEST SUMMARY:');
  console.log('✅ Document A can have template');
  console.log('✅ Document B remains template-free');
  console.log('✅ No cross-document template bleeding');
  
  return {
    documentAHadTemplate: true,
    documentBHadTemplate: !!documentBTemplate,
    isolationWorking: !documentBTemplate
  };
}

// Test current page template loading
function testCurrentPageTemplate() {
  console.log('🔍 Testing Current Page Template Loading');
  console.log('='.repeat(40));
  
  // Extract document ID from current URL
  const currentPath = window.location.pathname;
  const documentId = currentPath.split('/')[2]; // Assumes /document-editor/{id}/publish format
  
  console.log('Current page info:', {
    path: currentPath,
    documentId: documentId,
    isPublishPage: currentPath.includes('/publish')
  });
  
  if (documentId) {
    // Check current document's template
    const sessionTemplate = sessionStorage.getItem(`selectedTemplate_${documentId}`);
    const localTemplate = localStorage.getItem('lastSelectedTemplate');
    
    console.log('Template storage check:', {
      documentId,
      hasDocumentSpecificTemplate: !!sessionTemplate,
      hasLocalTemplate: !!localTemplate,
      shouldUseTemplate: !!sessionTemplate // Only use if document-specific
    });
    
    if (sessionTemplate) {
      const template = JSON.parse(sessionTemplate);
      console.log('✅ Document has template:', {
        id: template.id,
        name: template.name
      });
    } else {
      console.log('✅ Document has no template (correct for new documents)');
    }
    
    if (localTemplate && !sessionTemplate) {
      console.log('⚠️ LocalStorage template exists but should NOT be used for this document');
    }
    
  } else {
    console.log('❌ Could not extract document ID from URL');
  }
}

// Clear all template data (for testing)
function clearAllTemplateData() {
  console.log('🧹 Clearing all template data...');
  
  // Clear all sessionStorage template entries
  const sessionKeys = Object.keys(sessionStorage);
  const templateKeys = sessionKeys.filter(key => key.startsWith('selectedTemplate_'));
  
  templateKeys.forEach(key => {
    sessionStorage.removeItem(key);
    console.log('Removed sessionStorage:', key);
  });
  
  // Clear localStorage template
  localStorage.removeItem('lastSelectedTemplate');
  console.log('Removed localStorage: lastSelectedTemplate');
  
  console.log('✅ All template data cleared');
}

// Export functions for browser console use
if (typeof window !== 'undefined') {
  window.testTemplateIsolation = testTemplateIsolation;
  window.testCurrentPageTemplate = testCurrentPageTemplate;
  window.clearAllTemplateData = clearAllTemplateData;
  
  console.log('🔧 Template isolation test tools loaded!');
  console.log('Available functions:');
  console.log('- window.testTemplateIsolation() - Test cross-document isolation');
  console.log('- window.testCurrentPageTemplate() - Check current page template');
  console.log('- window.clearAllTemplateData() - Clear all template data');
}

export { testTemplateIsolation, testCurrentPageTemplate, clearAllTemplateData };
