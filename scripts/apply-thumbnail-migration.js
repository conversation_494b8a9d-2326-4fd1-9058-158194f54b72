/**
 * <PERSON><PERSON><PERSON> to apply the extracted thumbnail migration
 */

import fs from "fs";
import path from "path";
import { createClient } from "@supabase/supabase-js";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Connect to Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY; // Needs service key for migrations

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("Missing required environment variables");
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { persistSession: false },
});

async function runMigration() {
  try {
    console.log("Starting extracted thumbnail migration...");

    // Read migration SQL file
    const sqlFilePath = path.join(
      __dirname,
      "../database/add-extracted-thumbnail.sql"
    );
    const sql = fs.readFileSync(sqlFilePath, "utf8");

    // Split SQL into statements
    const statements = sql
      .replace(/--.*$/gm, "") // Remove comments
      .split(";")
      .filter((statement) => statement.trim().length > 0);

    console.log(`Found ${statements.length} SQL statements to execute`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`Executing statement ${i + 1}/${statements.length}...`);

      const { data, error } = await supabase.rpc("exec_sql", {
        sql: statement,
      });

      if (error) {
        console.error(`Error executing statement ${i + 1}:`, error);
        console.error("Statement:", statement);
      } else {
        console.log(`Statement ${i + 1} executed successfully`);
      }
    }

    console.log("Migration complete!");
  } catch (error) {
    console.error("Error running migration:", error);
    process.exit(1);
  }
}

runMigration();
