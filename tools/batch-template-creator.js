#!/usr/bin/env node

/**
 * Batch Template Creator for DocForge AI
 * Processes multiple cover images at once
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createClient } from '@supabase/supabase-js';
import sizeOf from 'image-size';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env') });

// Initialize Supabase client with service role key
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY
);

// Template configurations
const TEMPLATE_CONFIGS = [
  {
    imagePath: './covers/business/digital-marketing.png',
    name: 'Digital Marketing',
    category: 'Business',
    description: 'Modern digital marketing design perfect for business presentations and marketing materials',
    tags: ['digital', 'marketing', 'business', 'modern', 'professional'],
    layout: 'custom'
  },
  {
    imagePath: './covers/love/with-love-forever.png',
    name: 'With Love Forever',
    category: 'Personal',
    description: 'Romantic and elegant design perfect for personal documents and love-themed content',
    tags: ['love', 'personal', 'romantic', 'elegant', 'wedding'],
    layout: 'custom'
  }
  // Add more templates here as needed
];

// Default text overlay configuration
const DEFAULT_TEXT_OVERLAYS = {
  overlays: [
    {
      id: "title",
      type: "text",
      placeholder: "{{title}}",
      position: { x: 50, y: 200, width: 400, height: 100 },
      styling: {
        fontSize: 40,
        fontFamily: "Arial",
        fontWeight: "bold",
        color: "#000000",
        textAlign: "center",
        lineHeight: 1.2,
        maxLines: 2,
        overflow: "ellipsis",
        verticalAlign: "center"
      }
    },
    {
      id: "author",
      type: "text",
      placeholder: "by {{author}}",
      position: { x: 50, y: 320, width: 400, height: 50 },
      styling: {
        fontSize: 22,
        fontFamily: "Arial",
        fontWeight: "normal",
        color: "#666666",
        textAlign: "center",
        lineHeight: 1.2,
        maxLines: 1,
        overflow: "ellipsis",
        verticalAlign: "center"
      }
    }
  ]
};

/**
 * Create a single template
 */
async function createTemplate(config) {
  const { imagePath, name, category, description, tags } = config;
  
  try {
    console.log(`\n📋 Creating template: ${name}`);
    
    // Check if image exists
    if (!fs.existsSync(imagePath)) {
      throw new Error(`Image file not found: ${imagePath}`);
    }
    
    // Get image dimensions
    const buffer = fs.readFileSync(imagePath);
    const dimensions = sizeOf(buffer);
    console.log(`   📏 Dimensions: ${dimensions.width}x${dimensions.height}`);
    
    // Generate template ID
    const templateId = `${category.toLowerCase()}-${name.toLowerCase().replace(/[^a-z0-9]/g, '-')}-${Date.now().toString(36)}`;
    console.log(`   🆔 ID: ${templateId}`);
    
    // Upload image to storage
    console.log(`   📤 Uploading image...`);
    const fileName = `${templateId}.png`;
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('template-backgrounds')
      .upload(fileName, buffer, {
        contentType: 'image/png',
        upsert: true
      });
    
    if (uploadError) {
      throw uploadError;
    }
    
    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('template-backgrounds')
      .getPublicUrl(fileName);
    
    // Create template data
    const templateData = {
      id: templateId,
      name,
      description,
      category,
      tags,
      background_image_url: publicUrl,
      background_image_width: dimensions.width,
      background_image_height: dimensions.height,
      text_overlays: DEFAULT_TEXT_OVERLAYS,
      supported_formats: ['pdf', 'png', 'jpg'],
      is_premium: false,
      status: 'active'
    };
    
    // Insert into database
    const { data: createdTemplate, error: dbError } = await supabase
      .from('cover_templates')
      .insert([templateData])
      .select()
      .single();
    
    if (dbError) {
      throw dbError;
    }
    
    console.log(`   ✅ Template created successfully!`);
    return createdTemplate;
    
  } catch (error) {
    console.error(`   ❌ Failed to create template "${name}":`, error.message);
    return null;
  }
}

/**
 * Process all templates
 */
async function processAllTemplates() {
  console.log('🚀 Batch Template Creator Starting...');
  console.log(`📋 Processing ${TEMPLATE_CONFIGS.length} templates\n`);
  
  const results = {
    successful: [],
    failed: []
  };
  
  for (const config of TEMPLATE_CONFIGS) {
    const result = await createTemplate(config);
    
    if (result) {
      results.successful.push(result);
    } else {
      results.failed.push(config.name);
    }
    
    // Small delay between uploads
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Summary
  console.log('\n🎉 Batch Processing Complete!');
  console.log(`   ✅ Successful: ${results.successful.length}`);
  console.log(`   ❌ Failed: ${results.failed.length}`);
  
  if (results.successful.length > 0) {
    console.log('\n📋 Created Templates:');
    results.successful.forEach(template => {
      console.log(`   • ${template.name} (${template.category}) - ID: ${template.id}`);
    });
  }
  
  if (results.failed.length > 0) {
    console.log('\n❌ Failed Templates:');
    results.failed.forEach(name => {
      console.log(`   • ${name}`);
    });
  }
}

// Run the batch processor
processAllTemplates();
