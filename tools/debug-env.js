#!/usr/bin/env node

/**
 * Debug environment variables and JWT tokens
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env') });

console.log('🔧 Environment Variable Debug\n');

// Check all Supabase-related environment variables
console.log('1. Environment Variables:');
console.log('   VITE_SUPABASE_URL:', process.env.VITE_SUPABASE_URL ? '✅ Set' : '❌ Missing');
console.log('   VITE_SUPABASE_ANON_KEY:', process.env.VITE_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing');
console.log('   SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '✅ Set' : '❌ Missing');

// Debug the service role key format
console.log('\n2. Service Role Key Analysis:');
if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
  const key = process.env.SUPABASE_SERVICE_ROLE_KEY.trim();
  console.log('   Length:', key.length);
  console.log('   Starts with:', key.substring(0, 30) + '...');
  console.log('   Ends with:', '...' + key.substring(key.length - 30));
  
  // Check for common JWT format
  const parts = key.split('.');
  console.log('   JWT parts:', parts.length, '(should be 3)');
  
  if (parts.length === 3) {
    try {
      // Decode the header (first part)
      const header = JSON.parse(atob(parts[0]));
      console.log('   JWT Header:', header);
      
      // Decode the payload (second part)
      const payload = JSON.parse(atob(parts[1]));
      console.log('   JWT Role:', payload.role);
      console.log('   JWT Issuer:', payload.iss);
    } catch (error) {
      console.log('   ❌ JWT decode error:', error.message);
    }
  }
} else {
  console.log('   ❌ Service role key not found');
}

// Check anon key for comparison
console.log('\n3. Anon Key Analysis:');
if (process.env.VITE_SUPABASE_ANON_KEY) {
  const key = process.env.VITE_SUPABASE_ANON_KEY.trim();
  console.log('   Length:', key.length);
  
  const parts = key.split('.');
  console.log('   JWT parts:', parts.length, '(should be 3)');
  
  if (parts.length === 3) {
    try {
      const payload = JSON.parse(atob(parts[1]));
      console.log('   JWT Role:', payload.role);
      console.log('   JWT Issuer:', payload.iss);
    } catch (error) {
      console.log('   ❌ JWT decode error:', error.message);
    }
  }
}

// Test Supabase client creation
console.log('\n4. Supabase Client Test:');
try {
  const { createClient } = await import('@supabase/supabase-js');
  
  // Test with service role key
  if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
    console.log('   Testing service role client...');
    const serviceClient = createClient(
      process.env.VITE_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY.trim()
    );
    console.log('   ✅ Service role client created');
  }
  
  // Test with anon key
  console.log('   Testing anon client...');
  const anonClient = createClient(
    process.env.VITE_SUPABASE_URL,
    process.env.VITE_SUPABASE_ANON_KEY.trim()
  );
  console.log('   ✅ Anon client created');
  
} catch (error) {
  console.log('   ❌ Client creation error:', error.message);
}

console.log('\n🏁 Debug completed!');
