#!/usr/bin/env node

/**
 * Insert Plain Template into Database
 * Uses SQL to directly insert the Plain template that replicates the original export format
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createClient } from '@supabase/supabase-js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: path.join(__dirname, '../.env') });

// Initialize Supabase client
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

/**
 * Plain template configuration matching original export format
 */
const PLAIN_TEMPLATE_DATA = {
  id: 'plain-template',
  name: 'Plain',
  description: 'Simple, clean document cover matching the original export format - perfect for professional documents that need a minimal, unadorned appearance',
  category: 'minimal',
  tags: ['plain', 'simple', 'minimal', 'default', 'original', 'clean'],
  background_image_url: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
  background_image_width: 1200,
  background_image_height: 1600,
  text_overlays: {
    overlays: [
      {
        id: "title",
        type: "text",
        placeholder: "{{title}}",
        position: { x: 100, y: 400, width: 1000, height: 120 },
        styling: {
          fontSize: 60,
          fontFamily: "Georgia",
          fontWeight: "bold",
          color: "#2c3e50",
          textAlign: "center",
          lineHeight: 1.2,
          maxLines: 3,
          overflow: "ellipsis",
          verticalAlign: "center"
        }
      },
      {
        id: "author",
        type: "text", 
        placeholder: "by {{author}}",
        position: { x: 100, y: 580, width: 1000, height: 60 },
        styling: {
          fontSize: 28,
          fontFamily: "Georgia",
          fontWeight: "normal",
          color: "#7f8c8d",
          textAlign: "center",
          lineHeight: 1.2,
          maxLines: 1,
          overflow: "ellipsis",
          verticalAlign: "center"
        }
      },
      {
        id: "description",
        type: "text",
        placeholder: "{{description}}",
        position: { x: 200, y: 680, width: 800, height: 200 },
        styling: {
          fontSize: 24,
          fontFamily: "Georgia",
          fontWeight: "normal",
          color: "#95a5a6",
          textAlign: "center",
          lineHeight: 1.6,
          maxLines: 6,
          overflow: "ellipsis",
          verticalAlign: "top",
          fontStyle: "italic"
        }
      }
    ]
  },
  supported_formats: ['pdf', 'png', 'jpg'],
  is_premium: false,
  status: 'active'
};

/**
 * Insert Plain template using direct database insert
 */
async function insertPlainTemplate() {
  try {
    console.log('🚀 Inserting Plain Template into DocForge AI database');
    console.log('📋 This template replicates the original pre-template export format');
    
    // Try to insert the template
    const { data, error } = await supabase
      .from('cover_templates')
      .upsert(PLAIN_TEMPLATE_DATA, { 
        onConflict: 'id',
        ignoreDuplicates: false 
      })
      .select();
    
    if (error) {
      console.error('❌ Database insert failed:', error);
      
      // If RLS policy blocks it, let's try a different approach
      if (error.message.includes('row-level security')) {
        console.log('🔒 Row-level security policy detected');
        console.log('📋 Template configuration ready for manual insertion:');
        console.log('\n--- TEMPLATE DATA ---');
        console.log(JSON.stringify(PLAIN_TEMPLATE_DATA, null, 2));
        console.log('\n--- SQL INSERT STATEMENT ---');
        console.log(`
INSERT INTO public.cover_templates (
    id, name, description, category, tags, background_image_url,
    background_image_width, background_image_height, text_overlays,
    supported_formats, is_premium, status
) VALUES (
    '${PLAIN_TEMPLATE_DATA.id}',
    '${PLAIN_TEMPLATE_DATA.name}',
    '${PLAIN_TEMPLATE_DATA.description}',
    '${PLAIN_TEMPLATE_DATA.category}',
    ARRAY[${PLAIN_TEMPLATE_DATA.tags.map(tag => `'${tag}'`).join(', ')}],
    '${PLAIN_TEMPLATE_DATA.background_image_url}',
    ${PLAIN_TEMPLATE_DATA.background_image_width},
    ${PLAIN_TEMPLATE_DATA.background_image_height},
    '${JSON.stringify(PLAIN_TEMPLATE_DATA.text_overlays)}'::jsonb,
    ARRAY[${PLAIN_TEMPLATE_DATA.supported_formats.map(format => `'${format}'`).join(', ')}],
    ${PLAIN_TEMPLATE_DATA.is_premium},
    '${PLAIN_TEMPLATE_DATA.status}'
) ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    text_overlays = EXCLUDED.text_overlays,
    updated_at = NOW();
        `);
        
        return { success: false, error: 'RLS_POLICY', templateData: PLAIN_TEMPLATE_DATA };
      }
      
      throw error;
    }
    
    console.log('\n🎉 Plain Template Created Successfully!');
    console.log('📊 Template Details:');
    console.log(`   ID: ${data[0].id}`);
    console.log(`   Name: ${data[0].name}`);
    console.log(`   Category: ${data[0].category}`);
    console.log(`   Tags: ${data[0].tags.join(', ')}`);
    console.log(`   Overlays: ${data[0].text_overlays.overlays.length}`);
    console.log(`   Status: ${data[0].status}`);
    
    console.log('\n✅ Users can now select the "Plain" template to get the original export format!');
    
    return { success: true, template: data[0] };
    
  } catch (error) {
    console.error('\n❌ Failed to insert Plain template:', error);
    return { success: false, error: error.message };
  }
}

// Run the script
console.log('🚀 Starting Plain Template insertion...');
insertPlainTemplate().then(result => {
  if (result.success) {
    console.log('\n🎊 Template insertion completed successfully!');
    process.exit(0);
  } else {
    console.log('\n⚠️  Template insertion requires manual intervention');
    process.exit(0); // Exit successfully since we provided the SQL
  }
}).catch(error => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
