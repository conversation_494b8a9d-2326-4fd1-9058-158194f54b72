#!/usr/bin/env node

/**
 * Plain Template Creator for DocForge AI
 * Creates a "Plain" template that replicates the original pre-template export format
 * 
 * Usage:
 * node tools/create-plain-template.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createClient } from '@supabase/supabase-js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: path.join(__dirname, '../.env') });

console.log('🔧 Environment check:');
console.log('   SUPABASE_URL:', process.env.VITE_SUPABASE_URL ? 'Set' : 'Missing');
console.log('   SUPABASE_KEY:', process.env.VITE_SUPABASE_ANON_KEY ? 'Set' : 'Missing');

// Initialize Supabase client
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

/**
 * Plain template text overlay configuration
 * Matches the original export system styling exactly
 */
const PLAIN_TEXT_OVERLAYS = {
  overlays: [
    {
      id: "title",
      type: "text",
      placeholder: "{{title}}",
      position: { x: 100, y: 400, width: 1000, height: 120 },
      styling: {
        fontSize: 60,
        fontFamily: "Georgia",
        fontWeight: "bold",
        color: "#2c3e50",
        textAlign: "center",
        lineHeight: 1.2,
        maxLines: 3,
        overflow: "ellipsis",
        verticalAlign: "center"
      }
    },
    {
      id: "author",
      type: "text", 
      placeholder: "by {{author}}",
      position: { x: 100, y: 580, width: 1000, height: 60 },
      styling: {
        fontSize: 28,
        fontFamily: "Georgia",
        fontWeight: "normal",
        color: "#7f8c8d",
        textAlign: "center",
        lineHeight: 1.2,
        maxLines: 1,
        overflow: "ellipsis",
        verticalAlign: "center"
      }
    },
    {
      id: "description",
      type: "text",
      placeholder: "{{description}}",
      position: { x: 200, y: 680, width: 800, height: 200 },
      styling: {
        fontSize: 24,
        fontFamily: "Georgia",
        fontWeight: "normal",
        color: "#95a5a6",
        textAlign: "center",
        lineHeight: 1.6,
        maxLines: 6,
        overflow: "ellipsis",
        verticalAlign: "top",
        fontStyle: "italic"
      }
    }
  ]
};

/**
 * Create a white background image as base64 data URL
 * Creates a minimal 1x1 white pixel that will be stretched by the overlay service
 */
function createWhiteBackgroundDataUrl() {
  console.log(`🎨 Creating white background image data URL`);

  // Create a minimal 1x1 white pixel PNG as base64
  // This is more efficient and will be stretched by the canvas rendering
  const whitePixelBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

  return whitePixelBase64;
}

/**
 * Convert base64 data URL to buffer for upload
 */
function dataUrlToBuffer(dataUrl) {
  const base64Data = dataUrl.split(',')[1];
  return Buffer.from(base64Data, 'base64');
}

/**
 * Upload image to Supabase storage
 */
async function uploadImageToStorage(imageBuffer, templateId) {
  try {
    console.log(`📤 Uploading background image for template: ${templateId}`);

    const fileName = `${templateId}-background.png`;
    const filePath = `${fileName}`;

    // Try template-backgrounds bucket first, fallback to template-assets
    let bucket = 'template-backgrounds';
    let { data, error } = await supabase.storage
      .from(bucket)
      .upload(filePath, imageBuffer, {
        contentType: 'image/png',
        upsert: true
      });

    // If template-backgrounds doesn't exist, try template-assets
    if (error && error.message.includes('not found')) {
      console.log('📤 Trying template-assets bucket...');
      bucket = 'template-assets';
      const result = await supabase.storage
        .from(bucket)
        .upload(`template-backgrounds/${fileName}`, imageBuffer, {
          contentType: 'image/png',
          upsert: true
        });
      data = result.data;
      error = result.error;
      filePath = `template-backgrounds/${fileName}`;
    }

    if (error) {
      throw error;
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from(bucket)
      .getPublicUrl(filePath);

    console.log(`✅ Background image uploaded: ${publicUrl}`);
    return publicUrl;

  } catch (error) {
    console.error('❌ Failed to upload background image:', error);
    throw error;
  }
}

/**
 * Generate template ID
 */
function generateTemplateId() {
  return 'plain-template';
}

/**
 * Create template data structure
 */
function createTemplateData(backgroundImageUrl, dimensions) {
  return {
    id: generateTemplateId(),
    name: 'Plain',
    description: 'Simple, clean document cover matching the original export format - perfect for professional documents that need a minimal, unadorned appearance',
    category: 'minimal',
    tags: ['plain', 'simple', 'minimal', 'default', 'original', 'clean'],
    background_image_url: backgroundImageUrl,
    background_image_width: dimensions.width,
    background_image_height: dimensions.height,
    text_overlays: PLAIN_TEXT_OVERLAYS,
    supported_formats: ['pdf', 'png', 'jpg'],
    is_premium: false,
    status: 'active'
  };
}

/**
 * Insert template into database
 */
async function insertTemplate(templateData) {
  try {
    console.log(`💾 Inserting Plain template into database`);
    
    const { data, error } = await supabase
      .from('cover_templates')
      .upsert(templateData, { onConflict: 'id' })
      .select();
    
    if (error) {
      throw error;
    }
    
    console.log(`✅ Plain template created successfully:`, data[0]);
    return data[0];
    
  } catch (error) {
    console.error('❌ Failed to insert template:', error);
    throw error;
  }
}

/**
 * Main function to create the Plain template
 */
async function createPlainTemplate() {
  try {
    console.log('🚀 Creating Plain Template for DocForge AI');
    console.log('📋 This template replicates the original pre-template export format');
    
    // Step 1: Create white background image
    const dimensions = { width: 1200, height: 1600 };
    const backgroundDataUrl = createWhiteBackgroundDataUrl();

    // Step 2: Use data URL directly (skip upload for now due to RLS policy)
    const templateId = generateTemplateId();
    console.log('📋 Using data URL directly due to storage permissions');
    const backgroundImageUrl = backgroundDataUrl;
    
    // Step 3: Create template data
    const templateData = createTemplateData(backgroundImageUrl, dimensions);
    
    // Step 4: Insert into database
    const createdTemplate = await insertTemplate(templateData);
    
    console.log('\n🎉 Plain Template Creation Complete!');
    console.log('📊 Template Details:');
    console.log(`   ID: ${createdTemplate.id}`);
    console.log(`   Name: ${createdTemplate.name}`);
    console.log(`   Category: ${createdTemplate.category}`);
    console.log(`   Background: ${createdTemplate.background_image_url}`);
    console.log(`   Dimensions: ${createdTemplate.background_image_width}x${createdTemplate.background_image_height}`);
    console.log('\n✅ Users can now select the "Plain" template to get the original export format!');
    
    return createdTemplate;
    
  } catch (error) {
    console.error('\n❌ Failed to create Plain template:', error);
    process.exit(1);
  }
}

// Run the script
console.log('🚀 Starting Plain Template creation script...');
console.log('📍 Script path check:', import.meta.url);
console.log('📍 Process argv[1]:', process.argv[1]);

createPlainTemplate().catch(error => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});

export { createPlainTemplate, PLAIN_TEXT_OVERLAYS };
