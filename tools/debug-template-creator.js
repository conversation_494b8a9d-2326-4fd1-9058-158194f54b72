#!/usr/bin/env node

/**
 * Debug version of template creator to isolate issues
 */

console.log('🚀 Debug Template Creator Starting...');

// Test basic imports
try {
  console.log('📦 Testing imports...');
  
  const fs = await import('fs');
  console.log('✅ fs imported');
  
  const path = await import('path');
  console.log('✅ path imported');
  
  const { fileURLToPath } = await import('url');
  console.log('✅ url imported');
  
  const { createClient } = await import('@supabase/supabase-js');
  console.log('✅ supabase imported');
  
  const sizeOf = await import('image-size');
  console.log('✅ image-size imported');
  
  const dotenv = await import('dotenv');
  console.log('✅ dotenv imported');
  
} catch (error) {
  console.error('❌ Import error:', error);
  process.exit(1);
}

// Test environment variables
console.log('\n🔧 Testing environment...');
const dotenv = await import('dotenv');
const path = await import('path');
const { fileURLToPath } = await import('url');

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.default.config({ path: path.join(__dirname, '../.env') });

console.log('VITE_SUPABASE_URL:', process.env.VITE_SUPABASE_URL ? '✅ Set' : '❌ Missing');
console.log('VITE_SUPABASE_ANON_KEY:', process.env.VITE_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing');

// Test Supabase connection
console.log('\n🔗 Testing Supabase...');
try {
  const { createClient } = await import('@supabase/supabase-js');
  const supabase = createClient(
    process.env.VITE_SUPABASE_URL,
    process.env.VITE_SUPABASE_ANON_KEY
  );
  console.log('✅ Supabase client created');
  
  // Test a simple query
  const { data, error } = await supabase
    .from('cover_templates')
    .select('count')
    .limit(1);
    
  if (error) {
    console.log('⚠️ Supabase query error:', error.message);
  } else {
    console.log('✅ Supabase connection working');
  }
} catch (error) {
  console.error('❌ Supabase error:', error);
}

// Test image file
console.log('\n🖼️ Testing image file...');
const fs = await import('fs');
const imagePath = './covers/business/arrow.png';

console.log('Image path:', imagePath);
console.log('File exists:', fs.default.existsSync(imagePath) ? '✅ Yes' : '❌ No');

if (fs.default.existsSync(imagePath)) {
  try {
    const buffer = fs.default.readFileSync(imagePath);
    console.log('File size:', buffer.length, 'bytes');
    
    const sizeOf = await import('image-size');
    const dimensions = sizeOf.default(buffer);
    console.log('Dimensions:', `${dimensions.width}x${dimensions.height} ✅`);
  } catch (error) {
    console.error('❌ Image processing error:', error);
  }
}

// Test command line arguments
console.log('\n📋 Command line arguments:');
console.log('process.argv:', process.argv);

console.log('\n🏁 Debug completed!');
