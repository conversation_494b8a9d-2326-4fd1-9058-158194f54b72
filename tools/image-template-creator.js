#!/usr/bin/env node

/**
 * Image Template Creator for DocForge AI
 * Converts background images to Image Overlay Templates
 * 
 * Usage:
 * node tools/image-template-creator.js --image path/to/image.jpg --name "Template Name" --category "Business"
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createClient } from '@supabase/supabase-js';
import sizeOf from 'image-size';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: path.join(__dirname, '../.env') });

// Initialize Supabase client with service role key for admin operations
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY
);

/**
 * Default text overlay configurations for common template layouts
 */
const DEFAULT_TEXT_OVERLAYS = {
  // Standard book cover layout
  standard: {
    overlays: [
      {
        id: "title",
        type: "text",
        placeholder: "{{title}}",
        position: { x: 50, y: 200, width: 500, height: 80 },
        styling: {
          fontSize: 36,
          fontFamily: "Arial",
          fontWeight: "bold",
          color: "#000000",
          textAlign: "center",
          lineHeight: 1.2,
          maxLines: 2,
          overflow: "ellipsis",
          verticalAlign: "center"
        }
      },
      {
        id: "author",
        type: "text",
        placeholder: "by {{author}}",
        position: { x: 50, y: 320, width: 500, height: 40 },
        styling: {
          fontSize: 20,
          fontFamily: "Arial",
          fontWeight: "normal",
          color: "#666666",
          textAlign: "center",
          lineHeight: 1.2,
          maxLines: 1,
          overflow: "ellipsis",
          verticalAlign: "center"
        }
      },
      {
        id: "description",
        type: "text",
        placeholder: "{{description}}",
        position: { x: 50, y: 400, width: 500, height: 120 },
        styling: {
          fontSize: 14,
          fontFamily: "Arial",
          fontWeight: "normal",
          color: "#333333",
          textAlign: "center",
          lineHeight: 1.4,
          maxLines: 6,
          overflow: "ellipsis",
          verticalAlign: "top"
        }
      }
    ]
  },

  // Minimal layout with title only
  minimal: {
    overlays: [
      {
        id: "title",
        type: "text",
        placeholder: "{{title}}",
        position: { x: 100, y: 300, width: 400, height: 100 },
        styling: {
          fontSize: 42,
          fontFamily: "Arial",
          fontWeight: "bold",
          color: "#FFFFFF",
          textAlign: "center",
          lineHeight: 1.1,
          maxLines: 3,
          overflow: "ellipsis",
          verticalAlign: "center"
        }
      }
    ]
  },

  // Professional layout
  professional: {
    overlays: [
      {
        id: "title",
        type: "text",
        placeholder: "{{title}}",
        position: { x: 80, y: 150, width: 440, height: 120 },
        styling: {
          fontSize: 32,
          fontFamily: "Georgia",
          fontWeight: "bold",
          color: "#2c3e50",
          textAlign: "left",
          lineHeight: 1.3,
          maxLines: 3,
          overflow: "ellipsis",
          verticalAlign: "top"
        }
      },
      {
        id: "author",
        type: "text",
        placeholder: "{{author}}",
        position: { x: 80, y: 300, width: 440, height: 30 },
        styling: {
          fontSize: 18,
          fontFamily: "Georgia",
          fontWeight: "normal",
          color: "#7f8c8d",
          textAlign: "left",
          lineHeight: 1.2,
          maxLines: 1,
          overflow: "ellipsis",
          verticalAlign: "center"
        }
      },
      {
        id: "description",
        type: "text",
        placeholder: "{{description}}",
        position: { x: 80, y: 350, width: 440, height: 100 },
        styling: {
          fontSize: 14,
          fontFamily: "Georgia",
          fontWeight: "normal",
          color: "#34495e",
          textAlign: "left",
          lineHeight: 1.5,
          maxLines: 5,
          overflow: "ellipsis",
          verticalAlign: "top"
        }
      }
    ]
  },

  // Custom layout - basic title and author
  custom: {
    overlays: [
      {
        id: "title",
        type: "text",
        placeholder: "{{title}}",
        position: { x: 50, y: 200, width: 500, height: 100 },
        styling: {
          fontSize: 40,
          fontFamily: "Arial",
          fontWeight: "bold",
          color: "#000000",
          textAlign: "center",
          lineHeight: 1.2,
          maxLines: 2,
          overflow: "ellipsis",
          verticalAlign: "center"
        }
      },
      {
        id: "author",
        type: "text",
        placeholder: "by {{author}}",
        position: { x: 50, y: 320, width: 500, height: 50 },
        styling: {
          fontSize: 22,
          fontFamily: "Arial",
          fontWeight: "normal",
          color: "#666666",
          textAlign: "center",
          lineHeight: 1.2,
          maxLines: 1,
          overflow: "ellipsis",
          verticalAlign: "center"
        }
      }
    ]
  }
};

/**
 * Upload image to Supabase Storage
 */
async function uploadImageToStorage(imagePath, templateId) {
  try {
    console.log(`📤 Uploading image: ${imagePath}`);
    
    const imageBuffer = fs.readFileSync(imagePath);
    const fileExt = path.extname(imagePath).toLowerCase();
    const fileName = `${templateId}${fileExt}`;
    
    const { data, error } = await supabase.storage
      .from('template-backgrounds')
      .upload(fileName, imageBuffer, {
        contentType: `image/${fileExt.slice(1)}`,
        upsert: true
      });
    
    if (error) {
      throw error;
    }
    
    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('template-backgrounds')
      .getPublicUrl(fileName);
    
    console.log(`✅ Image uploaded successfully: ${publicUrl}`);
    return publicUrl;
    
  } catch (error) {
    console.error(`❌ Failed to upload image:`, error);
    throw error;
  }
}

/**
 * Get image dimensions
 */
function getImageDimensions(imagePath) {
  try {
    console.log(`📏 Getting dimensions for: ${imagePath}`);

    // Read the file as a buffer first
    const buffer = fs.readFileSync(imagePath);
    const dimensions = sizeOf(buffer);

    console.log(`✅ Image dimensions: ${dimensions.width}x${dimensions.height}`);
    return { width: dimensions.width, height: dimensions.height };
  } catch (error) {
    console.warn(`⚠️ Could not get image dimensions, using defaults:`, error.message);
    return { width: 1200, height: 1600 }; // Better default dimensions for templates
  }
}

/**
 * Create template in database
 */
async function createTemplate(templateData) {
  try {
    console.log(`📝 Creating template: ${templateData.name}`);
    
    const { data, error } = await supabase
      .from('cover_templates')
      .insert([templateData])
      .select()
      .single();
    
    if (error) {
      throw error;
    }
    
    console.log(`✅ Template created successfully: ${data.id}`);
    return data;
    
  } catch (error) {
    console.error(`❌ Failed to create template:`, error);
    throw error;
  }
}

/**
 * Generate template ID
 */
function generateTemplateId(name, category) {
  const cleanName = name.toLowerCase().replace(/[^a-z0-9]/g, '-');
  const cleanCategory = category.toLowerCase().replace(/[^a-z0-9]/g, '-');
  const timestamp = Date.now().toString(36);
  return `${cleanCategory}-${cleanName}-${timestamp}`;
}

/**
 * Main function to create template
 */
async function createImageTemplate(options) {
  const {
    imagePath,
    name,
    description = '',
    category = 'General',
    tags = [],
    layout = 'standard',
    isPremium = false
  } = options;
  
  try {
    console.log(`📁 Checking if image file exists: ${imagePath}`);

    // Validate image file exists
    if (!fs.existsSync(imagePath)) {
      throw new Error(`Image file not found: ${imagePath}`);
    }

    console.log('✅ Image file found');
    
    // Generate template ID
    const templateId = generateTemplateId(name, category);
    
    // Upload image to storage
    const backgroundImageUrl = await uploadImageToStorage(imagePath, templateId);
    
    // Get image dimensions
    const dimensions = getImageDimensions(imagePath);
    
    // Get text overlay configuration
    const textOverlays = DEFAULT_TEXT_OVERLAYS[layout] || DEFAULT_TEXT_OVERLAYS.standard;
    
    // Create template data
    const templateData = {
      id: templateId,
      name,
      description,
      category,
      tags,
      background_image_url: backgroundImageUrl,
      background_image_width: dimensions.width,
      background_image_height: dimensions.height,
      text_overlays: textOverlays,
      supported_formats: ['pdf', 'png', 'jpg'],
      is_premium: isPremium,
      status: 'active'
    };
    
    // Create template in database
    const createdTemplate = await createTemplate(templateData);
    
    console.log(`\n🎉 Template created successfully!`);
    console.log(`   ID: ${createdTemplate.id}`);
    console.log(`   Name: ${createdTemplate.name}`);
    console.log(`   Category: ${createdTemplate.category}`);
    console.log(`   Background: ${createdTemplate.background_image_url}`);
    
    return createdTemplate;
    
  } catch (error) {
    console.error(`\n❌ Failed to create template:`, error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

/**
 * Parse command line arguments
 */
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {};
  
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i].replace('--', '');
    const value = args[i + 1];
    
    switch (key) {
      case 'image':
        options.imagePath = value;
        break;
      case 'name':
        options.name = value;
        break;
      case 'description':
        options.description = value;
        break;
      case 'category':
        options.category = value;
        break;
      case 'tags':
        options.tags = value.split(',').map(tag => tag.trim());
        break;
      case 'layout':
        options.layout = value;
        break;
      case 'premium':
        options.isPremium = value === 'true';
        break;
    }
  }
  
  return options;
}

/**
 * Show usage information
 */
function showUsage() {
  console.log(`
Image Template Creator for DocForge AI

Usage:
  node tools/image-template-creator.js [options]

Options:
  --image <path>        Path to background image (required)
  --name <name>         Template name (required)
  --description <desc>  Template description
  --category <cat>      Template category (default: General)
  --tags <tags>         Comma-separated tags
  --layout <layout>     Text layout: standard, minimal, professional (default: standard)
  --premium <bool>      Is premium template (default: false)

Examples:
  node tools/image-template-creator.js --image ./cover.jpg --name "Modern Business" --category "Business"
  node tools/image-template-creator.js --image ./book.png --name "Classic Novel" --category "Literature" --layout "professional"
`);
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
  (async () => {
    console.log('🚀 Starting Image Template Creator...');

    const options = parseArgs();
    console.log('📋 Parsed options:', options);

    if (!options.imagePath || !options.name) {
      console.log('❌ Missing required parameters');
      showUsage();
      process.exit(1);
    }

    console.log('✅ Required parameters provided, creating template...');

    // Wrap in try-catch to catch any unhandled errors
    try {
      await createImageTemplate(options);
    } catch (error) {
      console.error('💥 Unhandled error in main execution:', error);
      process.exit(1);
    }
  })();
}
