#!/usr/bin/env node

/**
 * Simple test script to debug the template creator tool
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createClient } from '@supabase/supabase-js';
import sizeOf from 'image-size';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env') });

console.log('🔧 Testing Template Creator Tool...\n');

// Test 1: Check environment variables
console.log('1. Environment Variables:');
console.log('   VITE_SUPABASE_URL:', process.env.VITE_SUPABASE_URL ? '✅ Set' : '❌ Missing');
console.log('   VITE_SUPABASE_ANON_KEY:', process.env.VITE_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing');

// Test 2: Check Supabase connection
console.log('\n2. Supabase Connection:');
try {
  const supabase = createClient(
    process.env.VITE_SUPABASE_URL,
    process.env.VITE_SUPABASE_ANON_KEY
  );
  console.log('   ✅ Supabase client created successfully');
} catch (error) {
  console.log('   ❌ Failed to create Supabase client:', error.message);
}

// Test 3: Check image file
const imagePath = './covers/business/arrow.png';
console.log('\n3. Image File Check:');
console.log('   Path:', imagePath);
console.log('   Exists:', fs.existsSync(imagePath) ? '✅ Yes' : '❌ No');

if (fs.existsSync(imagePath)) {
  try {
    const buffer = fs.readFileSync(imagePath);
    const dimensions = sizeOf(buffer);
    console.log('   Dimensions:', `${dimensions.width}x${dimensions.height} ✅`);
  } catch (error) {
    console.log('   Dimensions: ❌ Error:', error.message);
  }
}

// Test 4: List available images
console.log('\n4. Available Images:');
try {
  const coversDir = './covers';
  if (fs.existsSync(coversDir)) {
    const walkDir = (dir, level = 0) => {
      const files = fs.readdirSync(dir);
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        const indent = '   ' + '  '.repeat(level);
        
        if (stat.isDirectory()) {
          console.log(`${indent}📁 ${file}/`);
          walkDir(filePath, level + 1);
        } else if (file.match(/\.(png|jpg|jpeg|gif|webp)$/i)) {
          console.log(`${indent}🖼️  ${file}`);
        }
      });
    };
    walkDir(coversDir);
  } else {
    console.log('   ❌ Covers directory not found');
  }
} catch (error) {
  console.log('   ❌ Error listing files:', error.message);
}

console.log('\n🏁 Test completed!');
