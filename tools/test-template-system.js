#!/usr/bin/env node

/**
 * Test Template System
 * Verify the current template system and test the Plain template structure
 */

import path from 'path';
import { fileURLToPath } from 'url';
import { createClient } from '@supabase/supabase-js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: path.join(__dirname, '../.env') });

// Initialize Supabase client
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

/**
 * Test fetching existing templates
 */
async function testFetchTemplates() {
  try {
    console.log('📋 Testing template fetch...');
    
    const { data: templates, error } = await supabase
      .from('cover_templates')
      .select('id, name, category, tags, status')
      .eq('status', 'active')
      .limit(5);
    
    if (error) {
      console.error('❌ Failed to fetch templates:', error);
      return { success: false, error };
    }
    
    console.log(`✅ Successfully fetched ${templates.length} templates:`);
    templates.forEach(template => {
      console.log(`   - ${template.name} (${template.category}) [${template.id}]`);
    });
    
    return { success: true, templates };
    
  } catch (error) {
    console.error('❌ Template fetch test failed:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test if Plain template already exists
 */
async function testPlainTemplateExists() {
  try {
    console.log('\n🔍 Checking if Plain template already exists...');
    
    const { data: template, error } = await supabase
      .from('cover_templates')
      .select('*')
      .eq('id', 'plain-template')
      .single();
    
    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('❌ Error checking Plain template:', error);
      return { success: false, error };
    }
    
    if (template) {
      console.log('✅ Plain template already exists!');
      console.log(`   Name: ${template.name}`);
      console.log(`   Category: ${template.category}`);
      console.log(`   Status: ${template.status}`);
      console.log(`   Overlays: ${template.text_overlays?.overlays?.length || 0}`);
      return { success: true, exists: true, template };
    } else {
      console.log('📝 Plain template does not exist yet');
      return { success: true, exists: false };
    }
    
  } catch (error) {
    console.error('❌ Plain template check failed:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test template structure validation
 */
function testTemplateStructure() {
  console.log('\n🧪 Testing Plain template structure...');
  
  const plainTemplate = {
    id: 'plain-template',
    name: 'Plain',
    description: 'Simple, clean document cover matching the original export format',
    category: 'minimal',
    tags: ['plain', 'simple', 'minimal', 'default', 'original', 'clean'],
    background_image_url: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
    background_image_width: 1200,
    background_image_height: 1600,
    text_overlays: {
      overlays: [
        {
          id: "title",
          type: "text",
          placeholder: "{{title}}",
          position: { x: 100, y: 400, width: 1000, height: 120 },
          styling: {
            fontSize: 60,
            fontFamily: "Georgia",
            fontWeight: "bold",
            color: "#2c3e50",
            textAlign: "center",
            lineHeight: 1.2,
            maxLines: 3,
            overflow: "ellipsis",
            verticalAlign: "center"
          }
        }
      ]
    },
    supported_formats: ['pdf', 'png', 'jpg'],
    is_premium: false,
    status: 'active'
  };
  
  // Validate required fields
  const requiredFields = ['id', 'name', 'category', 'background_image_url', 'background_image_width', 'background_image_height', 'text_overlays'];
  const missingFields = requiredFields.filter(field => !plainTemplate[field]);
  
  if (missingFields.length > 0) {
    console.log(`❌ Missing required fields: ${missingFields.join(', ')}`);
    return false;
  }
  
  // Validate text overlays structure
  if (!plainTemplate.text_overlays.overlays || !Array.isArray(plainTemplate.text_overlays.overlays)) {
    console.log('❌ Invalid text_overlays structure');
    return false;
  }
  
  // Validate overlay fields
  for (const overlay of plainTemplate.text_overlays.overlays) {
    const requiredOverlayFields = ['id', 'type', 'placeholder', 'position', 'styling'];
    const missingOverlayFields = requiredOverlayFields.filter(field => !overlay[field]);
    
    if (missingOverlayFields.length > 0) {
      console.log(`❌ Overlay ${overlay.id} missing fields: ${missingOverlayFields.join(', ')}`);
      return false;
    }
  }
  
  console.log('✅ Plain template structure is valid');
  console.log(`   Required fields: ${requiredFields.length}/${requiredFields.length} ✓`);
  console.log(`   Text overlays: ${plainTemplate.text_overlays.overlays.length} configured`);
  console.log(`   Background: ${plainTemplate.background_image_width}x${plainTemplate.background_image_height}`);
  
  return true;
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🧪 DocForge AI Template System Test');
  console.log('=====================================\n');
  
  // Test 1: Fetch existing templates
  const fetchResult = await testFetchTemplates();
  
  // Test 2: Check if Plain template exists
  const plainResult = await testPlainTemplateExists();
  
  // Test 3: Validate template structure
  const structureValid = testTemplateStructure();
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log(`   Template fetch: ${fetchResult.success ? '✅' : '❌'}`);
  console.log(`   Plain template exists: ${plainResult.success && plainResult.exists ? '✅' : '📝'}`);
  console.log(`   Template structure: ${structureValid ? '✅' : '❌'}`);
  
  if (fetchResult.success && structureValid) {
    console.log('\n🎉 Template system is working correctly!');
    
    if (!plainResult.exists) {
      console.log('\n📋 Next Steps:');
      console.log('   1. Run the SQL script in database/create-plain-template.sql');
      console.log('   2. Or manually insert the template using Supabase dashboard');
      console.log('   3. The template configuration is ready and validated');
    }
  } else {
    console.log('\n⚠️  Issues detected that need to be resolved');
  }
}

// Run the tests
runTests().catch(error => {
  console.error('💥 Test suite failed:', error);
  process.exit(1);
});
