/**
 * Test script to validate content conversion and pagination
 * This tests the complete flow from generatedContent to paginated preview
 */

// Mock DOM environment for Node.js testing
const { JSDOM } = require('jsdom');
const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
global.document = dom.window.document;
global.window = dom.window;

// Mock the content conversion function
function mockConvertAIContentToHTML(generatedContent, imageSuggestions = null, isReadOnly = false) {
  if (!generatedContent) {
    return "<p></p>";
  }

  // If user has made edits, return their HTML directly
  if (generatedContent.editorHTML) {
    return generatedContent.editorHTML;
  }

  let fullHTML = "";

  try {
    // Add introduction
    if (generatedContent.introduction?.content) {
      fullHTML += `<div class="introduction">${generatedContent.introduction.content}</div>`;
    }

    // Add chapters
    if (generatedContent.chapters && Array.isArray(generatedContent.chapters)) {
      generatedContent.chapters.forEach((chapter, index) => {
        if (chapter.content) {
          const chapterNumber = chapter.number || index + 1;
          const chapterTitle = chapter.title;

          if (chapterTitle) {
            fullHTML += `<h2>Chapter ${chapterNumber}: ${chapterTitle}</h2>`;
          }
          fullHTML += `<div class="chapter-content">${chapter.content}</div>`;
        }
      });
    }

    // Add conclusion
    if (generatedContent.conclusion?.content) {
      fullHTML += `<div class="conclusion">${generatedContent.conclusion.content}</div>`;
    }

    // Ensure we have content
    if (!fullHTML.trim()) {
      console.warn("No content generated, using placeholder");
      fullHTML = "<p></p>";
    }

    return fullHTML;
  } catch (error) {
    console.error('Error converting content:', error);
    return "<p>Error converting content</p>";
  }
}

// Mock pagination function
function mockPaginateContentForPreview(html, options = {}) {
  const { wordsPerPage = 500 } = options;
  
  if (!html || html.trim() === '') {
    return ['<div class="empty-content">No content to display</div>'];
  }

  // Simple word-based pagination for testing
  const words = html.split(/\s+/);
  const pages = [];
  
  for (let i = 0; i < words.length; i += wordsPerPage) {
    const pageWords = words.slice(i, i + wordsPerPage);
    pages.push(pageWords.join(' '));
  }
  
  return pages.length > 0 ? pages : [html];
}

// Test different generatedContent structures
function testContentConversionAndPagination() {
  console.log('🧪 Testing Content Conversion and Pagination');
  console.log('=============================================\n');

  // Test 1: Complete generatedContent structure
  console.log('Test 1: Complete Generated Content Structure');
  const completeContent = {
    title: "Test Document",
    introduction: {
      content: "This is the introduction section with substantial content that explains the purpose and scope of the document. It provides context and sets expectations for the reader."
    },
    chapters: [
      {
        number: 1,
        title: "First Chapter",
        content: "This is the content of the first chapter. It contains detailed information about the topic, including examples, explanations, and supporting details that make up the bulk of the document content."
      },
      {
        number: 2,
        title: "Second Chapter", 
        content: "This is the content of the second chapter. It builds upon the previous chapter and introduces new concepts, methodologies, and insights that are crucial for understanding the subject matter."
      },
      {
        number: 3,
        title: "Third Chapter",
        content: "This is the content of the third chapter. It provides advanced topics, case studies, and practical applications that demonstrate the real-world relevance of the concepts discussed."
      }
    ],
    conclusion: {
      content: "This is the conclusion section that summarizes the key points, provides final thoughts, and suggests next steps or further reading for interested readers."
    }
  };

  const completeHTML = mockConvertAIContentToHTML(completeContent);
  console.log(`   HTML Length: ${completeHTML.length}`);
  console.log(`   HTML Preview: "${completeHTML.substring(0, 100)}..."`);
  
  const completePages = mockPaginateContentForPreview(completeHTML, { wordsPerPage: 100 });
  console.log(`   Pages Generated: ${completePages.length}`);
  console.log(`   Page Lengths: [${completePages.map(p => p.length).join(', ')}]`);
  console.log('');

  // Test 2: Minimal generatedContent structure
  console.log('Test 2: Minimal Generated Content Structure');
  const minimalContent = {
    chapters: [
      {
        content: "This is a single chapter with minimal content."
      }
    ]
  };

  const minimalHTML = mockConvertAIContentToHTML(minimalContent);
  console.log(`   HTML Length: ${minimalHTML.length}`);
  console.log(`   HTML Preview: "${minimalHTML}"`);
  
  const minimalPages = mockPaginateContentForPreview(minimalHTML);
  console.log(`   Pages Generated: ${minimalPages.length}`);
  console.log('');

  // Test 3: Empty generatedContent
  console.log('Test 3: Empty Generated Content');
  const emptyContent = {};

  const emptyHTML = mockConvertAIContentToHTML(emptyContent);
  console.log(`   HTML Length: ${emptyHTML.length}`);
  console.log(`   HTML Content: "${emptyHTML}"`);
  
  const emptyPages = mockPaginateContentForPreview(emptyHTML);
  console.log(`   Pages Generated: ${emptyPages.length}`);
  console.log(`   Page Content: "${emptyPages[0]}"`);
  console.log('');

  // Test 4: generatedContent with editorHTML (user edits)
  console.log('Test 4: Generated Content with Editor HTML');
  const editedContent = {
    title: "Original Title",
    chapters: [{ content: "Original content" }],
    editorHTML: "<h1>User Edited Title</h1><p>This is user-edited content with multiple paragraphs.</p><p>Second paragraph with more content.</p><p>Third paragraph to ensure pagination.</p><p>Fourth paragraph for testing.</p><p>Fifth paragraph to create substantial content.</p>"
  };

  const editedHTML = mockConvertAIContentToHTML(editedContent);
  console.log(`   HTML Length: ${editedHTML.length}`);
  console.log(`   HTML Preview: "${editedHTML.substring(0, 100)}..."`);
  
  const editedPages = mockPaginateContentForPreview(editedHTML, { wordsPerPage: 20 });
  console.log(`   Pages Generated: ${editedPages.length}`);
  console.log(`   Page Lengths: [${editedPages.map(p => p.length).join(', ')}]`);
  console.log('');

  // Test 5: Large content for stress testing
  console.log('Test 5: Large Content Stress Test');
  const largeContent = {
    introduction: { content: "Introduction with substantial content." },
    chapters: Array(10).fill(null).map((_, i) => ({
      number: i + 1,
      title: `Chapter ${i + 1}`,
      content: `This is chapter ${i + 1} with substantial content. `.repeat(50)
    })),
    conclusion: { content: "Conclusion with substantial content." }
  };

  const largeHTML = mockConvertAIContentToHTML(largeContent);
  console.log(`   HTML Length: ${largeHTML.length}`);
  
  const largePages = mockPaginateContentForPreview(largeHTML, { wordsPerPage: 200 });
  console.log(`   Pages Generated: ${largePages.length}`);
  console.log(`   Average Page Length: ${Math.round(largePages.reduce((sum, page) => sum + page.length, 0) / largePages.length)}`);
  console.log('');

  // Summary
  console.log('📊 Test Summary');
  console.log('===============');
  console.log(`✅ Complete content: ${completePages.length > 1 ? 'PASS' : 'FAIL'} (${completePages.length} pages)`);
  console.log(`✅ Minimal content: ${minimalPages.length === 1 ? 'PASS' : 'FAIL'} (${minimalPages.length} pages)`);
  console.log(`✅ Empty content: ${emptyPages.length === 1 && emptyPages[0].includes('empty-content') ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Edited content: ${editedPages.length > 1 ? 'PASS' : 'FAIL'} (${editedPages.length} pages)`);
  console.log(`✅ Large content: ${largePages.length > 5 ? 'PASS' : 'FAIL'} (${largePages.length} pages)`);
  
  const allTestsPassed = completePages.length > 1 && 
                        minimalPages.length === 1 && 
                        emptyPages.length === 1 && 
                        editedPages.length > 1 && 
                        largePages.length > 5;
  
  console.log(`\n🎉 Overall Result: ${allTestsPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
  
  return allTestsPassed;
}

// Run the tests
try {
  const success = testContentConversionAndPagination();
  process.exit(success ? 0 : 1);
} catch (error) {
  console.error('❌ Test execution failed:', error.message);
  process.exit(1);
}
