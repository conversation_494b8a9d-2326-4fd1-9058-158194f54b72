import React from "react";
import Routes from "./Routes";
import { SidebarProvider } from "./contexts/SidebarContext";
import { AuthProvider } from "./contexts/AuthContext";
import AuthErrorBoundary from "./components/auth/AuthErrorBoundary";
import NotificationProvider from "./components/notifications/NotificationProvider.jsx";
import "./styles/animations.css";

function App() {
  return (
    <AuthErrorBoundary>
      <AuthProvider>
        <NotificationProvider>
          <SidebarProvider>
            <Routes />
          </SidebarProvider>
        </NotificationProvider>
      </AuthProvider>
    </AuthErrorBoundary>
  );
}

export default App;
