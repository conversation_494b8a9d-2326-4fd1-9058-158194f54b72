import React from 'react';
import Icon from '../../../../components/AppIcon';

/**
 * MultiSelectCard - Reusable component for multiple choice selections
 * Supports single and multi-select modes with card-based UI
 */
const MultiSelectCard = ({
  options = [],
  value = null, // For single select: string, For multi-select: array
  onChange,
  multiSelect = false,
  className = '',
  cardClassName = '',
  title = '',
  description = '',
  required = false,
  disabled = false,
  columns = 'auto', // 'auto', 1, 2, 3, 4
  size = 'medium', // 'small', 'medium', 'large'
  showIcons = true,
  showDescriptions = true,
  allowCustom = false,
  customPlaceholder = 'Enter custom option...',
  onCustomAdd = null,
}) => {
  const [customValue, setCustomValue] = React.useState('');
  const [showCustomInput, setShowCustomInput] = React.useState(false);

  // Handle selection for both single and multi-select
  const handleSelection = (optionId) => {
    if (disabled) return;

    if (multiSelect) {
      const currentValues = Array.isArray(value) ? value : [];
      const newValues = currentValues.includes(optionId)
        ? currentValues.filter(id => id !== optionId)
        : [...currentValues, optionId];
      onChange(newValues);
    } else {
      onChange(optionId === value ? null : optionId);
    }
  };

  // Handle custom option addition
  const handleCustomAdd = () => {
    if (customValue.trim() && onCustomAdd) {
      onCustomAdd(customValue.trim());
      setCustomValue('');
      setShowCustomInput(false);
    }
  };

  // Check if option is selected
  const isSelected = (optionId) => {
    if (multiSelect) {
      return Array.isArray(value) && value.includes(optionId);
    }
    return value === optionId;
  };

  // Get grid columns class
  const getGridColumns = () => {
    if (columns === 'auto') {
      if (options.length <= 2) return 'grid-cols-1 md:grid-cols-2';
      if (options.length <= 4) return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-2';
      return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
    }
    return `grid-cols-1 md:grid-cols-${Math.min(columns, 4)}`;
  };

  // Get card size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'p-3 text-sm';
      case 'large':
        return 'p-6 text-base';
      default:
        return 'p-4 text-sm md:text-base';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      {(title || description) && (
        <div className="space-y-2">
          {title && (
            <h3 className="text-lg font-semibold text-text-primary">
              {title}
              {required && <span className="text-red-500 ml-1">*</span>}
            </h3>
          )}
          {description && (
            <p className="text-text-secondary text-sm">{description}</p>
          )}
        </div>
      )}

      {/* Options Grid */}
      <div className={`grid gap-3 ${getGridColumns()}`}>
        {options.map((option) => {
          const selected = isSelected(option.id);
          
          return (
            <button
              key={option.id}
              type="button"
              onClick={() => handleSelection(option.id)}
              disabled={disabled || option.disabled}
              className={`
                ${getSizeClasses()}
                border-2 rounded-lg transition-all duration-200 text-left
                hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary/20
                disabled:opacity-50 disabled:cursor-not-allowed
                ${selected
                  ? 'border-primary bg-primary/5 shadow-sm'
                  : 'border-border hover:border-primary/50 bg-white'
                }
                ${cardClassName}
              `}
            >
              <div className="flex items-start space-x-3">
                {/* Selection Indicator */}
                <div className="flex-shrink-0 mt-0.5">
                  {multiSelect ? (
                    <div className={`
                      w-4 h-4 border-2 rounded transition-colors
                      ${selected 
                        ? 'border-primary bg-primary' 
                        : 'border-gray-300'
                      }
                    `}>
                      {selected && (
                        <Icon name="Check" size={12} className="text-white" />
                      )}
                    </div>
                  ) : (
                    <div className={`
                      w-4 h-4 border-2 rounded-full transition-colors
                      ${selected 
                        ? 'border-primary' 
                        : 'border-gray-300'
                      }
                    `}>
                      {selected && (
                        <div className="w-2 h-2 bg-primary rounded-full m-0.5" />
                      )}
                    </div>
                  )}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    {/* Icon */}
                    {showIcons && option.icon && (
                      <Icon 
                        name={option.icon} 
                        size={size === 'large' ? 20 : 16} 
                        className={`flex-shrink-0 ${
                          selected ? 'text-primary' : 'text-gray-400'
                        }`} 
                      />
                    )}
                    
                    {/* Title */}
                    <h4 className={`font-medium truncate ${
                      selected ? 'text-primary' : 'text-text-primary'
                    }`}>
                      {option.name || option.label}
                    </h4>
                  </div>

                  {/* Description */}
                  {showDescriptions && option.description && (
                    <p className="text-text-secondary text-xs mt-1 line-clamp-2">
                      {option.description}
                    </p>
                  )}

                  {/* Additional Info */}
                  {option.badge && (
                    <span className="inline-block mt-2 px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                      {option.badge}
                    </span>
                  )}
                </div>
              </div>
            </button>
          );
        })}

        {/* Custom Option */}
        {allowCustom && (
          <div className="space-y-2">
            {!showCustomInput ? (
              <button
                type="button"
                onClick={() => setShowCustomInput(true)}
                className={`
                  ${getSizeClasses()}
                  border-2 border-dashed border-gray-300 rounded-lg
                  hover:border-primary/50 hover:bg-gray-50
                  transition-all duration-200 text-left w-full
                  ${cardClassName}
                `}
              >
                <div className="flex items-center space-x-3 text-gray-500">
                  <Icon name="Plus" size={16} />
                  <span>Add custom option</span>
                </div>
              </button>
            ) : (
              <div className={`
                ${getSizeClasses()}
                border-2 border-primary rounded-lg bg-white
                ${cardClassName}
              `}>
                <div className="space-y-3">
                  <input
                    type="text"
                    value={customValue}
                    onChange={(e) => setCustomValue(e.target.value)}
                    placeholder={customPlaceholder}
                    className="w-full border-0 focus:ring-0 p-0 text-sm placeholder-gray-400"
                    autoFocus
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        handleCustomAdd();
                      }
                    }}
                  />
                  <div className="flex space-x-2">
                    <button
                      type="button"
                      onClick={handleCustomAdd}
                      disabled={!customValue.trim()}
                      className="px-3 py-1 bg-primary text-white text-xs rounded hover:bg-primary/90 disabled:opacity-50"
                    >
                      Add
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        setShowCustomInput(false);
                        setCustomValue('');
                      }}
                      className="px-3 py-1 bg-gray-100 text-gray-600 text-xs rounded hover:bg-gray-200"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Selection Summary for Multi-select */}
      {multiSelect && Array.isArray(value) && value.length > 0 && (
        <div className="text-sm text-text-secondary">
          {value.length} option{value.length !== 1 ? 's' : ''} selected
        </div>
      )}
    </div>
  );
};

export default MultiSelectCard;
