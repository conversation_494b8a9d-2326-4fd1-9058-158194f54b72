import React from 'react';
import Icon from '../../../../components/AppIcon';

/**
 * ConditionalQuestion - Component that renders questions based on conditions
 * Supports complex conditional logic for dynamic questionnaire flows
 */
const ConditionalQuestion = ({
  condition,
  data,
  children,
  fallback = null,
  showTransition = true,
  animationDuration = 300,
  className = '',
  debugMode = false,
}) => {
  // Evaluate condition
  const evaluateCondition = (condition, data) => {
    if (typeof condition === 'boolean') {
      return condition;
    }
    
    if (typeof condition === 'function') {
      try {
        return condition(data);
      } catch (error) {
        console.error('Error evaluating condition function:', error);
        return false;
      }
    }
    
    if (typeof condition === 'string') {
      return evaluateStringCondition(condition, data);
    }
    
    if (typeof condition === 'object' && condition !== null) {
      return evaluateObjectCondition(condition, data);
    }
    
    return false;
  };

  // Evaluate string-based conditions (simple expressions)
  const evaluateStringCondition = (conditionStr, data) => {
    try {
      // Replace data references with actual values
      let expression = conditionStr;
      
      // Handle nested object references (e.g., "user.profile.age > 18")
      const dataRefs = expression.match(/[a-zA-Z_][a-zA-Z0-9_.]*(?:\[[^\]]+\])?/g) || [];
      
      dataRefs.forEach(ref => {
        const value = getNestedValue(data, ref);
        const jsonValue = JSON.stringify(value);
        expression = expression.replace(new RegExp(`\\b${ref}\\b`, 'g'), jsonValue);
      });
      
      // Evaluate the expression safely
      return Function(`"use strict"; return (${expression})`)();
    } catch (error) {
      console.error('Error evaluating string condition:', error);
      return false;
    }
  };

  // Evaluate object-based conditions (structured conditions)
  const evaluateObjectCondition = (conditionObj, data) => {
    const { field, operator, value, logic, conditions } = conditionObj;
    
    // Handle multiple conditions with logic operators
    if (conditions && Array.isArray(conditions)) {
      const results = conditions.map(cond => evaluateCondition(cond, data));
      
      switch (logic) {
        case 'AND':
        case 'and':
          return results.every(result => result);
        case 'OR':
        case 'or':
          return results.some(result => result);
        case 'NOT':
        case 'not':
          return !results[0];
        default:
          return results.every(result => result); // Default to AND
      }
    }
    
    // Handle single field condition
    if (field && operator !== undefined) {
      const fieldValue = getNestedValue(data, field);
      return evaluateOperator(fieldValue, operator, value);
    }
    
    return false;
  };

  // Evaluate operators
  const evaluateOperator = (fieldValue, operator, compareValue) => {
    switch (operator) {
      case '==':
      case 'equals':
        return fieldValue == compareValue;
      case '===':
      case 'strictEquals':
        return fieldValue === compareValue;
      case '!=':
      case 'notEquals':
        return fieldValue != compareValue;
      case '!==':
      case 'strictNotEquals':
        return fieldValue !== compareValue;
      case '>':
      case 'greaterThan':
        return fieldValue > compareValue;
      case '>=':
      case 'greaterThanOrEqual':
        return fieldValue >= compareValue;
      case '<':
      case 'lessThan':
        return fieldValue < compareValue;
      case '<=':
      case 'lessThanOrEqual':
        return fieldValue <= compareValue;
      case 'includes':
        return Array.isArray(fieldValue) && fieldValue.includes(compareValue);
      case 'excludes':
        return Array.isArray(fieldValue) && !fieldValue.includes(compareValue);
      case 'contains':
        return typeof fieldValue === 'string' && fieldValue.includes(compareValue);
      case 'startsWith':
        return typeof fieldValue === 'string' && fieldValue.startsWith(compareValue);
      case 'endsWith':
        return typeof fieldValue === 'string' && fieldValue.endsWith(compareValue);
      case 'isEmpty':
        return !fieldValue || fieldValue.length === 0;
      case 'isNotEmpty':
        return fieldValue && fieldValue.length > 0;
      case 'isNull':
        return fieldValue === null;
      case 'isNotNull':
        return fieldValue !== null;
      case 'isUndefined':
        return fieldValue === undefined;
      case 'isDefined':
        return fieldValue !== undefined;
      case 'in':
        return Array.isArray(compareValue) && compareValue.includes(fieldValue);
      case 'notIn':
        return Array.isArray(compareValue) && !compareValue.includes(fieldValue);
      default:
        console.warn(`Unknown operator: ${operator}`);
        return false;
    }
  };

  // Get nested value from object using dot notation
  const getNestedValue = (obj, path) => {
    if (!path) return obj;
    
    return path.split('.').reduce((current, key) => {
      // Handle array indices
      if (key.includes('[') && key.includes(']')) {
        const [arrayKey, indexStr] = key.split('[');
        const index = parseInt(indexStr.replace(']', ''));
        return current?.[arrayKey]?.[index];
      }
      return current?.[key];
    }, obj);
  };

  // Check if condition is met
  const shouldShow = evaluateCondition(condition, data);

  // Debug information
  if (debugMode) {
    console.log('ConditionalQuestion Debug:', {
      condition,
      data,
      shouldShow,
      evaluatedAt: new Date().toISOString()
    });
  }

  // Render based on condition
  if (!shouldShow) {
    return fallback;
  }

  // Render with transition if enabled
  if (showTransition) {
    return (
      <div 
        className={`
          transition-all duration-${animationDuration} ease-in-out
          animate-in fade-in slide-in-from-top-2
          ${className}
        `}
        style={{
          animationDuration: `${animationDuration}ms`
        }}
      >
        {children}
      </div>
    );
  }

  return (
    <div className={className}>
      {children}
    </div>
  );
};

// Helper component for creating condition groups
export const ConditionGroup = ({ 
  logic = 'AND', 
  conditions = [], 
  data, 
  children, 
  fallback = null,
  className = ''
}) => {
  const groupCondition = {
    logic,
    conditions
  };

  return (
    <ConditionalQuestion
      condition={groupCondition}
      data={data}
      fallback={fallback}
      className={className}
    >
      {children}
    </ConditionalQuestion>
  );
};

// Helper component for simple field-based conditions
export const FieldCondition = ({ 
  field, 
  operator, 
  value, 
  data, 
  children, 
  fallback = null,
  className = ''
}) => {
  const fieldCondition = {
    field,
    operator,
    value
  };

  return (
    <ConditionalQuestion
      condition={fieldCondition}
      data={data}
      fallback={fallback}
      className={className}
    >
      {children}
    </ConditionalQuestion>
  );
};

// Predefined condition builders for common use cases
export const conditionBuilders = {
  // Check if a field has a specific value
  fieldEquals: (field, value) => ({ field, operator: 'equals', value }),
  
  // Check if a field is not empty
  fieldNotEmpty: (field) => ({ field, operator: 'isNotEmpty' }),
  
  // Check if an array field includes a value
  fieldIncludes: (field, value) => ({ field, operator: 'includes', value }),
  
  // Check if a field is one of multiple values
  fieldIn: (field, values) => ({ field, operator: 'in', value: values }),
  
  // Combine multiple conditions with AND
  and: (...conditions) => ({ logic: 'AND', conditions }),
  
  // Combine multiple conditions with OR
  or: (...conditions) => ({ logic: 'OR', conditions }),
  
  // Negate a condition
  not: (condition) => ({ logic: 'NOT', conditions: [condition] }),
  
  // Check document type
  documentType: (type) => conditionBuilders.fieldEquals('documentPurpose.primaryType', type),
  
  // Check audience level
  audienceLevel: (level) => conditionBuilders.fieldEquals('audienceAnalysis.knowledgeLevel', level),
  
  // Check if academic document
  isAcademic: () => conditionBuilders.documentType('academic'),
  
  // Check if business document
  isBusiness: () => conditionBuilders.documentType('business'),
  
  // Check if research is required
  requiresResearch: () => conditionBuilders.fieldEquals('contentScope.researchRequired', true),
  
  // Check if citations are needed
  needsCitations: () => ({ 
    field: 'additionalRequirements.citationStyle', 
    operator: 'notEquals', 
    value: 'none' 
  }),
};

export default ConditionalQuestion;
