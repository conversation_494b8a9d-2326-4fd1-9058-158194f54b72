import React, { useState, useEffect } from 'react';
import Button from '../../../../components/ui/Button';
import MultiSelectCard from '../questionnaire/MultiSelectCard';
import { generateSubNiches } from '../../../../services/aiService';

/**
 * SubNicheSelectionStep - Dedicated step for AI-generated sub-niche selection
 * Generates relevant sub-niches based on the user's entered topic
 */
const SubNicheSelectionStep = ({ 
  formData, 
  onInputChange, 
  onValidationChange,
  className = '' 
}) => {
  const [availableSubNiches, setAvailableSubNiches] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(false);

  // AI-powered sub-niche generation using real Gemini AI
  const generateSubNichesForTopic = async (topic) => {
    setIsGenerating(true);

    try {
      // Use real AI service
      const language = formData.topicAndNiche?.language || 'english';
      const aiSubNiches = await generateSubNiches(topic, language);

      setAvailableSubNiches(aiSubNiches);
      onInputChange('topicAndNiche.availableSubNiches', aiSubNiches);
      setHasGenerated(true);

    } catch (error) {
      console.error('Error generating sub-niches:', error);
      // Fallback to predefined options if AI fails
      const fallbackSubNiches = getFallbackSubNiches(topic);
      setAvailableSubNiches(fallbackSubNiches);
      onInputChange('topicAndNiche.availableSubNiches', fallbackSubNiches);
    } finally {
      setIsGenerating(false);
    }
  };

  // Fallback sub-niches if AI generation fails
  const getFallbackSubNiches = (topic) => {
    return [
      { id: 'fundamentals', name: `${topic} Fundamentals`, description: 'Core concepts and basics' },
      { id: 'practical-guide', name: `Practical ${topic} Guide`, description: 'Hands-on approach and implementation' },
      { id: 'advanced-techniques', name: `Advanced ${topic} Techniques`, description: 'Expert-level strategies' },
      { id: 'common-mistakes', name: `Common ${topic} Mistakes`, description: 'What to avoid and how to fix issues' },
    ];
  };

  // Generate sub-niches when component mounts or topic changes
  useEffect(() => {
    const topic = formData.topicAndNiche?.mainTopic;
    if (topic && !hasGenerated) {
      generateSubNichesForTopic(topic);
    }
  }, [formData.topicAndNiche?.mainTopic, hasGenerated]);

  // Reset hasGenerated flag when availableSubNiches are cleared (e.g., returning from editor)
  useEffect(() => {
    const topic = formData.topicAndNiche?.mainTopic;
    const availableSubNiches = formData.topicAndNiche?.availableSubNiches;

    // Reset if topic exists but no available sub-niches (indicates reset)
    if (topic && (!availableSubNiches || availableSubNiches.length === 0)) {
      console.log('Detected AI content reset - resetting sub-niche generation flag');
      setHasGenerated(false);
      setAvailableSubNiches([]);
    }
  }, [formData.topicAndNiche?.availableSubNiches]);

  const handleSubNicheChange = (selectedSubNiches) => {
    onInputChange('topicAndNiche.subNiches', selectedSubNiches);
  };

  const handleRegenerateSubNiches = () => {
    const topic = formData.topicAndNiche?.mainTopic;
    if (topic) {
      setHasGenerated(false);
      generateSubNichesForTopic(topic);
    }
  };

  // Validation
  useEffect(() => {
    const isValid = formData.topicAndNiche?.subNiches?.length > 0 ||
                   formData.topicAndNiche?.customSubNiche?.trim().length > 0;

    onValidationChange?.(isValid);
  }, [formData.topicAndNiche?.subNiches, formData.topicAndNiche?.customSubNiche]);

  return (
    <div className={`space-y-8 max-w-4xl mx-auto px-4 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl md:text-3xl font-bold text-text-primary mb-4">
          Choose your Sub-niches
        </h2>
        <p className="text-text-secondary text-base md:text-lg">
          Select specific areas within <strong>{formData.topicAndNiche?.mainTopic}</strong> to focus on
        </p>
      </div>

      {/* Loading State */}
      {isGenerating && (
        <div className="text-center py-12">
          <div className="inline-flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span className="text-text-secondary">Generating relevant sub-niches using AI...</span>
          </div>
        </div>
      )}

      {/* Sub-niches Selection */}
      {!isGenerating && availableSubNiches.length > 0 && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg md:text-xl font-bold text-text-primary mb-2">
                AI-Generated Sub-niches
              </h3>
              <p className="text-text-secondary text-sm md:text-base">
                Select one or more areas that match your content goals
              </p>
            </div>
            <Button
              variant="secondary"
              size="sm"
              onClick={handleRegenerateSubNiches}
              iconName="RefreshCw"
              iconPosition="left"
              className="text-sm"
            >
              Regenerate
            </Button>
          </div>

          <MultiSelectCard
            options={availableSubNiches}
            value={formData.topicAndNiche?.subNiches || []}
            onChange={handleSubNicheChange}
            multiSelect={true}
            columns={2}
            size="medium"
            showIcons={false}
            showDescriptions={true}
            allowCustom={true}
            customPlaceholder="Enter your custom sub-niche..."
            onCustomAdd={(customValue) => {
              onInputChange('topicAndNiche.customSubNiche', customValue);
            }}
          />
        </div>
      )}

      {/* AI Integration Notice */}
      <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-purple-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.53-1.053l-.548-.547z" />
            </svg>
          </div>
          <div>
            <h3 className="text-sm font-medium text-purple-800 mb-1">
              🤖 AI-Powered Suggestions
            </h3>
            <p className="text-sm text-purple-700">
              These sub-niches were generated specifically for your topic using AI. They're designed to help you create more targeted and valuable content.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubNicheSelectionStep;
