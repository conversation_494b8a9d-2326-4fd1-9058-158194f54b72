import React, { useState, useEffect } from 'react';
import Input from '../../../../components/ui/Input';
import CustomDropdown from '../CustomDropdown';
import MultiSelectCard from '../questionnaire/MultiSelectCard';
import { subNicheConfigurations } from '../../utils/questionnaireDataStructure';

/**
 * TopicNicheStep - Designrr-style topic and niche selection
 * Matches the flow: Topic input -> Language selection -> Sub-niche selection
 */
const TopicNicheStep = ({ 
  formData, 
  onInputChange, 
  onValidationChange,
  className = '' 
}) => {
  const [availableSubNiches, setAvailableSubNiches] = useState([]);
  const [showRegenerateButton, setShowRegenerateButton] = useState(false);

  // Language options
  const languageOptions = [
    { id: 'english', name: 'English (British)', icon: '🇬🇧' },
    { id: 'english-us', name: 'English (US)', icon: '🇺🇸' },
    { id: 'spanish', name: 'Spanish', icon: '🇪🇸' },
    { id: 'french', name: 'French', icon: '🇫🇷' },
    { id: 'german', name: 'German', icon: '🇩🇪' },
    { id: 'italian', name: 'Italian', icon: '🇮🇹' },
    { id: 'portuguese', name: 'Portuguese', icon: '🇵🇹' },
    { id: 'dutch', name: 'Dutch', icon: '🇳🇱' },
  ];

  // Update available sub-niches based on main topic
  useEffect(() => {
    if (formData.topicAndNiche?.mainTopic) {
      const topicKey = detectTopicKey(formData.topicAndNiche.mainTopic);
      const subNiches = subNicheConfigurations[topicKey] || [];
      setAvailableSubNiches(subNiches);
      setShowRegenerateButton(subNiches.length > 0);
      
      // Update form data with available sub-niches
      onInputChange('topicAndNiche.availableSubNiches', subNiches);
    }
  }, [formData.topicAndNiche?.mainTopic]);

  // Simple topic detection logic (can be enhanced with AI)
  const detectTopicKey = (topic) => {
    const lowerTopic = topic.toLowerCase();
    
    if (lowerTopic.includes('life') || lowerTopic.includes('coaching')) {
      return 'life-coaching';
    } else if (lowerTopic.includes('business') || lowerTopic.includes('entrepreneur')) {
      return 'business';
    } else if (lowerTopic.includes('health') || lowerTopic.includes('wellness') || lowerTopic.includes('fitness')) {
      return 'health-wellness';
    }
    
    return 'life-coaching'; // Default fallback
  };

  const handleTopicChange = (value) => {
    onInputChange('topicAndNiche.mainTopic', value);
    // Clear selected sub-niches when topic changes
    onInputChange('topicAndNiche.subNiches', []);
  };

  const handleSubNicheChange = (selectedSubNiches) => {
    onInputChange('topicAndNiche.subNiches', selectedSubNiches);
  };

  const handleRegenerateSubNiches = () => {
    // In a real implementation, this would call an AI service to generate new sub-niches
    // For now, we'll just shuffle the existing ones
    const shuffled = [...availableSubNiches].sort(() => Math.random() - 0.5);
    setAvailableSubNiches(shuffled);
    onInputChange('topicAndNiche.availableSubNiches', shuffled);
  };

  // Validation
  useEffect(() => {
    const isValid = formData.topicAndNiche?.mainTopic &&
                   formData.topicAndNiche?.language &&
                   (formData.topicAndNiche?.subNiches?.length > 0 || formData.topicAndNiche?.customSubNiche);

    onValidationChange?.(isValid);
  }, [formData.topicAndNiche]);

  return (
    <div className={`space-y-8 max-w-2xl mx-auto px-4 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl md:text-3xl font-bold text-text-primary mb-4">
          Enter your Topic or Niche
        </h2>
        <p className="text-text-secondary text-base md:text-lg">
          Enter your topic or niche
        </p>
      </div>

      {/* Main Topic Input */}
      <div className="space-y-4">
        <label className="block text-sm md:text-base font-medium text-text-primary">
          Enter your topic or niche
        </label>
        <Input
          type="text"
          placeholder="Life coaching"
          value={formData.topicAndNiche?.mainTopic || ''}
          onChange={(e) => handleTopicChange(e.target.value)}
          className="w-full h-12 md:h-14 text-base md:text-lg rounded-lg border-2 focus:border-primary focus:ring-0 px-4"
        />
      </div>

      {/* Language Selection */}
      <div className="space-y-4">
        <label className="block text-sm md:text-base font-medium text-text-primary">
          Language
        </label>
        <CustomDropdown
          value={formData.topicAndNiche?.language || 'english'}
          onChange={(value) => onInputChange('topicAndNiche.language', value)}
          options={languageOptions}
          placeholder="English (British)"
          className="w-full"
        />
      </div>

      {/* Sub-niches Selection */}
      {availableSubNiches.length > 0 && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg md:text-xl font-bold text-text-primary mb-2">
                Choose your Sub-niches
              </h3>
              <p className="text-text-secondary text-sm md:text-base">
                Select the specific areas you want to focus on
              </p>
            </div>
            {showRegenerateButton && (
              <button
                type="button"
                onClick={handleRegenerateSubNiches}
                className="flex items-center space-x-2 text-primary hover:text-primary/80 transition-colors"
              >
                <span className="text-sm font-medium">Regenerate</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
            )}
          </div>

          <MultiSelectCard
            options={availableSubNiches}
            value={formData.topicAndNiche?.subNiches || []}
            onChange={handleSubNicheChange}
            multiSelect={true}
            columns={1}
            size="medium"
            showIcons={false}
            showDescriptions={true}
            allowCustom={true}
            customPlaceholder="Enter your custom sub-niche..."
            onCustomAdd={(customValue) => {
              onInputChange('topicAndNiche.customSubNiche', customValue);
            }}
          />
        </div>
      )}

      {/* Custom Sub-niche Input (fallback if no predefined options) */}
      {availableSubNiches.length === 0 && formData.topicAndNiche?.mainTopic && (
        <div className="space-y-4">
          <label className="block text-sm md:text-base font-medium text-text-primary">
            Describe your specific focus area
          </label>
          <Input
            type="text"
            placeholder="e.g., Wellness coaching for busy professionals"
            value={formData.topicAndNiche?.customSubNiche || ''}
            onChange={(e) => onInputChange('topicAndNiche.customSubNiche', e.target.value)}
            className="w-full h-12 md:h-14 text-base md:text-lg rounded-lg border-2 focus:border-primary focus:ring-0 px-4"
          />
        </div>
      )}
    </div>
  );
};

export default TopicNicheStep;
