import React, { useEffect } from 'react';
import Input from '../../../../components/ui/Input';
import CustomDropdown from '../CustomDropdown';

/**
 * TopicSelectionStep - Dedicated step for topic input only
 * Clean, focused interface for users to enter their main topic
 */
const TopicSelectionStep = ({ 
  formData, 
  onInputChange, 
  onValidationChange,
  className = '' 
}) => {

  // Language options
  const languageOptions = [
    { id: 'english', name: 'English (British)', icon: '🇬🇧' },
    { id: 'english-us', name: 'English (US)', icon: '🇺🇸' },
    { id: 'spanish', name: 'Spanish', icon: '🇪🇸' },
    { id: 'french', name: 'French', icon: '🇫🇷' },
    { id: 'german', name: 'German', icon: '🇩🇪' },
    { id: 'italian', name: 'Italian', icon: '🇮🇹' },
    { id: 'portuguese', name: 'Portuguese', icon: '🇵🇹' },
    { id: 'dutch', name: 'Dutch', icon: '🇳🇱' },
  ];

  const handleTopicChange = (value) => {
    onInputChange('topicAndNiche.mainTopic', value);
    // Clear any previously selected sub-niches when topic changes
    onInputChange('topicAndNiche.subNiches', []);
    onInputChange('topicAndNiche.availableSubNiches', []);
  };

  // Validation - only require topic and language
  useEffect(() => {
    const isValid = formData.topicAndNiche?.mainTopic?.trim().length > 0 &&
                   formData.topicAndNiche?.language;

    onValidationChange?.(isValid);
  }, [formData.topicAndNiche?.mainTopic, formData.topicAndNiche?.language]);

  // Check if we have extracted content
  const hasExtractedContent = formData.documentPurpose?.importedContent?.extractedContent;

  return (
    <div className={`space-y-8 max-w-2xl mx-auto px-4 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl md:text-3xl font-bold text-text-primary mb-4">
          {hasExtractedContent ? 'Confirm or Modify Your Topic' : 'Enter your Topic or Niche'}
        </h2>
        <p className="text-text-secondary text-base md:text-lg">
          {hasExtractedContent
            ? 'We\'ve pre-filled the topic based on your imported content. You can modify it if needed.'
            : 'What topic would you like to create content about?'
          }
        </p>
      </div>

      {/* Main Topic Input */}
      <div className="space-y-4">
        <label className="block text-sm md:text-base font-medium text-text-primary">
          {hasExtractedContent ? 'Topic (from imported content)' : 'Enter your topic or niche'}
        </label>
        <Input
          type="text"
          placeholder={hasExtractedContent ? "Topic extracted from your content" : "Life coaching"}
          value={formData.topicAndNiche?.mainTopic || ''}
          onChange={(e) => handleTopicChange(e.target.value)}
          className="w-full h-12 md:h-14 text-base md:text-lg rounded-lg border-2 focus:border-primary focus:ring-0 px-4"
          autoFocus={!hasExtractedContent}
        />
        {hasExtractedContent ? (
          <p className="text-xs text-green-600">
            ✓ Topic automatically extracted from your imported content. You can modify it if needed.
          </p>
        ) : (
          <p className="text-xs text-text-secondary">
            Examples: Life coaching, Digital marketing, Personal finance, Health and wellness
          </p>
        )}
      </div>

      {/* Language Selection */}
      <div className="space-y-4">
        <label className="block text-sm md:text-base font-medium text-text-primary">
          Language
        </label>
        <CustomDropdown
          value={formData.topicAndNiche?.language || 'english'}
          onChange={(value) => onInputChange('topicAndNiche.language', value)}
          options={languageOptions}
          placeholder="English (British)"
          className="w-full"
        />
      </div>

      {/* Help Text */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h3 className="text-sm font-medium text-blue-800 mb-1">
              💡 Tip for better results
            </h3>
            <p className="text-sm text-blue-700">
              Be specific with your topic. Instead of "business," try "small business marketing" or "startup fundraising strategies."
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopicSelectionStep;
