/**
 * Debug Pagination Page
 * Temporary page to debug pagination issues
 */

import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { fetchDocument } from '../services/documentService.js';
import { convertAIContentToHTML } from '../utils/contentConverter.js';
import { extractEnhancedContent } from '../services/enhancedContentExtraction.js';
import { paginateContentForPreview } from '../services/previewService.js';

const DebugPagination = () => {
  const { documentId } = useParams();
  const [debugData, setDebugData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadAndDebugDocument = async () => {
      try {
        console.log('🔍 DEBUG: Loading document', { documentId });
        
        // Fetch document data
        const result = await fetchDocument(documentId);
        
        if (result.success && result.data) {
          const { document_data, generated_content } = result.data;
          
          console.log('🔍 DEBUG: Document loaded', {
            hasDocumentData: !!document_data,
            hasGeneratedContent: !!generated_content,
            generatedContentKeys: Object.keys(generated_content || {}),
            generatedContentType: typeof generated_content
          });

          // Convert content to HTML
          const htmlContent = convertAIContentToHTML(generated_content, {}, false);
          
          console.log('🔍 DEBUG: Content converted to HTML', {
            htmlLength: htmlContent?.length || 0,
            htmlPreview: htmlContent?.substring(0, 200)
          });

          // Create mock editor instance
          const mockEditorInstance = {
            getHTML: () => htmlContent,
            getText: () => htmlContent?.replace(/<[^>]*>/g, '') || '',
            getJSON: () => ({ content: [] })
          };

          // Extract enhanced content
          const enhancedContent = extractEnhancedContent(mockEditorInstance);
          
          console.log('🔍 DEBUG: Enhanced content extracted', {
            hasHtml: !!enhancedContent.html,
            htmlType: typeof enhancedContent.html,
            htmlKeys: enhancedContent.html ? Object.keys(enhancedContent.html) : 'N/A',
            previewLength: enhancedContent.html?.preview?.length || 0,
            rawLength: enhancedContent.html?.raw?.length || 0,
            cleanedLength: enhancedContent.html?.cleaned?.length || 0
          });

          // Test pagination
          const htmlForPagination = enhancedContent.html?.preview || enhancedContent.html?.raw || enhancedContent.html?.cleaned || '';
          const paginatedContent = paginateContentForPreview(htmlForPagination, {
            wordsPerPage: 500,
            maxPageHeight: 950,
            useHeightBasedPagination: true,
            preserveHeadings: true
          });

          console.log('🔍 DEBUG: Content paginated', {
            inputLength: htmlForPagination.length,
            outputPageCount: paginatedContent.length,
            pageLengths: paginatedContent.map(page => page.length),
            firstPagePreview: paginatedContent[0]?.substring(0, 100)
          });

          setDebugData({
            documentData: document_data,
            generatedContent: generated_content,
            htmlContent,
            enhancedContent,
            paginatedContent,
            analysis: {
              originalContentStructure: {
                hasIntroduction: !!generated_content?.introduction,
                hasChapters: !!generated_content?.chapters,
                chaptersCount: generated_content?.chapters?.length || 0,
                hasConclusion: !!generated_content?.conclusion,
                hasEditorHTML: !!generated_content?.editorHTML
              },
              htmlConversion: {
                inputType: typeof generated_content,
                outputLength: htmlContent?.length || 0,
                isEmpty: !htmlContent || htmlContent.trim() === ''
              },
              enhancedExtraction: {
                htmlStructure: enhancedContent.html ? Object.keys(enhancedContent.html) : [],
                previewContentLength: enhancedContent.html?.preview?.length || 0
              },
              pagination: {
                inputLength: htmlForPagination.length,
                outputPageCount: paginatedContent.length,
                successful: paginatedContent.length > 0
              }
            }
          });

        } else {
          setError('Document not found or failed to load');
        }

      } catch (err) {
        console.error('🔍 DEBUG: Error loading document', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (documentId) {
      loadAndDebugDocument();
    }
  }, [documentId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading document for debugging...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">Debug Error</div>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Pagination Debug Report</h1>
        
        {debugData && (
          <div className="space-y-8">
            {/* Analysis Summary */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Analysis Summary</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-blue-50 p-4 rounded">
                  <h3 className="font-medium text-blue-900">Original Content</h3>
                  <p className="text-sm text-blue-700">
                    Chapters: {debugData.analysis.originalContentStructure.chaptersCount}<br/>
                    Has Introduction: {debugData.analysis.originalContentStructure.hasIntroduction ? '✅' : '❌'}<br/>
                    Has Editor HTML: {debugData.analysis.originalContentStructure.hasEditorHTML ? '✅' : '❌'}
                  </p>
                </div>
                <div className="bg-green-50 p-4 rounded">
                  <h3 className="font-medium text-green-900">HTML Conversion</h3>
                  <p className="text-sm text-green-700">
                    Length: {debugData.analysis.htmlConversion.outputLength}<br/>
                    Is Empty: {debugData.analysis.htmlConversion.isEmpty ? '❌' : '✅'}
                  </p>
                </div>
                <div className="bg-yellow-50 p-4 rounded">
                  <h3 className="font-medium text-yellow-900">Enhanced Extraction</h3>
                  <p className="text-sm text-yellow-700">
                    Structure: {debugData.analysis.enhancedExtraction.htmlStructure.join(', ')}<br/>
                    Preview Length: {debugData.analysis.enhancedExtraction.previewContentLength}
                  </p>
                </div>
                <div className="bg-purple-50 p-4 rounded">
                  <h3 className="font-medium text-purple-900">Pagination</h3>
                  <p className="text-sm text-purple-700">
                    Input: {debugData.analysis.pagination.inputLength}<br/>
                    Pages: {debugData.analysis.pagination.outputPageCount}<br/>
                    Success: {debugData.analysis.pagination.successful ? '✅' : '❌'}
                  </p>
                </div>
              </div>
            </div>

            {/* Generated Content Structure */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Generated Content Structure</h2>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
                {JSON.stringify(debugData.generatedContent, null, 2)}
              </pre>
            </div>

            {/* HTML Content */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Converted HTML Content</h2>
              <div className="bg-gray-100 p-4 rounded">
                <p className="text-sm text-gray-600 mb-2">Length: {debugData.htmlContent?.length || 0} characters</p>
                <div className="max-h-96 overflow-auto">
                  <pre className="text-sm">{debugData.htmlContent}</pre>
                </div>
              </div>
            </div>

            {/* Paginated Content */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Paginated Content ({debugData.paginatedContent?.length || 0} pages)</h2>
              <div className="space-y-4">
                {debugData.paginatedContent?.map((page, index) => (
                  <div key={index} className="border rounded p-4">
                    <h3 className="font-medium mb-2">Page {index + 1} ({page.length} characters)</h3>
                    <div className="bg-gray-50 p-3 rounded max-h-48 overflow-auto">
                      <div className="text-sm" dangerouslySetInnerHTML={{ __html: page }} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DebugPagination;
