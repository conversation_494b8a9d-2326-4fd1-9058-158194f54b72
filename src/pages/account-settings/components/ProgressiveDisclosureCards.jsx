import React, { useState, useEffect, useRef } from 'react';
import Icon from '../../../components/AppIcon';
import ProfileSection from './ProfileSection';
import SubscriptionSection from './SubscriptionSection';
import PreferencesSection from './PreferencesSection';
import SecuritySection from './SecuritySection';
import BillingSection from './BillingSection';

/**
 * ProgressiveDisclosureCards - Mobile-first card-based interface for account settings
 * Replaces traditional tab navigation with expandable cards for better mobile UX
 * Includes full accessibility support with ARIA labels and keyboard navigation
 */
const ProgressiveDisclosureCards = ({
  tabs,
  accountStats,
  authLoading = false
}) => {
  const [expandedCard, setExpandedCard] = useState(null);
  const cardContentRefs = useRef({});

  // Toggle card expansion - only one card can be open at a time (accordion behavior)
  const toggleCard = (cardId) => {
    setExpandedCard(expandedCard === cardId ? null : cardId);
  };

  // Handle animation completion to remove overflow constraints
  useEffect(() => {
    if (expandedCard) {
      const cardElement = cardContentRefs.current[expandedCard];
      if (cardElement) {
        // Add animation complete class after animation duration
        const timer = setTimeout(() => {
          cardElement.classList.add('animation-complete');
        }, 300); // Match animation duration

        return () => {
          clearTimeout(timer);
          // Clean up animation-complete class when card changes
          Object.values(cardContentRefs.current).forEach(el => {
            if (el) el.classList.remove('animation-complete');
          });
        };
      }
    }
  }, [expandedCard]);

  // Handle keyboard navigation
  const handleKeyDown = (event, cardId) => {
    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        toggleCard(cardId);
        break;
      case 'Escape':
        if (expandedCard === cardId) {
          setExpandedCard(null);
        }
        break;
      case 'ArrowDown':
        event.preventDefault();
        focusNextCard(cardId);
        break;
      case 'ArrowUp':
        event.preventDefault();
        focusPreviousCard(cardId);
        break;
      default:
        break;
    }
  };

  // Focus management for keyboard navigation
  const focusNextCard = (currentCardId) => {
    const currentIndex = tabs.findIndex(tab => tab.id === currentCardId);
    const nextIndex = (currentIndex + 1) % tabs.length;
    const nextCardButton = document.getElementById(`card-header-${tabs[nextIndex].id}`);
    if (nextCardButton) {
      nextCardButton.focus();
    }
  };

  const focusPreviousCard = (currentCardId) => {
    const currentIndex = tabs.findIndex(tab => tab.id === currentCardId);
    const prevIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
    const prevCardButton = document.getElementById(`card-header-${tabs[prevIndex].id}`);
    if (prevCardButton) {
      prevCardButton.focus();
    }
  };

  // Helper function to get tab descriptions
  const getTabDescription = (tabId) => {
    const descriptions = {
      profile: "Personal information and avatar",
      subscription: "Plan details and billing information",
      preferences: "App settings and default preferences",
      security: "Password and authentication settings",
      billing: "Payment methods and billing history"
    };
    return descriptions[tabId] || "";
  };

  // Render the appropriate content for each tab
  const renderTabContent = (tabId) => {
    switch (tabId) {
      case 'profile':
        return <ProfileSection />;
      case 'subscription':
        return <SubscriptionSection />;
      case 'preferences':
        return <PreferencesSection />;
      case 'security':
        return <SecuritySection />;
      case 'billing':
        return <BillingSection />;
      default:
        return <ProfileSection />;
    }
  };

  return (
    <div
      className="space-y-4 lg:hidden"
      role="region"
      aria-label="Account settings"
    >
      {/* Account Overview Card - Always Visible and Prominent */}
      <div
        className="bg-surface rounded-lg border border-border p-5 shadow-card"
        role="region"
        aria-labelledby="account-overview-heading"
      >
        <div className="flex items-center space-x-3 mb-5">
          <div className="w-9 h-9 bg-primary/10 rounded-lg flex items-center justify-center" aria-hidden="true">
            <Icon name="User" size={18} color="var(--color-primary)" />
          </div>
          <h3 id="account-overview-heading" className="font-semibold text-text-primary text-base">Account Overview</h3>
        </div>

        <div className="grid grid-cols-3 gap-3">
          <div className="p-3 bg-background rounded-lg text-center">
            <div className="text-xs text-text-secondary mb-1.5 font-medium">Plan</div>
            <div className="text-sm font-semibold text-text-primary">
              {authLoading ? 'Loading...' : (accountStats?.plan || 'Free')}
            </div>
          </div>
          <div className="p-3 bg-background rounded-lg text-center">
            <div className="text-xs text-text-secondary mb-1.5 font-medium">Documents</div>
            <div className="text-sm font-semibold text-accent">
              {authLoading ? 'Loading...' : (accountStats?.documents || '0')}
            </div>
          </div>
          <div className="p-3 bg-background rounded-lg text-center">
            <div className="text-xs text-text-secondary mb-1.5 font-medium">Member Since</div>
            <div className="text-sm font-semibold text-text-primary">
              {authLoading ? 'Loading...' : (accountStats?.memberSince || 'N/A')}
            </div>
          </div>
        </div>
      </div>

      {/* Settings Category Cards */}
      {tabs.map((tab) => (
        <div
          key={tab.id}
          className="bg-surface rounded-lg border border-border overflow-hidden shadow-card card-hover-lift"
        >
          {/* Card Header - Always Visible */}
          <button
            onClick={() => toggleCard(tab.id)}
            onKeyDown={(e) => handleKeyDown(e, tab.id)}
            className="w-full flex items-center justify-between px-5 py-4 text-left hover:bg-surface-hover transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-inset active:bg-surface-hover"
            style={{ minHeight: '56px', minWidth: '44px' }}
            aria-expanded={expandedCard === tab.id}
            aria-controls={`card-content-${tab.id}`}
            aria-describedby={`card-description-${tab.id}`}
            id={`card-header-${tab.id}`}
            type="button"
          >
            <div className="flex items-center space-x-4 flex-1 min-w-0">
              <div className="w-9 h-9 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0" aria-hidden="true">
                <Icon name={tab.icon} size={18} color="var(--color-primary)" />
              </div>
              <div className="text-left flex-1 min-w-0 py-1">
                <h3 className="font-semibold text-text-primary truncate text-base leading-tight mb-1">{tab.label}</h3>
                <p
                  id={`card-description-${tab.id}`}
                  className="text-sm text-text-secondary truncate leading-tight"
                >
                  {getTabDescription(tab.id)}
                </p>
              </div>
            </div>
            <div className="flex-shrink-0 ml-4 w-6 h-6 flex items-center justify-center" aria-hidden="true">
              <Icon
                name="ChevronDown"
                size={18}
                className={`text-text-secondary chevron-rotate ${
                  expandedCard === tab.id ? 'rotate-180' : 'rotate-0'
                }`}
              />
            </div>
          </button>

          {/* Card Content - Expandable */}
          {expandedCard === tab.id && (
            <div
              ref={(el) => cardContentRefs.current[tab.id] = el}
              id={`card-content-${tab.id}`}
              className="border-t border-border bg-surface-secondary animate-slide-down"
              role="region"
              aria-labelledby={`card-header-${tab.id}`}
            >
              <div className="p-4">
                {renderTabContent(tab.id)}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default ProgressiveDisclosureCards;
