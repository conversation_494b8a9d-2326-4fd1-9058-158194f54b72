import React, { useState } from 'react';
import { useSidebar } from '../../contexts/SidebarContext';
import { useAuth } from '../../contexts/AuthContext';
import Header from '../../components/ui/Header';
import Breadcrumbs from '../../components/ui/Breadcrumbs';
import QuickActionSidebar from '../../components/ui/QuickActionSidebar';
import Icon from '../../components/AppIcon';
import ProfileSection from './components/ProfileSection';
import SubscriptionSection from './components/SubscriptionSection';
import PreferencesSection from './components/PreferencesSection';
import SecuritySection from './components/SecuritySection';
import BillingSection from './components/BillingSection';
import ProgressiveDisclosureCards from './components/ProgressiveDisclosureCards';

const AccountSettings = () => {
  const { contentMargin } = useSidebar();
  const { user, profile, loading: authLoading } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');

  // Helper function to format member since date
  const formatMemberSince = (createdAt) => {
    if (!createdAt) return 'N/A';
    
    try {
      const date = new Date(createdAt);
      return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short' 
      });
    } catch (error) {
      return 'N/A';
    }
  };

  // Helper function to format plan name
  const formatPlanName = (subscriptionTier) => {
    if (!subscriptionTier) return 'Free';
    
    return subscriptionTier.charAt(0).toUpperCase() + subscriptionTier.slice(1);
  };

  // Helper function to format documents count
  const formatDocumentsCount = (profile) => {
    if (!profile) return 'Loading...';

    const documentsCreated = profile.documents_created || 0;
    return documentsCreated.toString();
  };

  const tabs = [
    { id: 'profile', label: 'Profile', icon: 'User' },
    { id: 'subscription', label: 'Subscription', icon: 'Crown' },
    { id: 'preferences', label: 'Preferences', icon: 'Settings' },
    { id: 'security', label: 'Security', icon: 'Shield' },
    { id: 'billing', label: 'Billing', icon: 'CreditCard' }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return <ProfileSection />;
      case 'subscription':
        return <SubscriptionSection />;
      case 'preferences':
        return <PreferencesSection />;
      case 'security':
        return <SecuritySection />;
      case 'billing':
        return <BillingSection />;
      default:
        return <ProfileSection />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <QuickActionSidebar />
      
      <main className={`${contentMargin} pt-16 transition-all duration-300 ease-in-out`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Breadcrumbs />
          
          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-2">
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <Icon name="Settings" size={20} color="var(--color-primary)" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-text-primary">Account Settings</h1>
                <p className="text-text-secondary">Manage your account preferences and security settings</p>
              </div>
            </div>
          </div>

          {/* Progressive Disclosure Cards for Mobile */}
          <ProgressiveDisclosureCards
            tabs={tabs}
            accountStats={{
              plan: formatPlanName(profile?.subscription_tier),
              documents: formatDocumentsCount(profile),
              memberSince: formatMemberSince(profile?.created_at)
            }}
            authLoading={authLoading}
          />

          {/* Desktop Layout */}
          <div className="hidden lg:grid lg:grid-cols-4 gap-8">
            {/* Sidebar Navigation */}
            <div className="lg:col-span-1">
              <div className="bg-surface rounded-lg border border-border p-4 sticky top-24">
                <nav className="space-y-1">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-micro ${
                        activeTab === tab.id
                          ? 'bg-primary text-primary-foreground'
                          : 'text-text-secondary hover:text-text-primary hover:bg-background'
                      }`}
                    >
                      <Icon name={tab.icon} size={16} />
                      <span>{tab.label}</span>
                    </button>
                  ))}
                </nav>

                {/* Quick Stats */}
                <div className="mt-6 pt-6 border-t border-border">
                  <h4 className="text-sm font-medium text-text-primary mb-3">Account Overview</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-text-secondary">Plan</span>
                      <span className="text-text-primary font-medium">
                        {authLoading ? 'Loading...' : formatPlanName(profile?.subscription_tier)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-text-secondary">Documents</span>
                      <span className="text-accent font-medium">
                        {authLoading ? 'Loading...' : formatDocumentsCount(profile)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-text-secondary">Member since</span>
                      <span className="text-text-primary">
                        {authLoading ? 'Loading...' : formatMemberSince(profile?.created_at)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3">
              <div className="space-y-6">
                {/* Tab Content */}
                <div className="min-h-[600px]">
                  {renderTabContent()}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default AccountSettings;