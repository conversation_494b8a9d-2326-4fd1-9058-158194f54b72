import React, { useState, useEffect, useMemo } from 'react';
import { fetchCoverTemplates, fetchTemplateCategories } from '../../../services/templateService.js';
import { exportDocument } from '../../../services/exportService.js';
import PreviewRenderer from './PreviewRenderer.jsx';

/**
 * Export with Template Modal
 * Combines export format selection with template selection in a unified workflow
 */
const ExportWithTemplateModal = ({ 
  isOpen, 
  onClose, 
  documentData = {},
  generatedContent = {},
  editorInstance = null
}) => {
  const [currentStep, setCurrentStep] = useState(1); // 1: Format, 2: Template, 3: Preview, 4: Export
  const [selectedFormat, setSelectedFormat] = useState('pdf');
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [useTemplate, setUseTemplate] = useState(true);
  
  // Template data
  const [templates, setTemplates] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  
  // Export state
  const [isExporting, setIsExporting] = useState(false);
  const [exportResult, setExportResult] = useState(null);
  const [exportError, setExportError] = useState(null);

  // Export format options
  const exportFormats = [
    {
      id: 'pdf',
      name: 'PDF',
      description: 'Best for sharing and printing',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      supportsTemplates: true,
      recommended: true
    },
    {
      id: 'html',
      name: 'HTML',
      description: 'Web-ready format',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      ),
      supportsTemplates: true,
      recommended: false
    },
    {
      id: 'docx',
      name: 'Word',
      description: 'Editable Microsoft Word format',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      supportsTemplates: false,
      recommended: false
    }
  ];

  // Load templates when modal opens and format supports templates
  useEffect(() => {
    if (isOpen && selectedFormat && exportFormats.find(f => f.id === selectedFormat)?.supportsTemplates) {
      loadTemplatesAndCategories();
    }
  }, [isOpen, selectedFormat]);

  // Filter templates based on search and category
  const filteredTemplates = useMemo(() => {
    let filtered = templates;

    if (selectedCategory) {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(template => 
        template.name.toLowerCase().includes(term) ||
        template.description?.toLowerCase().includes(term) ||
        template.tags?.some(tag => tag.toLowerCase().includes(term))
      );
    }

    return filtered;
  }, [templates, selectedCategory, searchTerm]);

  const loadTemplatesAndCategories = async () => {
    setLoading(true);
    setError(null);

    try {
      const [templatesResult, categoriesResult] = await Promise.all([
        fetchCoverTemplates({ limit: 100 }),
        fetchTemplateCategories()
      ]);

      if (templatesResult.success) {
        setTemplates(templatesResult.templates);
      } else {
        setError(templatesResult.error);
      }

      if (categoriesResult.success) {
        setCategories(categoriesResult.categories);
      }

    } catch (err) {
      setError('Failed to load templates');
      console.error('Template loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleFormatSelect = (format) => {
    setSelectedFormat(format);
    const formatInfo = exportFormats.find(f => f.id === format);
    
    if (formatInfo?.supportsTemplates) {
      setCurrentStep(2); // Go to template selection
    } else {
      setUseTemplate(false);
      setCurrentStep(4); // Skip template selection and preview, go to export
    }
  };

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);
  };

  const handleSkipTemplate = () => {
    setUseTemplate(false);
    setSelectedTemplate(null);
    setCurrentStep(4); // Skip preview, go to export
  };

  const handleUseTemplate = () => {
    if (selectedTemplate) {
      setUseTemplate(true);
      setCurrentStep(3); // Go to preview
    }
  };

  const handlePreviewBack = () => {
    setCurrentStep(2); // Back to template selection
  };

  const handlePreviewDownload = () => {
    setCurrentStep(4); // Go to export confirmation
  };

  const handleTemplateChangeInPreview = (newTemplate) => {
    setSelectedTemplate(newTemplate);
  };

  const handleExport = async () => {
    setIsExporting(true);
    setExportError(null);
    setExportResult(null);

    try {
      const exportOptions = {
        editorInstance: editorInstance
      };

      if (useTemplate && selectedTemplate) {
        exportOptions.selectedTemplate = selectedTemplate;
      }

      const result = await exportDocument(selectedFormat, documentData, generatedContent, exportOptions);

      if (result.success) {
        setExportResult(result);
        // Auto-close modal after successful export
        setTimeout(() => {
          onClose();
          resetModal();
        }, 2000);
      } else {
        setExportError(result.error || 'Export failed');
      }

    } catch (error) {
      console.error('Export error:', error);
      setExportError(error.message || 'Export failed');
    } finally {
      setIsExporting(false);
    }
  };

  const resetModal = () => {
    setCurrentStep(1);
    setSelectedFormat('pdf');
    setSelectedTemplate(null);
    setUseTemplate(true);
    setSearchTerm('');
    setSelectedCategory('');
    setExportResult(null);
    setExportError(null);
    setIsExporting(false);
  };

  const handleClose = () => {
    if (!isExporting) {
      onClose();
      resetModal();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Export Document</h2>
            <div className="flex items-center mt-2 space-x-2">
              {/* Step indicators */}
              <div className={`flex items-center space-x-2 ${currentStep >= 1 ? 'text-blue-600' : 'text-gray-400'}`}>
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${currentStep >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
                  1
                </div>
                <span className="text-sm">Format</span>
              </div>
              <div className="w-4 h-px bg-gray-300"></div>
              <div className={`flex items-center space-x-2 ${currentStep >= 2 ? 'text-blue-600' : 'text-gray-400'}`}>
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${currentStep >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
                  2
                </div>
                <span className="text-sm">Template</span>
              </div>
              <div className="w-4 h-px bg-gray-300"></div>
              <div className={`flex items-center space-x-2 ${currentStep >= 3 ? 'text-blue-600' : 'text-gray-400'}`}>
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${currentStep >= 3 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
                  3
                </div>
                <span className="text-sm">Preview</span>
              </div>
              <div className="w-4 h-px bg-gray-300"></div>
              <div className={`flex items-center space-x-2 ${currentStep >= 4 ? 'text-blue-600' : 'text-gray-400'}`}>
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${currentStep >= 4 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
                  4
                </div>
                <span className="text-sm">Export</span>
              </div>
            </div>
          </div>
          <button
            onClick={handleClose}
            disabled={isExporting}
            className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {currentStep === 1 && (
            <FormatSelectionStep 
              formats={exportFormats}
              selectedFormat={selectedFormat}
              onFormatSelect={handleFormatSelect}
            />
          )}

          {currentStep === 2 && (
            <TemplateSelectionStep
              templates={filteredTemplates}
              categories={categories}
              selectedTemplate={selectedTemplate}
              selectedCategory={selectedCategory}
              searchTerm={searchTerm}
              loading={loading}
              error={error}
              documentData={documentData}
              onTemplateSelect={handleTemplateSelect}
              onCategoryChange={setSelectedCategory}
              onSearchChange={setSearchTerm}
              onSkipTemplate={handleSkipTemplate}
              onUseTemplate={handleUseTemplate}
              onBack={() => setCurrentStep(1)}
            />
          )}

          {currentStep === 3 && (
            <PreviewRenderer
              selectedTemplate={selectedTemplate}
              templates={templates}
              documentData={documentData}
              editorInstance={editorInstance}
              onTemplateChange={handleTemplateChangeInPreview}
              onBack={handlePreviewBack}
              onDownload={handlePreviewDownload}
              className="h-full"
            />
          )}

          {currentStep === 4 && (
            <ExportConfirmationStep
              selectedFormat={selectedFormat}
              selectedTemplate={selectedTemplate}
              useTemplate={useTemplate}
              documentData={documentData}
              isExporting={isExporting}
              exportResult={exportResult}
              exportError={exportError}
              onExport={handleExport}
              onBack={() => setCurrentStep(useTemplate && selectedTemplate ? 3 : 1)}
            />
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * Step 1: Format Selection
 */
const FormatSelectionStep = ({ formats, selectedFormat, onFormatSelect }) => {
  return (
    <div className="p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Choose Export Format</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {formats.map(format => (
          <button
            key={format.id}
            onClick={() => onFormatSelect(format.id)}
            className={`relative p-6 border-2 rounded-lg transition-all hover:shadow-md ${
              selectedFormat === format.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex flex-col items-center text-center">
              <div className={`mb-3 ${selectedFormat === format.id ? 'text-blue-600' : 'text-gray-600'}`}>
                {format.icon}
              </div>
              <h4 className={`font-semibold mb-2 ${selectedFormat === format.id ? 'text-blue-900' : 'text-gray-900'}`}>
                {format.name}
              </h4>
              <p className="text-sm text-gray-600 mb-3">{format.description}</p>

              {format.supportsTemplates && (
                <span className="inline-block px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">
                  Supports Templates
                </span>
              )}

              {format.recommended && (
                <span className="absolute top-2 right-2 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                  Recommended
                </span>
              )}
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

/**
 * Step 2: Template Selection
 */
const TemplateSelectionStep = ({
  templates,
  categories,
  selectedTemplate,
  selectedCategory,
  searchTerm,
  loading,
  error,
  documentData,
  onTemplateSelect,
  onCategoryChange,
  onSearchChange,
  onSkipTemplate,
  onUseTemplate,
  onBack
}) => {
  return (
    <div className="flex flex-col h-full">
      {/* Template Selection Header */}
      <div className="p-6 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Choose Cover Template (Optional)</h3>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search templates..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <svg className="w-5 h-5 text-gray-400 absolute left-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>

          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => onCategoryChange('')}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === ''
                  ? 'bg-blue-500 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100'
              }`}
            >
              All
            </button>
            {categories.map(category => (
              <button
                key={category}
                onClick={() => onCategoryChange(category === selectedCategory ? '' : category)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors capitalize ${
                  selectedCategory === category
                    ? 'bg-blue-500 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-100'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Template Grid */}
      <div className="flex-1 overflow-y-auto p-6">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
              <p className="text-gray-600 mt-4">Loading templates...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-red-600 mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        ) : templates.length === 0 ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-gray-600">No templates found</p>
              {(searchTerm || selectedCategory) && (
                <button
                  onClick={() => {
                    onSearchChange('');
                    onCategoryChange('');
                  }}
                  className="text-blue-500 hover:text-blue-600 mt-2"
                >
                  Clear filters
                </button>
              )}
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {templates.map(template => (
              <TemplateCard
                key={template.id}
                template={template}
                isSelected={selectedTemplate?.id === template.id}
                onClick={() => onTemplateSelect(template)}
                documentData={documentData}
              />
            ))}
          </div>
        )}
      </div>

      {/* Template Selection Footer */}
      <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
        <button
          onClick={onBack}
          className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Back
        </button>
        <div className="flex gap-3">
          <button
            onClick={onSkipTemplate}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Skip Template
          </button>
          <button
            onClick={onUseTemplate}
            disabled={!selectedTemplate}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Use Template
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * Step 3: Export Confirmation
 */
const ExportConfirmationStep = ({
  selectedFormat,
  selectedTemplate,
  useTemplate,
  documentData,
  isExporting,
  exportResult,
  exportError,
  onExport,
  onBack
}) => {
  if (exportResult) {
    return (
      <div className="p-6 text-center">
        <div className="mb-6">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Export Successful!</h3>
          <p className="text-gray-600">{exportResult.message}</p>
        </div>
      </div>
    );
  }

  if (exportError) {
    return (
      <div className="p-6 text-center">
        <div className="mb-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Export Failed</h3>
          <p className="text-red-600 mb-4">{exportError}</p>
          <button
            onClick={onBack}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Confirm Export</h3>

      {/* Export Summary */}
      <div className="bg-gray-50 rounded-lg p-6 mb-6">
        <h4 className="font-medium text-gray-900 mb-4">Export Summary</h4>

        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">Document:</span>
            <span className="font-medium">{documentData.title || 'Untitled Document'}</span>
          </div>

          <div className="flex justify-between">
            <span className="text-gray-600">Format:</span>
            <span className="font-medium uppercase">{selectedFormat}</span>
          </div>

          <div className="flex justify-between">
            <span className="text-gray-600">Cover Template:</span>
            <span className="font-medium">
              {useTemplate && selectedTemplate ? selectedTemplate.name : 'None'}
            </span>
          </div>

          {useTemplate && selectedTemplate && (
            <div className="mt-4 p-4 bg-white rounded border">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  {selectedTemplate.thumbnail_url ? (
                    <img
                      src={selectedTemplate.thumbnail_url}
                      alt={selectedTemplate.name}
                      className="w-12 h-16 object-cover rounded border"
                    />
                  ) : (
                    <div className="w-12 h-16 bg-gray-200 rounded border flex items-center justify-center">
                      <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <h5 className="font-medium text-gray-900">{selectedTemplate.name}</h5>
                  <p className="text-sm text-gray-600 capitalize">{selectedTemplate.category}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Export Actions */}
      <div className="flex items-center justify-between">
        <button
          onClick={onBack}
          disabled={isExporting}
          className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
        >
          Back
        </button>

        <button
          onClick={onExport}
          disabled={isExporting}
          className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors flex items-center space-x-2"
        >
          {isExporting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Exporting...</span>
            </>
          ) : (
            <>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span>Export Document</span>
            </>
          )}
        </button>
      </div>
    </div>
  );
};

/**
 * Template Card Component
 */
const TemplateCard = ({ template, isSelected, onClick, documentData }) => {
  const [imageError, setImageError] = useState(false);

  return (
    <div
      onClick={onClick}
      className={`relative cursor-pointer rounded-lg border-2 transition-all hover:shadow-lg ${
        isSelected
          ? 'border-blue-500 bg-blue-50'
          : 'border-gray-200 hover:border-gray-300'
      }`}
    >
      {/* Template Preview */}
      <div className="aspect-[3/4] bg-gray-100 rounded-t-lg overflow-hidden">
        {template.thumbnail_url && !imageError ? (
          <img
            src={template.thumbnail_url}
            alt={template.name}
            className="w-full h-full object-cover"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
            <div className="text-center p-4">
              <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <p className="text-xs text-gray-500">Preview</p>
            </div>
          </div>
        )}
      </div>

      {/* Template Info */}
      <div className="p-3">
        <h4 className="font-medium text-gray-900 text-sm truncate">{template.name}</h4>
        <p className="text-xs text-gray-600 mt-1 capitalize">{template.category}</p>

        {/* Premium Badge */}
        {template.is_premium && (
          <span className="inline-block mt-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
            Premium
          </span>
        )}
      </div>

      {/* Selection Indicator */}
      {isSelected && (
        <div className="absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
      )}
    </div>
  );
};

export default ExportWithTemplateModal;
