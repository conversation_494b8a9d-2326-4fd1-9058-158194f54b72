import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSidebar } from '../../../contexts/SidebarContext';
import { useAuth } from '../../../contexts/AuthContext';
import QuickActionSidebar from '../../../components/ui/QuickActionSidebar';
import DocumentWorkflowHeader from './DocumentWorkflowHeader';
import DocumentInfoHeader from './DocumentInfoHeader';
import DocumentCanvasMinimal from './DocumentCanvasMinimal';
import ValidationResults from './ValidationResults';
import DocumentStatistics from './DocumentStatistics';
import useReviewMode from '../hooks/useReviewMode';
import useBackgroundOperations from '../hooks/useBackgroundOperations';
import { validateDocument } from '../../../services/documentValidationService';
import Icon from '../../../components/AppIcon';
import { handlePhaseTransition } from '../../../utils/progressUtils';
import { documentStorage } from '../../../services/documentStorageService';

/**
 * DocumentReview - Review phase component for document workflow
 * Provides document validation, quality assessment, and review functionality
 */
const DocumentReview = () => {

  const { documentId } = useParams();
  const navigate = useNavigate();
  const { isCollapsed, contentMargin } = useSidebar();
  const { user, profile } = useAuth();
  const [documentData, setDocumentData] = useState(null);
  const [generatedContent, setGeneratedContent] = useState(null);

  const [validationResults, setValidationResults] = useState(null);
  const [error, setError] = useState(null);

  // Review mode hook for managing review state
  const {
    reviewData,
    updateReviewData,
    updateReviewProgress,
    setValidationResults: setReviewValidationResults,
    completeReview,
    isReviewReadyForCompletion,
    reviewCompletionPercentage
  } = useReviewMode('Review');

  // Background operations tracking for smooth UX
  const {
    hasActiveOperations,
    getPrimaryMessage,
    trackOperation
  } = useBackgroundOperations();

  // Handle template selection navigation
  const handleChooseTemplate = () => {
    console.log('🎨 Navigating to template selection from review');
    navigate(`/document-template/${documentId}`);
  };

  // Navigation handlers for DocumentInfoHeader
  const handleBackToEditClick = () => {
    navigate(`/document-editor/${documentId}`);
  };



  // Load document data from database on component mount
  useEffect(() => {
    const loadDocumentData = async () => {
      try {

        // Try cache first for immediate loading, then refresh from database
        const cachedResult = await documentStorage.loadDocument(documentId, { forceFresh: false });
        
        if (cachedResult.success) {
          console.log('⚡ Review phase: Using cached content for immediate display');
          
          const data = cachedResult.data;
          const documentData = {
            ...data.questionnaire_data,
            generatedContent: data.generated_content,
            lastModified: data.updated_at,
            createdAt: data.created_at,
            documentId: documentId,
            projectId: documentId
          };

          setDocumentData(documentData);
          setGeneratedContent(data.generated_content);
        }

        // Then refresh from database in background for latest data
        const result = await trackOperation(
          'document-refresh',
          'Loading latest changes...',
          () => documentStorage.loadDocument(documentId, { forceFresh: true })
        );

        if (result.success) {
          const data = result.result.data;

          console.log('✅ Review phase: Fresh document loaded from database', {
            documentId,
            source: result.result.source,
            hasGeneratedContent: !!data.generated_content,
            lastModified: data.updated_at,
            hasEditorHTML: !!(data.generated_content?.editorHTML)
          });

          // Convert database format to component format
          const documentData = {
            ...data.questionnaire_data,
            generatedContent: data.generated_content,
            lastModified: data.updated_at,
            createdAt: data.created_at,
            documentId: documentId,
            projectId: documentId
          };

          // Update with fresh data
          setDocumentData(documentData);
          setGeneratedContent(data.generated_content);

          console.log('📄 Review content refreshed:', {
            hasContent: !!data.generated_content,
            hasEditorHTML: !!(data.generated_content?.editorHTML),
            wordCount: data.generated_content?.wordCount,
            chapterCount: data.generated_content?.chapters?.length
          });
        } else {
          console.error('❌ Failed to load document for review:', result.error);
          if (!cachedResult.success) {
            setError('Document not found');
          }
        }
      } catch (error) {
        console.error('❌ Error loading document for review:', error);
        setError('Failed to load document');
      }
    };

    if (documentId) {
      loadDocumentData();

      // Track progress update as background operation
      trackOperation(
        'progress-update',
        'Updating review progress...',
        () => handlePhaseTransition(documentId, 'Review', 'Edit Content')
      ).then(result => {
        if (result.success) {
          console.log('Progress updated to Review phase (75%)');
        } else {
          console.warn('Failed to update progress to Review phase:', result.error);
        }
      });
    }
  }, [documentId]);

  // Auto-validate document when content is loaded (non-blocking)
  useEffect(() => {
    if (documentData && generatedContent && !validationResults) {
      // Run validation in background without blocking UI
      setTimeout(() => {
        handleValidateDocument();
      }, 100); // Small delay to ensure smooth UI transition
    }
  }, [documentData, generatedContent, validationResults]);

  // Handle document validation
  const handleValidateDocument = async () => {
    if (!documentData || !generatedContent) {
      return;
    }

    // Track validation as background operation
    const result = await trackOperation(
      'document-validation',
      'Validating document...',
      () => validateDocument(documentData, generatedContent)
    );

    if (result.success) {
      setValidationResults(result.result);
      setReviewValidationResults(result.result);
      console.log('Validation completed:', result.result);
    } else {
      console.error('Validation failed:', result.error);
      setError('Failed to validate document');
    }
  };

  // Handle phase navigation
  const handlePhaseNavigation = (phase) => {

    switch (phase) {
      case 'Generate':
        // Navigate back to document creator
        navigate('/document-creator');
        break;
      case 'Edit Content':
        navigate(`/document-editor/${documentId}`);
        break;
      case 'Review':
        // Already on review page
        break;
      case 'template':
        // Navigate to template selection page
        navigate(`/document-template/${documentId}`);
        break;
      case 'Publish':
        // Publish phase is no longer accessible from Review - redirect to template selection
        navigate(`/document-template/${documentId}`);
        break;
      default:
    }
  };

  // Handle review completion
  const handleCompleteReview = () => {
    completeReview();
    updateReviewProgress('reviewCompleted', true);
  };



  // Get document title for display
  const getDocumentTitle = () => {
    return documentData?.titleSelection?.selectedTitle || 
           documentData?.title || 
           'Untitled Document';
  };

  if (error) {
    return (
      <div className="min-h-screen bg-background">
        <QuickActionSidebar />
        <main className={`${contentMargin} ml-0`}>
          <DocumentWorkflowHeader
            currentPhase="Review"
            onPhaseClick={handlePhaseNavigation}
          />
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Icon name="AlertCircle" size={48} className="text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Document</h2>
              <p className="text-gray-600 mb-4">{error}</p>
              <button
                onClick={() => navigate('/dashboard')}
                className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
              >
                Return to Dashboard
              </button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (!documentData || !generatedContent) {
    return (
      <div className="min-h-screen bg-background">
        <QuickActionSidebar />
        <main className={`${contentMargin} ml-0`}>
          <DocumentWorkflowHeader
            currentPhase="Review"
            onPhaseClick={handlePhaseNavigation}
          />
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600">Loading document...</p>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <QuickActionSidebar />
      
      <main className={`${contentMargin} ml-0 pt-16`}>
        {/* Document Workflow Header */}
        <DocumentWorkflowHeader
          currentPhase="Review"
          onPhaseClick={handlePhaseNavigation}
        />

        {/* Document Info Header */}
        <DocumentInfoHeader
          documentTitle={generatedContent?.title}
          documentData={documentData}
          generatedContent={generatedContent}
          currentPhase="Review"
          primaryButtonText="Choose Template"
          primaryButtonIcon="Layout"
          primaryButtonAction={handleChooseTemplate}
          secondaryButtonText="Back to Edit"
          secondaryButtonIcon="ArrowLeft"
          secondaryButtonAction={handleBackToEditClick}
          showSaveStatus={true}
          isProcessing={hasActiveOperations}
          processingMessage={getPrimaryMessage() || 'Preparing review...'}
        />

        {/* Review Content */}
        <div className="bg-gray-50 overflow-hidden relative" style={{ height: 'calc(100vh - 10rem)' }}>
          {/* Subtle processing overlay */}
          {hasActiveOperations && (
            <div className="absolute top-0 right-0 m-4 bg-white/90 backdrop-blur-sm border border-gray-200 rounded-lg px-3 py-2 shadow-sm z-10 flex items-center space-x-2">
              <div className="animate-spin h-4 w-4 border-2 border-gray-300 border-t-blue-600 rounded-full"></div>
              <span className="text-sm text-gray-700">{getPrimaryMessage()}</span>
            </div>
          )}
          
          <div className="h-full flex">
            {/* Main Document View */}
            <div className="flex-1 overflow-hidden">
              <DocumentCanvasMinimal
                content={generatedContent}
                isLoading={false}
                isReadOnly={true}
              />
            </div>


          </div>
        </div>
      </main>
    </div>
  );
};

export default DocumentReview;
