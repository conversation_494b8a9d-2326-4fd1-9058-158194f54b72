import React, { useState, useEffect, useCallback } from 'react';
import { 
  extractContentFromEditor, 
  generatePreviewData, 
  updatePreviewWithNewTemplate 
} from '../../../services/previewService.js';

/**
 * Preview Renderer Component
 * Renders template + document content preview with navigation and controls
 */
const PreviewRenderer = ({
  selectedTemplate,
  templates = [],
  documentData = {},
  editorInstance = null,
  onTemplateChange = null,
  onBack = null,
  onDownload = null,
  className = ''
}) => {
  const [previewData, setPreviewData] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [zoomLevel, setZoomLevel] = useState(0.8);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Generate initial preview data
  useEffect(() => {
    if (selectedTemplate && editorInstance) {
      generateInitialPreview();
    }
  }, [selectedTemplate, editorInstance]);

  const generateInitialPreview = async () => {
    setLoading(true);
    setError(null);

    try {
      // Extract content from TipTap editor
      const extractedContent = extractContentFromEditor(editorInstance);
      
      // Generate preview data
      const preview = await generatePreviewData(
        selectedTemplate,
        extractedContent,
        documentData
      );

      setPreviewData(preview);
      setCurrentPage(1); // Start with template page

    } catch (err) {
      console.error('Error generating preview:', err);
      setError('Failed to generate preview');
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateChange = async (newTemplate) => {
    if (!previewData || !newTemplate) return;

    setLoading(true);
    try {
      const updatedPreview = await updatePreviewWithNewTemplate(
        previewData,
        newTemplate,
        documentData
      );

      setPreviewData(updatedPreview);
      setCurrentPage(1); // Return to template page
      
      if (onTemplateChange) {
        onTemplateChange(newTemplate);
      }

    } catch (err) {
      console.error('Error changing template:', err);
      setError('Failed to change template');
    } finally {
      setLoading(false);
    }
  };

  const handleZoomChange = (newZoom) => {
    setZoomLevel(Math.max(0.3, Math.min(2.0, newZoom)));
  };

  const handlePageChange = (pageNumber) => {
    if (previewData && pageNumber >= 1 && pageNumber <= previewData.pages.length) {
      setCurrentPage(pageNumber);
    }
  };

  if (loading) {
    return (
      <div className={`preview-renderer ${className}`}>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Generating preview...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`preview-renderer ${className}`}>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Preview Error</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={generateInitialPreview}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!previewData) {
    return (
      <div className={`preview-renderer ${className}`}>
        <div className="flex items-center justify-center h-96">
          <p className="text-gray-600">No preview data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`preview-renderer flex flex-col h-full ${className}`}>
      {/* Preview Header */}
      <PreviewHeader
        selectedTemplate={selectedTemplate}
        templates={templates}
        currentPage={currentPage}
        totalPages={previewData.pages.length}
        zoomLevel={zoomLevel}
        onTemplateChange={handleTemplateChange}
        onPageChange={handlePageChange}
        onZoomChange={handleZoomChange}
      />

      {/* Preview Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Main Preview Area */}
        <div className="flex-1 flex items-center justify-center bg-gray-100 overflow-auto p-4">
          <PreviewPage
            content={previewData.pages[currentPage - 1]}
            pageNumber={currentPage}
            isTemplate={currentPage === 1}
            zoomLevel={zoomLevel}
          />
        </div>

        {/* Page Thumbnails Sidebar */}
        <div className="w-48 bg-white border-l border-gray-200 overflow-y-auto">
          <PageThumbnails
            pages={previewData.pages}
            currentPage={currentPage}
            onPageSelect={handlePageChange}
            metadata={previewData.metadata}
          />
        </div>
      </div>

      {/* Preview Footer */}
      <PreviewFooter
        previewData={previewData}
        onBack={onBack}
        onDownload={onDownload}
      />
    </div>
  );
};

/**
 * Preview Header Component
 */
const PreviewHeader = ({
  selectedTemplate,
  templates,
  currentPage,
  totalPages,
  zoomLevel,
  onTemplateChange,
  onPageChange,
  onZoomChange
}) => {
  return (
    <div className="preview-header flex items-center justify-between p-4 border-b border-gray-200 bg-white">
      {/* Template Selector */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <label className="text-sm font-medium text-gray-700">Template:</label>
          <select
            value={selectedTemplate?.id || ''}
            onChange={(e) => {
              const template = templates.find(t => t.id === e.target.value);
              if (template) onTemplateChange(template);
            }}
            className="px-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {templates.map(template => (
              <option key={template.id} value={template.id}>
                {template.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Page Navigation */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage <= 1}
            className="p-1 text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          
          <span className="text-sm text-gray-700">
            Page {currentPage} of {totalPages}
          </span>
          
          <button
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage >= totalPages}
            className="p-1 text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        {/* Zoom Controls */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onZoomChange(zoomLevel - 0.1)}
            className="p-1 text-gray-600 hover:text-gray-900"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
            </svg>
          </button>
          
          <span className="text-sm text-gray-700 min-w-12 text-center">
            {Math.round(zoomLevel * 100)}%
          </span>
          
          <button
            onClick={() => onZoomChange(zoomLevel + 0.1)}
            className="p-1 text-gray-600 hover:text-gray-900"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * Preview Page Component
 */
const PreviewPage = ({ content, pageNumber, isTemplate, zoomLevel }) => {
  return (
    <div 
      className="preview-page bg-white shadow-lg border border-gray-300 mx-auto"
      style={{
        transform: `scale(${zoomLevel})`,
        transformOrigin: 'center top',
        width: '8.5in',
        minHeight: '11in',
        maxWidth: '100%'
      }}
    >
      <div className="preview-page-content p-8 h-full">
        <div 
          className={`preview-content ${isTemplate ? 'template-content' : 'document-content'}`}
          dangerouslySetInnerHTML={{ __html: content }}
        />
      </div>
      
      {/* Page Number */}
      <div className="absolute bottom-2 right-2 text-xs text-gray-500 bg-white px-2 py-1 rounded shadow">
        {pageNumber}
      </div>
    </div>
  );
};

/**
 * Page Thumbnails Component
 */
const PageThumbnails = ({ pages, currentPage, onPageSelect, metadata }) => {
  return (
    <div className="page-thumbnails p-3">
      <h4 className="text-sm font-medium text-gray-900 mb-3">Pages</h4>

      <div className="space-y-2">
        {pages.map((page, index) => {
          const pageNumber = index + 1;
          const isTemplate = pageNumber === 1;
          const isSelected = pageNumber === currentPage;

          return (
            <div
              key={pageNumber}
              onClick={() => onPageSelect(pageNumber)}
              className={`page-thumbnail cursor-pointer border-2 rounded transition-all ${
                isSelected
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="aspect-[8.5/11] bg-white p-1">
                <div
                  className="w-full h-full bg-gray-50 border border-gray-100 overflow-hidden text-xs"
                  style={{ fontSize: '6px', lineHeight: '1.2' }}
                >
                  <div
                    className="preview-thumbnail-content p-1"
                    dangerouslySetInnerHTML={{ __html: page.substring(0, 200) + '...' }}
                  />
                </div>
              </div>

              <div className="p-2 text-center">
                <div className="text-xs font-medium text-gray-900">
                  {isTemplate ? 'Cover' : `Page ${pageNumber}`}
                </div>
                {isTemplate && (
                  <div className="text-xs text-blue-600">Template</div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Preview Metadata */}
      {metadata && (
        <div className="mt-4 pt-3 border-t border-gray-200">
          <h5 className="text-xs font-medium text-gray-900 mb-2">Document Info</h5>
          <div className="space-y-1 text-xs text-gray-600">
            <div>Pages: {metadata.totalPages}</div>
            <div>Words: {metadata.content?.wordCount?.toLocaleString() || 0}</div>
            <div>Read time: {metadata.content?.estimatedReadTime || 0} min</div>
            {metadata.template && (
              <div className="pt-1 border-t border-gray-100">
                <div className="font-medium text-gray-700">Template:</div>
                <div>{metadata.template.name}</div>
                <div className="capitalize text-gray-500">{metadata.template.category}</div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Preview Footer Component
 */
const PreviewFooter = ({ previewData, onBack, onDownload }) => {
  return (
    <div className="preview-footer flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
      <div className="flex items-center space-x-4">
        <div className="text-sm text-gray-600">
          {previewData?.metadata?.totalPages || 0} pages • {' '}
          {previewData?.metadata?.content?.wordCount?.toLocaleString() || 0} words
        </div>
      </div>

      <div className="flex items-center space-x-3">
        <button
          onClick={onBack}
          className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Back
        </button>

        <button
          onClick={onDownload}
          className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <span>Download</span>
        </button>
      </div>
    </div>
  );
};

export default PreviewRenderer;
