import React from 'react';
import { NodeViewWrapper } from '@tiptap/react';
import ImageSuggestionCard from './ImageSuggestionCard';

/**
 * ImageSuggestionCardNodeView - React Node View wrapper for Tiptap
 * 
 * This component bridges the gap between Tiptap's node system and React components.
 * It renders the ImageSuggestionCard as a fully interactive React component within
 * the Tiptap editor while maintaining proper editor integration.
 * 
 * Props from Tiptap ReactNodeViewRenderer:
 * - node: The Tiptap node containing attributes and data
 * - editor: The Tiptap editor instance
 * - getPos: Function to get the node's position in the document
 * - updateAttributes: Function to update node attributes
 * - deleteNode: Function to remove this node from the document
 * - selected: <PERSON><PERSON><PERSON> indicating if this node is selected
 * - decorations: Array of decorations applied to this node
 * - extension: Access to the node extension instance
 * - innerDecorations: Internal TipTap prop for nested decorations
 * - view: The ProseMirror EditorView instance
 * - HTMLAttributes: HTML attributes for the node
 */
const ImageSuggestionCardNodeView = (props) => {
  // Debug: Log all props to identify any malformed ones (development only)
  if (process.env.NODE_ENV === 'development') {
    // Complete list of props that TipTap ReactNodeViewRenderer passes to React components
    // Based on official TipTap documentation: https://tiptap.dev/docs/editor/extensions/custom-extensions/node-views/react
    const expectedProps = [
      'node',           // The current node
      'editor',         // Editor instance
      'getPos',         // Get document position
      'updateAttributes', // Update node attributes
      'deleteNode',     // Delete current node
      'selected',       // Selection state
      'decorations',    // Array of decorations (documented)
      'extension',      // Access to the node extension (documented)
      'innerDecorations', // Internal TipTap prop for nested decorations
      'view',           // ProseMirror EditorView instance
      'HTMLAttributes'  // HTML attributes for the node
    ];
    const unexpectedProps = Object.keys(props).filter(key => !expectedProps.includes(key));
    if (unexpectedProps.length > 0) {
      console.warn('ImageSuggestionCardNodeView received truly unexpected props (not from TipTap):', unexpectedProps.join(', '));
      console.log('All props received:', Object.keys(props));
    }
  }

  // Safely extract only the props we need to prevent prop corruption
  const {
    node,
    editor,
    getPos,
    updateAttributes,
    deleteNode,
    selected
  } = props;

  // Extract attributes from the Tiptap node
  const {
    chapterId = '',
    placementId = '',
    searchQuery = '',
    imageCount = 0
  } = node.attrs || {};

  // Check if editor is in read-only mode
  const isReadOnly = editor ? !editor.isEditable : false;

  // If in read-only mode, don't render the card to prevent text injection
  if (isReadOnly) {
    return <NodeViewWrapper className="image-suggestion-card-hidden" />;
  }

  // Handle the "View Images" button click (use ContextualImageSelectionModal for all devices)
  const handleViewImages = (chapterId, placementId, placement) => {
    // Use the ContextualImageSelectionModal system for all devices
    const contextualHandler = editor?.storage?.imageModal?.onContextual;
    const generalHandler = editor?.storage?.imageModal?.onOpen;

    // Prefer contextual handler for proper modal experience
    const handler = contextualHandler || generalHandler;
    
    if (handler) {
      handler(chapterId, placementId, placement);
    } else {
      console.error('No modal handler found in editor.storage.imageModal');
    }
  };

  // Create placement object for compatibility with existing card component
  const placement = {
    id: placementId,
    chapterId: chapterId,
    contextualHint: `Suggested placement for this section`
  };

  return (
    <NodeViewWrapper
      className={`image-suggestion-card-node-view ${selected ? 'ProseMirror-selectednode' : ''}`}
      data-node-type="imageSuggestionCard"
      data-chapter-id={chapterId}
      data-placement-id={placementId}
    >
      {/* Render the actual ImageSuggestionCard component */}
      <ImageSuggestionCard
        chapterId={chapterId}
        placementId={placementId}
        imageCount={imageCount}
        searchQuery={searchQuery}
        placement={placement}
        onViewImages={handleViewImages}
        className={selected ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}
      />
    </NodeViewWrapper>
  );
};

export default ImageSuggestionCardNodeView;
