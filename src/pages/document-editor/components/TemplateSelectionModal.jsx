import React, { useState, useEffect, useMemo } from 'react';
import { fetchCoverTemplates, fetchTemplateCategories, searchTemplates } from '../../../services/templateService.js';
import imageOverlayService from '../../../services/imageOverlayService.js';

/**
 * Template Selection Modal
 * Allows users to browse, search, and select cover templates for their documents
 */
const TemplateSelectionModal = ({ 
  isOpen, 
  onClose, 
  onSelectTemplate, 
  documentData = {} 
}) => {
  const [templates, setTemplates] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  // Load templates and categories on mount
  useEffect(() => {
    if (isOpen) {
      loadTemplatesAndCategories();
    }
  }, [isOpen]);

  // Filter templates based on search and category
  const filteredTemplates = useMemo(() => {
    let filtered = templates;

    if (selectedCategory) {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(template => 
        template.name.toLowerCase().includes(term) ||
        template.description?.toLowerCase().includes(term) ||
        template.tags?.some(tag => tag.toLowerCase().includes(term))
      );
    }

    return filtered;
  }, [templates, selectedCategory, searchTerm]);

  const loadTemplatesAndCategories = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load templates and categories in parallel
      const [templatesResult, categoriesResult] = await Promise.all([
        fetchCoverTemplates({ limit: 100 }),
        fetchTemplateCategories()
      ]);

      if (templatesResult.success) {
        setTemplates(templatesResult.templates);
      } else {
        setError(templatesResult.error);
      }

      if (categoriesResult.success) {
        setCategories(categoriesResult.categories);
      }

    } catch (err) {
      setError('Failed to load templates');
      console.error('Template loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);
  };

  const handleConfirmSelection = () => {
    if (selectedTemplate && onSelectTemplate) {
      onSelectTemplate(selectedTemplate);
      onClose();
    }
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleCategoryChange = (category) => {
    setSelectedCategory(category === selectedCategory ? '' : category);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Select Cover Template</h2>
            <p className="text-gray-600 mt-1">Choose a template for your document cover</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Search and Filters */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search templates..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <svg className="w-5 h-5 text-gray-400 absolute left-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => handleCategoryChange('')}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  selectedCategory === '' 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-white text-gray-700 hover:bg-gray-100'
                }`}
              >
                All
              </button>
              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => handleCategoryChange(category)}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors capitalize ${
                    selectedCategory === category 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-white text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden flex">
          {/* Template Grid */}
          <div className="flex-1 overflow-y-auto p-6">
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
                  <p className="text-gray-600 mt-4">Loading templates...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <p className="text-red-600 mb-4">{error}</p>
                  <button
                    onClick={loadTemplatesAndCategories}
                    className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    Retry
                  </button>
                </div>
              </div>
            ) : filteredTemplates.length === 0 ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <p className="text-gray-600">No templates found</p>
                  {(searchTerm || selectedCategory) && (
                    <button
                      onClick={() => {
                        setSearchTerm('');
                        setSelectedCategory('');
                      }}
                      className="text-blue-500 hover:text-blue-600 mt-2"
                    >
                      Clear filters
                    </button>
                  )}
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {filteredTemplates.map(template => (
                  <TemplateCard
                    key={template.id}
                    template={template}
                    isSelected={selectedTemplate?.id === template.id}
                    onClick={() => handleTemplateSelect(template)}
                    documentData={documentData}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Preview Panel */}
          {selectedTemplate && (
            <div className="w-80 border-l border-gray-200 p-6 bg-gray-50">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Preview</h3>
              <TemplatePreview 
                template={selectedTemplate} 
                documentData={documentData}
              />
              <div className="mt-4">
                <h4 className="font-medium text-gray-900 mb-2">{selectedTemplate.name}</h4>
                <p className="text-sm text-gray-600 mb-4">{selectedTemplate.description}</p>
                <div className="flex flex-wrap gap-1 mb-4">
                  {selectedTemplate.tags?.map(tag => (
                    <span key={tag} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            {filteredTemplates.length} template{filteredTemplates.length !== 1 ? 's' : ''} available
          </div>
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleConfirmSelection}
              disabled={!selectedTemplate}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Use Template
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Individual Template Card Component
 */
const TemplateCard = ({ template, isSelected, onClick, documentData }) => {
  const [imageError, setImageError] = useState(false);

  return (
    <div
      onClick={onClick}
      className={`relative cursor-pointer rounded-lg border-2 transition-all hover:shadow-lg ${
        isSelected
          ? 'border-blue-500 bg-blue-50'
          : 'border-gray-200 hover:border-gray-300'
      }`}
    >
      {/* Template Preview */}
      <div className="aspect-[3/4] bg-gray-100 rounded-t-lg overflow-hidden">
        {template.thumbnail_url && !imageError ? (
          <img
            src={template.thumbnail_url}
            alt={template.name}
            className="w-full h-full object-cover"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
            <div className="text-center p-4">
              <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <p className="text-xs text-gray-500">Preview</p>
            </div>
          </div>
        )}
      </div>

      {/* Template Info */}
      <div className="p-3">
        <h4 className="font-medium text-gray-900 text-sm truncate">{template.name}</h4>
        <p className="text-xs text-gray-600 mt-1 capitalize">{template.category}</p>

        {/* Premium Badge */}
        {template.is_premium && (
          <span className="inline-block mt-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
            Premium
          </span>
        )}
      </div>

      {/* Selection Indicator */}
      {isSelected && (
        <div className="absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
      )}
    </div>
  );
};

/**
 * Template Preview Component
 */
const TemplatePreview = ({ template, documentData }) => {
  const [previewError, setPreviewError] = useState(false);

  const [previewImage, setPreviewImage] = React.useState(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(false);

  React.useEffect(() => {
    const generatePreview = async () => {
      try {
        setLoading(true);
        setError(false);

        const canvas = await imageOverlayService.renderTemplate(template, documentData);
        const imageData = imageOverlayService.exportAsImage(canvas, 'png', 0.8);
        setPreviewImage(imageData);
      } catch (error) {
        console.error('Error generating template preview:', error);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    if (template && template.background_image_url) {
      generatePreview();
    }
  }, [template, documentData]);

  return (
    <div className="bg-white rounded-lg p-4 shadow-sm">
      <div className="flex justify-center">
        {loading ? (
          <div className="w-32 h-40 bg-gray-100 border border-gray-200 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <span className="text-gray-500 text-xs">Loading...</span>
            </div>
          </div>
        ) : error ? (
          <div className="w-32 h-40 bg-gray-100 border border-gray-200 rounded-lg flex items-center justify-center">
            <div className="text-center p-2">
              <svg className="w-6 h-6 text-gray-400 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <span className="text-gray-500 text-xs">Preview Error</span>
            </div>
          </div>
        ) : previewImage ? (
          <img
            src={previewImage}
            alt={template.name}
            className="border border-gray-200 rounded-lg max-w-full h-auto shadow-sm"
            style={{ maxHeight: '200px', transform: 'scale(0.4)', transformOrigin: 'center' }}
          />
        ) : (
          <div className="w-32 h-40 bg-gray-100 border border-gray-200 rounded-lg flex items-center justify-center">
            <span className="text-gray-500 text-sm">No Preview</span>
          </div>
        )}
      </div>
    </div>
  );
  } catch (error) {
    console.error('Template preview error:', error);
    setPreviewError(true);

    return (
      <div className="bg-gray-100 rounded-lg p-4 flex items-center justify-center h-48">
        <div className="text-center">
          <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <p className="text-sm text-gray-500">Preview unavailable</p>
        </div>
      </div>
    );
  }
};

export default TemplateSelectionModal;
