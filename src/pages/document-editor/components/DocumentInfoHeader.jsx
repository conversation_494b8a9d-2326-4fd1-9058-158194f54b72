import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import CloudSaveIndicator from '../../../components/ui/CloudSaveIndicator';

/**
 * DocumentInfoHeader - Header component showing document information and action buttons
 * Displays document title, metadata, and provides quick access to key actions like Review, Export, and Zoom
 * Responsive design optimized for mobile, tablet, and desktop views
 */
const DocumentInfoHeader = ({
  documentTitle = 'Untitled Document',
  documentData = null,
  generatedContent = null,
  currentPhase = 'Edit Content',
  onReviewClick = null,
  onExportClick = null,
  onManualSave = null,
  saveStatus = 'saved',
  lastSaved = null,
  className = '',
  // New props for customizing buttons based on phase
  primaryButtonText = null,
  primaryButtonIcon = null,
  primaryButtonAction = null,
  showPrimaryButton = true, // New prop to control primary button visibility
  secondaryButtonText = null,
  secondaryButtonIcon = null,
  secondaryButtonAction = null,
  // Prop to control save status visibility
  showSaveStatus = true,
  // Prop for showing background processing indicator
  isProcessing = false,
  processingMessage = 'Preparing review...',
  // Template navigation props
  onChooseTemplate = null,
  showChooseTemplate = false
}) => {
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  // Utility function to format time ago
  const formatTimeAgo = (date) => {
    if (!date) return '';
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 10) return 'just now';
    if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    return `${Math.floor(diffInSeconds / 86400)} days ago`;
  };

  // Get document title from various sources
  const getDocumentTitle = () => {
    if (generatedContent?.title) return generatedContent.title;
    if (documentData?.documentPurpose?.title) return documentData.documentPurpose.title;
    return documentTitle;
  };

  // Export action - now opens the export modal instead of direct export
  const handleExportClick = () => {
    console.log('Export clicked, onExportClick:', !!onExportClick);
    onExportClick?.(); // Call without format parameter to open modal
  };

  return (
    <div className={`w-full bg-white border-b border-gray-200 shadow-sm ${className}`}>
      <div className="px-4 sm:px-6 lg:px-8 py-3">
        <div className="flex items-center justify-between">
          {/* Left Side - Document Title */}
          <div className="flex-1 min-w-0">
            <h1 className="text-lg sm:text-xl font-semibold text-gray-900 truncate">
              {getDocumentTitle()}
            </h1>
            {/* Document metadata - hidden on mobile */}
            <div className="hidden sm:flex items-center space-x-4 mt-1 text-sm text-gray-500">
              {generatedContent?.metadata?.generatedAt && (
                <span className="flex items-center space-x-1">
                  <Icon name="Calendar" size={14} />
                  <span>{new Date(generatedContent.metadata.generatedAt).toLocaleDateString()}</span>
                </span>
              )}
            </div>
          </div>

          {/* Right Side - Action Buttons */}
          <div className="flex items-center space-x-2 ml-4">
            {/* Desktop Actions */}
            <div className="hidden md:flex items-center space-x-2">
              {/* Auto-save status indicator - only show if showSaveStatus is true */}
              {showSaveStatus && (
                <div className="flex items-center space-x-2 mr-2">
                  <CloudSaveIndicator
                    size="small"
                    showCheckmark={saveStatus === 'saved' && !isProcessing}
                    title={
                      isProcessing ? processingMessage :
                      saveStatus === 'saving' ? 'Saving document...' :
                      saveStatus === 'saved' ? 'Document saved' :
                      'Save failed - please try again'
                    }
                    color="text-gray-600"
                  />
                  {isProcessing && (
                    <span className="text-xs text-gray-500 flex items-center">
                      <div className="animate-spin h-3 w-3 border border-gray-300 border-t-blue-600 rounded-full mr-1"></div>
                      {processingMessage}
                    </span>
                  )}
                  {!isProcessing && saveStatus === 'saved' && lastSaved && (
                    <span className="text-xs text-gray-500">
                      Saved {formatTimeAgo(lastSaved)}
                    </span>
                  )}
                  {!isProcessing && saveStatus === 'saving' && (
                    <span className="text-xs text-gray-500">
                      Saving...
                    </span>
                  )}
                  {!isProcessing && saveStatus === 'error' && (
                    <span className="text-xs text-gray-500">
                      Save failed
                    </span>
                  )}
                </div>
              )}

              {/* Manual Save Button - Only show in Edit Content phase */}
              {currentPhase === 'Edit Content' && onManualSave && (
                <Button
                  variant={saveStatus === 'error' ? 'outline' : 'ghost'}
                  onClick={onManualSave}
                  disabled={saveStatus === 'saving'}
                  className={`px-4 py-2.5 text-sm font-medium touch-manipulation transition-all duration-200 ${
                    saveStatus === 'error' ? 'border-red-300 text-red-600 hover:bg-red-50' :
                    saveStatus === 'saved' ? 'text-green-600 hover:bg-green-50' :
                    'hover:bg-gray-50'
                  }`}
                  title={
                    saveStatus === 'saving' ? 'Saving document...' :
                    saveStatus === 'error' ? 'Save failed - click to retry' :
                    saveStatus === 'saved' ? 'Document saved - click to save again' :
                    'Save document'
                  }
                >
                  <Icon
                    name={
                      saveStatus === 'saving' ? "Loader2" :
                      saveStatus === 'error' ? "AlertCircle" :
                      "Save"
                    }
                    size={16}
                    className={`mr-2 ${saveStatus === 'saving' ? 'animate-spin' : ''}`}
                  />
                  {
                    saveStatus === 'saving' ? 'Saving...' :
                    saveStatus === 'error' ? 'Retry' :
                    'Save'
                  }
                </Button>
              )}

              {/* Secondary Action Button (Back/Navigation) */}
              {secondaryButtonAction && (
                <Button
                  variant="outline"
                  onClick={secondaryButtonAction}
                  className="px-4 py-2.5 text-sm font-medium touch-manipulation"
                >
                  <Icon name={secondaryButtonIcon || "ArrowLeft"} size={16} className="mr-2" />
                  {secondaryButtonText || "Back"}
                </Button>
              )}

              {/* Primary Action Button */}
              <Button
                onClick={primaryButtonAction || onReviewClick}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2.5 text-sm font-medium touch-manipulation"
              >
                <Icon name={primaryButtonIcon || "Eye"} size={16} className="mr-2" />
                {primaryButtonText || "Review"}
              </Button>

              {/* Choose Template Button (when review is complete) */}
              {showChooseTemplate && onChooseTemplate && (
                <Button
                  variant="primary"
                  onClick={onChooseTemplate}
                  className="px-4 py-2 text-sm font-medium"
                >
                  <Icon name="Layout" size={16} className="mr-2" />
                  Choose Template
                </Button>
              )}

              {/* Export Button (fallback when no secondary action) */}
              {!secondaryButtonAction && !showChooseTemplate && (
                <Button
                  variant="outline"
                  onClick={handleExportClick}
                  className="px-3 py-2 text-sm font-medium"
                >
                  <Icon name="Download" size={16} className="mr-2" />
                  Export
                </Button>
              )}

            </div>

            {/* Tablet Actions - Touch-friendly sizing */}
            <div className="hidden sm:flex md:hidden items-center space-x-2">
              {/* Auto-save status indicator for tablet - only show if showSaveStatus is true */}
              {showSaveStatus && (
                <div className="flex items-center space-x-2 mr-2">
                  <CloudSaveIndicator
                    size="small"
                    showCheckmark={saveStatus === 'saved'}
                    title={
                      saveStatus === 'saving' ? 'Saving document...' :
                      saveStatus === 'saved' ? 'Document saved' :
                      'Save failed - please try again'
                    }
                    color="text-gray-600"
                  />
                </div>
              )}

              {/* Manual Save Button - Only show in Edit Content phase (Tablet) */}
              {currentPhase === 'Edit Content' && onManualSave && (
                <Button
                  variant={saveStatus === 'error' ? 'outline' : 'ghost'}
                  onClick={onManualSave}
                  disabled={saveStatus === 'saving'}
                  className={`px-4 py-2.5 text-sm font-medium touch-manipulation transition-all duration-200 ${
                    saveStatus === 'error' ? 'border-red-300 text-red-600 hover:bg-red-50' :
                    saveStatus === 'saved' ? 'text-green-600 hover:bg-green-50' :
                    'hover:bg-gray-50'
                  }`}
                  title={
                    saveStatus === 'saving' ? 'Saving document...' :
                    saveStatus === 'error' ? 'Save failed - click to retry' :
                    saveStatus === 'saved' ? 'Document saved - click to save again' :
                    'Save document'
                  }
                >
                  <Icon
                    name={
                      saveStatus === 'saving' ? "Loader2" :
                      saveStatus === 'error' ? "AlertCircle" :
                      "Save"
                    }
                    size={16}
                    className={saveStatus === 'saving' ? 'animate-spin' : ''}
                  />
                </Button>
              )}

              {/* Secondary Action Button (Back/Navigation) */}
              {secondaryButtonAction && (
                <Button
                  variant="outline"
                  onClick={secondaryButtonAction}
                  className="px-4 py-2.5 text-sm font-medium touch-manipulation"
                >
                  <Icon name={secondaryButtonIcon || "ArrowLeft"} size={16} />
                </Button>
              )}

              {/* Primary Action Button */}
              <Button
                onClick={primaryButtonAction || onReviewClick}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2.5 text-sm font-medium touch-manipulation"
              >
                <Icon name={primaryButtonIcon || "Eye"} size={16} />
              </Button>

              {/* Choose Template Button (when review is complete) */}
              {showChooseTemplate && onChooseTemplate && (
                <Button
                  variant="primary"
                  onClick={onChooseTemplate}
                  className="px-4 py-2.5 touch-manipulation"
                >
                  <Icon name="Layout" size={16} />
                </Button>
              )}

              {/* Export Button (fallback when no secondary action) */}
              {!secondaryButtonAction && !showChooseTemplate && (
                <Button
                  variant="outline"
                  onClick={handleExportClick}
                  className="px-4 py-2.5 touch-manipulation"
                >
                  <Icon name="Download" size={16} />
                </Button>
              )}

            </div>

            {/* Mobile Menu Button - Touch-friendly */}
            <div className="sm:hidden relative">
              <Button
                variant="ghost"
                onClick={() => setShowMobileMenu(!showMobileMenu)}
                className="p-3 touch-manipulation"
                aria-label="More options"
              >
                <Icon name="MoreVertical" size={20} />
              </Button>

              {/* Mobile Menu - Overlay dropdown */}
              {showMobileMenu && (
                <div className="absolute top-full right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-1100">
                  <div className="flex flex-col">
                    {/* Manual Save Button - Only show in Edit Content phase (Mobile) */}
                    {currentPhase === 'Edit Content' && onManualSave && (
                      <button
                        onClick={() => {
                          onManualSave?.();
                          setShowMobileMenu(false);
                        }}
                        disabled={saveStatus === 'saving'}
                        className={`w-full text-left px-3 py-2.5 text-sm flex items-center touch-manipulation disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ${
                          saveStatus === 'error' ? 'text-red-600 hover:bg-red-50' :
                          saveStatus === 'saved' ? 'text-green-600 hover:bg-green-50' :
                          'text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        <Icon
                          name={
                            saveStatus === 'saving' ? "Loader2" :
                            saveStatus === 'error' ? "AlertCircle" :
                            "Save"
                          }
                          size={16}
                          className={`mr-3 ${saveStatus === 'saving' ? 'animate-spin' : ''}`}
                        />
                        {
                          saveStatus === 'saving' ? 'Saving...' :
                          saveStatus === 'error' ? 'Retry Save' :
                          'Save'
                        }
                      </button>
                    )}

                    {/* Secondary Action Button (Back/Navigation) */}
                    {secondaryButtonAction && (
                      <button
                        onClick={() => {
                          secondaryButtonAction?.();
                          setShowMobileMenu(false);
                        }}
                        className="w-full text-left px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 flex items-center touch-manipulation"
                      >
                        <Icon name={secondaryButtonIcon || "ArrowLeft"} size={16} className="mr-3" />
                        {secondaryButtonText || "Back"}
                      </button>
                    )}

                    {/* Primary Action Button */}
                    <button
                      onClick={() => {
                        (primaryButtonAction || onReviewClick)?.();
                        setShowMobileMenu(false);
                      }}
                      className="w-full text-left px-3 py-2.5 text-sm text-blue-600 hover:bg-blue-50 flex items-center touch-manipulation"
                    >
                      <Icon name={primaryButtonIcon || "Eye"} size={16} className="mr-3" />
                      {primaryButtonText || "Review"}
                    </button>

                    {/* Choose Template Button (when review is complete) */}
                    {showChooseTemplate && onChooseTemplate && (
                      <button
                        onClick={() => {
                          onChooseTemplate();
                          setShowMobileMenu(false);
                        }}
                        className="w-full text-left px-3 py-2.5 text-sm text-blue-700 hover:bg-blue-50 flex items-center touch-manipulation font-medium"
                      >
                        <Icon name="Layout" size={16} className="mr-3" />
                        Choose Template
                      </button>
                    )}

                    {/* Export Button (fallback when no secondary action) */}
                    {!secondaryButtonAction && !showChooseTemplate && (
                      <button
                        onClick={() => {
                          handleExportClick();
                          setShowMobileMenu(false);
                        }}
                        className="w-full text-left px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 flex items-center touch-manipulation"
                      >
                        <Icon name="Download" size={16} className="mr-3" />
                        Export
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Backdrop overlay to close mobile menu */}
        {showMobileMenu && (
          <div
            className="fixed inset-0 z-1090"
            onClick={() => setShowMobileMenu(false)}
          />
        )}
      </div>
    </div>
  );
};

export default DocumentInfoHeader;
