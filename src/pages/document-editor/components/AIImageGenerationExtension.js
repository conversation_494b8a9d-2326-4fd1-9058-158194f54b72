/**
 * AI Image Generation Extension for Tiptap
 *
 * This extension provides commands and functionality for AI-generated image insertion
 * and management within the Tiptap editor. It integrates with the existing
 * EnhancedImageExtension to provide a seamless experience.
 */

import { Extension } from "@tiptap/core";

/**
 * AI Image Generation Extension
 *
 * Provides commands for:
 * - Inserting AI-generated images
 * - Managing generation state
 * - Handling generation metadata
 */
export const AIImageGenerationExtension = Extension.create({
  name: "aiImageGeneration",

  // Add storage for generation state and metadata
  addStorage() {
    return {
      // Track active generation requests
      activeGenerations: new Map(),

      // Store generation history for undo/redo
      generationHistory: [],

      // Configuration and settings
      config: {
        maxConcurrentGenerations: 3,
        enableGenerationHistory: true,
        autoInsertOnGeneration: true,
      },

      // Callbacks for generation events
      callbacks: {
        onGenerationStart: null,
        onGenerationProgress: null,
        onGenerationComplete: null,
        onGenerationError: null,
      },
    };
  },

  // Add commands for AI image generation
  addCommands() {
    return {
      /**
       * Start AI image generation process
       * @param {Object} options - Generation options
       * @param {string} options.prompt - Text prompt for generation
       * @param {string} options.style - Image style (photorealistic, artistic, etc.)
       * @param {string} options.size - Image size (landscape, portrait, etc.)
       * @param {Object} options.advanced - Advanced generation options
       * @param {Function} options.onProgress - Progress callback
       * @returns {boolean} Success status
       */
      startImageGeneration:
        (options) =>
        ({ editor, commands }) => {
          try {
            const {
              prompt,
              style = "photorealistic",
              size = "landscape",
              advanced = {},
              onProgress,
            } = options;

            if (
              !prompt ||
              typeof prompt !== "string" ||
              prompt.trim().length === 0
            ) {
              console.error("AIImageGeneration: Valid prompt is required");
              return false;
            }

            // Check concurrent generation limit
            const storage = editor.storage.aiImageGeneration;
            if (
              storage.activeGenerations.size >=
              storage.config.maxConcurrentGenerations
            ) {
              console.warn(
                "AIImageGeneration: Maximum concurrent generations reached"
              );
              return false;
            }

            // Generate unique ID for this generation request
            const generationId = `gen_${Date.now()}_${Math.random()
              .toString(36)
              .substr(2, 9)}`;

            // Store generation metadata
            const generationData = {
              id: generationId,
              prompt,
              style,
              size,
              advanced,
              startTime: Date.now(),
              status: "starting",
              progress: 0,
            };

            storage.activeGenerations.set(generationId, generationData);

            // Call generation start callback
            if (storage.callbacks.onGenerationStart) {
              storage.callbacks.onGenerationStart(generationData);
            }

            console.log("AIImageGeneration: Started generation", {
              generationId,
              prompt: prompt.substring(0, 50),
            });
            return true;
          } catch (error) {
            console.error(
              "AIImageGeneration: Failed to start generation",
              error
            );
            return false;
          }
        },

      /**
       * Update generation progress
       * @param {string} generationId - Generation ID
       * @param {Object} progressData - Progress information
       * @returns {boolean} Success status
       */
      updateGenerationProgress:
        (generationId, progressData) =>
        ({ editor }) => {
          try {
            const storage = editor.storage.aiImageGeneration;
            const generation = storage.activeGenerations.get(generationId);

            if (!generation) {
              console.warn(
                "AIImageGeneration: Generation not found for progress update",
                generationId
              );
              return false;
            }

            // Update generation data
            Object.assign(generation, {
              ...progressData,
              lastUpdate: Date.now(),
            });

            // Call progress callback
            if (storage.callbacks.onGenerationProgress) {
              storage.callbacks.onGenerationProgress(generation);
            }

            return true;
          } catch (error) {
            console.error(
              "AIImageGeneration: Failed to update progress",
              error
            );
            return false;
          }
        },

      /**
       * Complete image generation and insert into editor
       * @param {string} generationId - Generation ID
       * @param {Object} result - Generation result
       * @param {string} result.imageUrl - Generated image URL
       * @param {Object} result.metadata - Generation metadata
       * @returns {boolean} Success status
       */
      completeImageGeneration:
        (generationId, result) =>
        ({ editor, commands }) => {
          try {
            const storage = editor.storage.aiImageGeneration;
            const generation = storage.activeGenerations.get(generationId);

            if (!generation) {
              console.warn(
                "AIImageGeneration: Generation not found for completion",
                generationId
              );
              return false;
            }

            const { imageUrl, metadata = {} } = result;

            if (!imageUrl) {
              console.error(
                "AIImageGeneration: No image URL provided for completion"
              );
              return false;
            }

            // Update generation status
            generation.status = "completed";
            generation.completedAt = Date.now();
            generation.duration = generation.completedAt - generation.startTime;
            generation.result = result;

            // Insert image into editor using enhanced image extension
            const insertSuccess = commands.setEnhancedImage({
              src: imageUrl,
              alt: `AI generated image: ${generation.prompt.substring(0, 100)}`,
              title: `Generated with ${generation.style} style`,
              "data-ai-generated": "true",
              "data-generation-id": generationId,
              "data-prompt": generation.prompt,
              "data-style": generation.style,
              "data-size": generation.size,
            });

            if (insertSuccess) {
              // Add to generation history if enabled
              if (storage.config.enableGenerationHistory) {
                storage.generationHistory.push({
                  ...generation,
                  insertedAt: Date.now(),
                });

                // Limit history size (keep last 50 generations)
                if (storage.generationHistory.length > 50) {
                  storage.generationHistory =
                    storage.generationHistory.slice(-50);
                }
              }

              // Call completion callback
              if (storage.callbacks.onGenerationComplete) {
                storage.callbacks.onGenerationComplete(generation);
              }

              console.log(
                "AIImageGeneration: Successfully completed and inserted",
                {
                  generationId,
                  duration: generation.duration,
                  imageUrl: imageUrl.substring(0, 50),
                }
              );
            }

            // Remove from active generations
            storage.activeGenerations.delete(generationId);

            return insertSuccess;
          } catch (error) {
            console.error(
              "AIImageGeneration: Failed to complete generation",
              error
            );
            return false;
          }
        },

      /**
       * Handle generation error
       * @param {string} generationId - Generation ID
       * @param {Error} error - Error object
       * @returns {boolean} Success status
       */
      handleGenerationError:
        (generationId, error) =>
        ({ editor }) => {
          try {
            const storage = editor.storage.aiImageGeneration;
            const generation = storage.activeGenerations.get(generationId);

            if (generation) {
              generation.status = "failed";
              generation.error = error.message || "Unknown error";
              generation.failedAt = Date.now();
              generation.duration = generation.failedAt - generation.startTime;

              // Call error callback
              if (storage.callbacks.onGenerationError) {
                storage.callbacks.onGenerationError(generation, error);
              }

              console.error("AIImageGeneration: Generation failed", {
                generationId,
                error: error.message,
                duration: generation.duration,
              });

              // Remove from active generations
              storage.activeGenerations.delete(generationId);
            }

            return true;
          } catch (err) {
            console.error(
              "AIImageGeneration: Failed to handle generation error",
              err
            );
            return false;
          }
        },

      /**
       * Cancel active generation
       * @param {string} generationId - Generation ID
       * @returns {boolean} Success status
       */
      cancelImageGeneration:
        (generationId) =>
        ({ editor }) => {
          try {
            const storage = editor.storage.aiImageGeneration;
            const generation = storage.activeGenerations.get(generationId);

            if (generation) {
              generation.status = "cancelled";
              generation.cancelledAt = Date.now();
              generation.duration =
                generation.cancelledAt - generation.startTime;

              console.log("AIImageGeneration: Generation cancelled", {
                generationId,
                duration: generation.duration,
              });

              // Remove from active generations
              storage.activeGenerations.delete(generationId);
            }

            return true;
          } catch (error) {
            console.error(
              "AIImageGeneration: Failed to cancel generation",
              error
            );
            return false;
          }
        },

      /**
       * Get active generations
       * @returns {Array} Array of active generation data
       */
      getActiveGenerations:
        () =>
        ({ editor }) => {
          const storage = editor.storage.aiImageGeneration;
          return Array.from(storage.activeGenerations.values());
        },

      /**
       * Get generation history
       * @returns {Array} Array of completed generations
       */
      getGenerationHistory:
        () =>
        ({ editor }) => {
          const storage = editor.storage.aiImageGeneration;
          return [...storage.generationHistory];
        },

      /**
       * Clear generation history
       * @returns {boolean} Success status
       */
      clearGenerationHistory:
        () =>
        ({ editor }) => {
          const storage = editor.storage.aiImageGeneration;
          storage.generationHistory = [];
          return true;
        },
    };
  },

  // Add global attributes for AI-generated images
  addGlobalAttributes() {
    return [
      {
        types: ["enhancedImage"],
        attributes: {
          "data-ai-generated": {
            default: null,
            parseHTML: (element) => element.getAttribute("data-ai-generated"),
            renderHTML: (attributes) => {
              if (!attributes["data-ai-generated"]) {
                return {};
              }
              return { "data-ai-generated": attributes["data-ai-generated"] };
            },
          },
          "data-generation-id": {
            default: null,
            parseHTML: (element) => element.getAttribute("data-generation-id"),
            renderHTML: (attributes) => {
              if (!attributes["data-generation-id"]) {
                return {};
              }
              return { "data-generation-id": attributes["data-generation-id"] };
            },
          },
          "data-prompt": {
            default: null,
            parseHTML: (element) => element.getAttribute("data-prompt"),
            renderHTML: (attributes) => {
              if (!attributes["data-prompt"]) {
                return {};
              }
              return { "data-prompt": attributes["data-prompt"] };
            },
          },
          "data-style": {
            default: null,
            parseHTML: (element) => element.getAttribute("data-style"),
            renderHTML: (attributes) => {
              if (!attributes["data-style"]) {
                return {};
              }
              return { "data-style": attributes["data-style"] };
            },
          },
          "data-size": {
            default: null,
            parseHTML: (element) => element.getAttribute("data-size"),
            renderHTML: (attributes) => {
              if (!attributes["data-size"]) {
                return {};
              }
              return { "data-size": attributes["data-size"] };
            },
          },
        },
      },
    ];
  },

  // Initialize extension
  onCreate() {
    console.log("AIImageGeneration: Extension initialized");
  },

  // Cleanup on destroy
  onDestroy() {
    const storage = this.storage.aiImageGeneration;
    if (storage) {
      // Cancel any active generations
      storage.activeGenerations.clear();
      console.log(
        "AIImageGeneration: Extension destroyed, active generations cleared"
      );
    }
  },
});

export default AIImageGenerationExtension;
