import { useState, useCallback, useMemo } from 'react';

/**
 * Custom hook for managing Review Mode state and operations
 * Provides centralized state management for the Review phase
 */
export const useReviewMode = (initialPhase = 'Edit Content') => {
  const [currentPhase, setCurrentPhase] = useState(initialPhase);
  const [reviewData, setReviewData] = useState({
    validationResults: null,

    exportReadiness: null,
    reviewProgress: {
      contentReviewed: false,

      exportPreviewed: false,
      reviewCompleted: false
    },
    lastReviewDate: null,
    qualityScore: null,
    issues: []
  });

  // Derived states
  const isReviewMode = useMemo(() => currentPhase === 'Review', [currentPhase]);
  const isEditMode = useMemo(() => currentPhase === 'Edit Content', [currentPhase]);
  const isPublishMode = useMemo(() => currentPhase === 'Publish', [currentPhase]);
  
  // Calculate review completion percentage
  const reviewCompletionPercentage = useMemo(() => {
    const progress = reviewData.reviewProgress;
    const completedSteps = Object.values(progress).filter(Boolean).length;
    const totalSteps = Object.keys(progress).length;
    return Math.round((completedSteps / totalSteps) * 100);
  }, [reviewData.reviewProgress]);

  // Enter review mode
  const enterReviewMode = useCallback((documentData, generatedContent, documentId) => {
    console.log('Entering Review mode');

    // Note: Document saving is now handled by the database storage service
    // No localStorage operations needed here

    // Switch to review phase
    setCurrentPhase('Review');

    // Initialize review data if not already done
    setReviewData(prev => ({
      ...prev,
      lastReviewDate: new Date().toISOString()
    }));
  }, []);

  // Exit review mode (return to edit)
  const exitReviewMode = useCallback(() => {
    console.log('Exiting Review mode, returning to Edit Content');
    setCurrentPhase('Edit Content');
  }, []);

  // Enter publish mode
  const enterPublishMode = useCallback((documentData, generatedContent, documentId) => {
    console.log('Entering Publish mode');

    // Save current state before switching to publish
    if (generatedContent && documentId) {
      const documentToSave = {
        ...documentData,
        generatedContent: generatedContent,
        lastModified: new Date().toISOString()
      };
      localStorage.setItem(`document-${documentId}`, JSON.stringify(documentToSave));
    }

    // Switch to publish phase
    setCurrentPhase('Publish');
  }, []);

  // Exit publish mode
  const exitPublishMode = useCallback(() => {
    console.log('Exiting Publish mode');
    setCurrentPhase('Review');
  }, []);

  // Update review data
  const updateReviewData = useCallback((updates) => {
    setReviewData(prev => ({
      ...prev,
      ...updates
    }));
  }, []);

  // Update review progress
  const updateReviewProgress = useCallback((progressKey, completed = true) => {
    setReviewData(prev => ({
      ...prev,
      reviewProgress: {
        ...prev.reviewProgress,
        [progressKey]: completed
      }
    }));
  }, []);

  // Mark validation results
  const setValidationResults = useCallback((results) => {
    updateReviewData({ validationResults: results });
    updateReviewProgress('contentReviewed', true);
  }, [updateReviewData, updateReviewProgress]);



  // Mark export readiness
  const setExportReadiness = useCallback((readiness) => {
    updateReviewData({ exportReadiness: readiness });
    updateReviewProgress('exportPreviewed', true);
  }, [updateReviewData, updateReviewProgress]);

  // Complete review
  const completeReview = useCallback(() => {
    updateReviewProgress('reviewCompleted', true);
    console.log('Review marked as complete');
  }, [updateReviewProgress]);

  // Reset review data
  const resetReviewData = useCallback(() => {
    setReviewData({
      validationResults: null,

      exportReadiness: null,
      reviewProgress: {
        contentReviewed: false,

        exportPreviewed: false,
        reviewCompleted: false
      },
      lastReviewDate: null,
      qualityScore: null,
      issues: []
    });
  }, []);

  // Check if review is ready for completion
  const isReviewReadyForCompletion = useMemo(() => {
    const progress = reviewData.reviewProgress;
    return progress.contentReviewed && progress.exportPreviewed;
  }, [reviewData.reviewProgress]);

  return {
    // State
    currentPhase,
    reviewData,
    isReviewMode,
    isEditMode,
    isPublishMode,
    reviewCompletionPercentage,
    isReviewReadyForCompletion,

    // Actions
    setCurrentPhase,
    enterReviewMode,
    exitReviewMode,
    enterPublishMode,
    exitPublishMode,
    updateReviewData,
    updateReviewProgress,
    setValidationResults,

    setExportReadiness,
    completeReview,
    resetReviewData
  };
};

export default useReviewMode;
