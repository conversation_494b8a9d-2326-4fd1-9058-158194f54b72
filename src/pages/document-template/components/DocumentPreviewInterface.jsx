import React, { useCallback } from 'react';
import { getTipTapStyles } from '../../../services/enhancedContentExtraction.js';
import '../../../styles/document-preview-mobile.css';
import '../../../styles/preview-pagination.css';

/**
 * Document Preview Interface
 * Shows combined template + formatted document content with navigation controls
 *
 * NOTE: This component is now primarily used for full document previews in export contexts.
 * For template selection, use CoverPreviewInterface for better performance.
 */
const DocumentPreviewInterface = ({
  selectedTemplate = null,
  templates = [],
  documentData = {},
  generatedContent = {},
  previewData = null,
  loading = false,
  error = null,
  onTemplateChange = null,
  onBack = null,
  onExport = null,
  className = ''
}) => {
  // Zoom functionality disabled to simplify container sizing

  const handleTemplateChange = useCallback((newTemplate) => {
    if (onTemplateChange && newTemplate.id !== selectedTemplate?.id) {
      onTemplateChange(newTemplate);
    }
  }, [onTemplateChange, selectedTemplate]);

  if (loading) {
    return (
      <div className={`document-preview-interface ${className}`}>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Generating preview...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`document-preview-interface ${className}`}>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Preview Error</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={onBack}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Back to Templates
            </button>
          </div>
        </div>
      </div>
    );
  }

  // CRITICAL DEBUG: Log preview data structure
  console.log('🔍 DOCUMENT PREVIEW INTERFACE DEBUG: Received preview data', {
    hasPreviewData: !!previewData,
    previewDataType: typeof previewData,
    previewDataKeys: previewData ? Object.keys(previewData) : 'N/A',
    hasPages: !!previewData?.pages,
    pagesType: typeof previewData?.pages,
    pagesLength: previewData?.pages?.length || 0,
    pagesIsArray: Array.isArray(previewData?.pages),
    metadata: previewData?.metadata,
    firstPageLength: previewData?.pages?.[0]?.length || 0,
    secondPageLength: previewData?.pages?.[1]?.length || 0
  });

  if (!previewData || !previewData.pages || previewData.pages.length === 0) {
    console.log('🔍 DOCUMENT PREVIEW INTERFACE DEBUG: No preview data available');
    return (
      <div className={`document-preview-interface ${className}`}>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Preview Available</h3>
            <p className="text-gray-600">Please select a template to generate preview</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`document-preview-interface flex flex-col h-full ${className}`}>
      {/* Preview Header */}
      <PreviewHeader
        selectedTemplate={selectedTemplate}
        templates={templates}
        onTemplateChange={handleTemplateChange}
        onBack={onBack}
        onExport={onExport}
      />

      {/* Preview Content */}
      <div className="flex-1 bg-gray-100 overflow-auto">
        <ContinuousView
          pages={previewData.pages}
          metadata={previewData.metadata}
        />
      </div>


    </div>
  );
};

/**
 * Preview Header Component
 */
const PreviewHeader = ({
  selectedTemplate,
  templates,
  onTemplateChange,
  onBack,
  onExport
}) => {
  return (
    <div className="preview-header bg-white border-b border-gray-200">
      {/* Mobile-First Responsive Layout */}
      <div className="p-3 md:p-4">
        {/* Top Row: Navigation and Primary Actions */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-3 sm:mb-0">
          {/* Left Side: Back Button and Template Selector */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 min-w-0">
            {/* Back Button */}
            <button
              onClick={onBack}
              className="flex items-center space-x-2 px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              <span className="hidden sm:inline">Back to Templates</span>
              <span className="sm:hidden">Back</span>
            </button>

          </div>

          {/* Right Side: Export */}
          <div className="flex items-center justify-end">
            {/* Export Button */}
            <button
              onClick={onExport}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm font-medium"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span className="hidden sm:inline">Export Document</span>
              <span className="sm:hidden">Export</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Continuous View Component - Optimized for Mobile
 */
const ContinuousView = ({ pages, metadata }) => {
  // CRITICAL DEBUG: Log page container generation
  console.log('🔍 CONTINUOUS VIEW DEBUG: Rendering pages', {
    pagesReceived: !!pages,
    pagesType: typeof pages,
    pagesLength: pages?.length || 0,
    pagesIsArray: Array.isArray(pages),
    firstPagePreview: pages?.[0]?.substring(0, 100),
    secondPagePreview: pages?.[1]?.substring(0, 100),
    allPageLengths: pages?.map(page => page?.length || 0)
  });

  if (!pages || pages.length === 0) {
    console.log('🔍 CONTINUOUS VIEW DEBUG: No pages to display');
    return (
      <div className="continuous-view p-4 sm:p-6 md:p-8">
        <div className="text-center text-gray-500 py-8">
          No pages to display
        </div>
      </div>
    );
  }

  console.log('🔍 CONTINUOUS VIEW DEBUG: About to render', pages.length, 'page containers');

  return (
    <div className="continuous-view p-2 sm:p-3 md:p-4">
      {pages.map((page, index) => {
        console.log(`🔍 CONTINUOUS VIEW DEBUG: Rendering page ${index + 1}`, {
          pageIndex: index,
          pageLength: page?.length || 0,
          isTemplate: index === 0,
          pagePreview: page?.substring(0, 50)
        });

        return (
          <div
            key={index}
            style={{
              // FIXED: Reduced spacing between pages for better compactness
              marginBottom: index < pages.length - 1 ? '8px' : '0'
            }}
          >
            <PreviewPage
              content={page}
              pageNumber={index + 1}
              isTemplate={index === 0}
              metadata={metadata}
            />
          </div>
        );
      })}
    </div>
  );
};

/**
 * Preview Page Component - Mobile Optimized with Proper Pagination and Dynamic Container Sizing
 */
const PreviewPage = ({ content, pageNumber, isTemplate, metadata }) => {
  // CRITICAL DEBUG: Log individual page rendering and check for list elements
  const listCount = content?.match(/<[uo]l[^>]*>/g)?.length || 0;
  const listItemCount = content?.match(/<li[^>]*>/g)?.length || 0;

  // Fixed container sizing approach - container should accommodate the scaled page properly
  const baseWidth = 8.5; // inches
  const baseHeight = 11; // inches
  const previewScale = 0.75; // Increased scale for better visibility

  // Convert base dimensions to pixels (96 DPI standard)
  const baseWidthPx = baseWidth * 96;  // 816px
  const baseHeightPx = baseHeight * 96; // 1056px

  // Calculate visual dimensions (what user actually sees)
  const visualWidthPx = baseWidthPx * previewScale;  // 612px
  const visualHeightPx = baseHeightPx * previewScale; // 792px

  // Simplified responsive constraints
  const getViewportConstraints = () => {
    if (typeof window === 'undefined') return { maxWidth: 1200, isMobile: false };

    const viewportWidth = document.documentElement.clientWidth || window.innerWidth;
    const isMobile = viewportWidth < 768;

    const margin = isMobile ? 16 : 32;
    const maxWidth = viewportWidth - margin;

    return { maxWidth, isMobile, viewportWidth };
  };

  const { maxWidth: maxViewportWidth, isMobile, viewportWidth } = getViewportConstraints();

  // FIXED: Different scaling strategies for mobile vs desktop
  let containerWidth, containerHeight, pageScale, shouldConstrainWidth;

  if (isMobile) {
    // Mobile: Use a reasonable minimum scale to avoid tiny pages
    const mobileMinScale = 0.65; // Minimum scale for readability
    const calculatedScale = maxViewportWidth / baseWidthPx;
    pageScale = Math.max(mobileMinScale, calculatedScale);

    // Container dimensions accommodate the scaled page
    containerWidth = Math.min(baseWidthPx * pageScale, maxViewportWidth);
    containerHeight = baseHeightPx * pageScale; // CRITICAL: Match actual scaled page height
    shouldConstrainWidth = calculatedScale < mobileMinScale; // For debugging
  } else {
    // Desktop: Use preview scale if it fits, otherwise constrain
    shouldConstrainWidth = visualWidthPx > maxViewportWidth;

    if (shouldConstrainWidth) {
      pageScale = maxViewportWidth / baseWidthPx;
      containerWidth = maxViewportWidth;
      containerHeight = baseHeightPx * pageScale;
    } else {
      pageScale = previewScale;
      containerWidth = visualWidthPx;
      containerHeight = visualHeightPx;
    }
  }

  // ENHANCED MOBILE DEBUGGING
  console.log(`🔍 PREVIEW PAGE DEBUG: Rendering page ${pageNumber}`, {
    pageNumber,
    isTemplate,
    contentLength: content?.length || 0,
    hasContent: !!content && content.trim() !== '',
    viewport: {
      viewportWidth,
      maxViewportWidth,
      isMobile,
      shouldConstrainWidth,
      margin: isMobile ? 16 : 32
    },
    calculations: {
      baseWidthPx,
      baseHeightPx,
      previewScale,
      visualWidthPx,
      visualHeightPx,
      'visualWidthPx > maxViewportWidth': visualWidthPx > maxViewportWidth,
      pageScale,
      containerWidth,
      containerHeight
    },
    issues: {
      'Mobile zoom-out': isMobile && pageScale < 0.6 ? `Scale too small: ${pageScale.toFixed(2)}` : 'OK',
      'Container overflow': containerHeight < baseHeightPx ? `Container ${containerHeight}px < Page ${baseHeightPx}px` : 'OK'
    },
    formatting: {
      hasLists: listCount > 0,
      listCount,
      listItemCount
    }
  });

  return (
    <div
      className="preview-page-container"
      style={{
        // FIXED: Container properly sized to contain scaled page without overflow
        width: `${containerWidth}px`,
        height: `${containerHeight}px`,
        margin: '0 auto',
        marginBottom: 0,
        position: 'relative',
        maxWidth: isMobile ? '100vw' : 'none',
        // Ensure proper centering and NO internal scrolling
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'visible' // CRITICAL: Prevent internal scrollbars
      }}
    >
      <div
        className="preview-page bg-white shadow-lg border border-gray-300"
        style={{
          // FIXED: Proper scaling and centering
          transform: `scale(${pageScale})`,
          transformOrigin: 'center center', // Center both horizontally and vertically
          width: '8.5in',
          height: '11in',
          aspectRatio: '8.5/11',
          overflow: 'hidden',
          position: 'relative',
          boxSizing: 'border-box'
          // Removed margin - let container handle centering
        }}
      >
        {/* Page Content */}
        <div
          className="preview-page-content h-full p-4 sm:p-6 md:p-8"
          style={{
            height: '100%',
            overflow: 'hidden', // Ensure content doesn't overflow page boundaries
            display: 'flex',
            flexDirection: 'column',
            // MOBILE FIX: Ensure content scales with page
            fontSize: isMobile && pageScale < 0.7 ? '14px' : 'inherit',
            lineHeight: isMobile && pageScale < 0.7 ? '1.4' : 'inherit'
          }}
        >
          {/* Include TipTap styles with page break support */}
          <style>{getTipTapStyles()}</style>
          <style>{`
            /* Additional page-specific styles */
            .preview-content {
              height: 100%;
              overflow: hidden;
              font-size: 14px;
              line-height: 1.6;
            }

            @media (min-width: 640px) {
              .preview-content {
                font-size: 15px;
              }
            }

            @media (min-width: 768px) {
              .preview-content {
                font-size: 16px;
              }
            }

            /* Ensure content fits within page boundaries */
            .preview-content * {
              max-width: 100%;
              word-wrap: break-word;
            }

            /* CRITICAL FIX: Ensure list formatting is preserved */
            .preview-content ul,
            .preview-content ol {
              list-style: initial !important;
              margin: 1rem 0 !important;
              padding-left: 1.5rem !important;
            }

            .preview-content ul {
              list-style-type: disc !important;
            }

            .preview-content ol {
              list-style-type: decimal !important;
            }

            .preview-content li {
              display: list-item !important;
              margin-bottom: 0.25rem !important;
              line-height: 1.6 !important;
            }

            /* Nested list styling */
            .preview-content ul ul {
              list-style-type: circle !important;
              margin: 0.5rem 0 !important;
            }

            .preview-content ul ul ul {
              list-style-type: square !important;
            }

            .preview-content ol ol {
              list-style-type: lower-alpha !important;
              margin: 0.5rem 0 !important;
            }

            .preview-content ol ol ol {
              list-style-type: lower-roman !important;
            }

            /* Handle images within page constraints */
            .preview-content img {
              max-height: calc(100% - 2rem);
              object-fit: contain;
            }

            /* Handle tables within page constraints */
            .preview-content table {
              font-size: 0.9em;
              table-layout: fixed;
            }
          `}</style>

          <div
            className={`preview-content flex-1 ${isTemplate ? 'template-content' : 'document-content'}`}
            dangerouslySetInnerHTML={{ __html: content }}
          />
        </div>

        {/* Page Number */}
        <div className="absolute bottom-2 right-2 text-xs text-gray-500 bg-white px-2 py-1 rounded shadow z-10">
          {pageNumber}
        </div>

        {/* Template Badge */}
        {isTemplate && (
          <div className="absolute top-2 left-2 z-10">
            <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
              Cover Template
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentPreviewInterface;
