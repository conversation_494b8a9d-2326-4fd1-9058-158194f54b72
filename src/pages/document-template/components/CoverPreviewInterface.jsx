import React, { useCallback } from 'react';

/**
 * Cover Preview Interface
 * Shows cover-only preview for template design iteration
 * Replaces the full document preview during template selection phase
 */
const CoverPreviewInterface = ({
  selectedTemplate = null,
  templates = [],
  documentData = {},
  coverPreviewData = null,
  loading = false,
  error = null,
  onTemplateChange = null,
  onBack = null,
  onProceedToExport = null,
  className = ''
}) => {

  const handleTemplateChange = useCallback((newTemplate) => {
    if (onTemplateChange && newTemplate.id !== selectedTemplate?.id) {
      onTemplateChange(newTemplate);
    }
  }, [onTemplateChange, selectedTemplate]);

  if (loading) {
    return (
      <div className={`cover-preview-interface flex flex-col h-full ${className}`}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Generating cover preview...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`cover-preview-interface flex flex-col h-full ${className}`}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Preview Error</h3>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!coverPreviewData) {
    return (
      <div className={`cover-preview-interface flex flex-col h-full ${className}`}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <p className="text-gray-600">Please select a template to generate cover preview</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`cover-preview-interface flex flex-col h-full ${className}`}>
      {/* Preview Header */}
      <CoverPreviewHeader
        selectedTemplate={selectedTemplate}
        templates={templates}
        onTemplateChange={handleTemplateChange}
        onBack={onBack}
        onProceedToExport={onProceedToExport}
      />

      {/* Cover Preview Content */}
      <div className="flex-1 bg-gray-100 overflow-auto">
        <div className="mt-4 mx-auto sm:mt-6 text-center max-w-2xl">
          <h2 className="text-base sm:text-lg font-semibold text-gray-900 mb-2">Cover Design Preview</h2>
        </div>
        <CoverPreviewContainer coverPreviewData={coverPreviewData} selectedTemplate={selectedTemplate} />
      </div>
    </div>
  );
};

/**
 * Responsive Cover Preview Container
 * Handles mobile viewport constraints and proper scaling
 */
const CoverPreviewContainer = ({ coverPreviewData, selectedTemplate }) => {
  // Base dimensions for US Letter size (8.5" x 11")
  const baseWidth = 8.5; // inches
  const baseHeight = 11; // inches
  const baseWidthPx = baseWidth * 96; // 816px (96 DPI standard)
  const baseHeightPx = baseHeight * 96; // 1056px

  // Get viewport constraints with mobile-specific logic
  const getViewportConstraints = () => {
    if (typeof window === 'undefined') return { maxWidth: 1200, isMobile: false };

    const viewportWidth = document.documentElement.clientWidth || window.innerWidth;
    const viewportHeight = document.documentElement.clientHeight || window.innerHeight;

    let deviceType, margin;
    if (viewportWidth <= 480) {
      deviceType = 'small-mobile';
      margin = 16; // Small margin for small phones
    } else if (viewportWidth <= 640) {
      deviceType = 'large-mobile';
      margin = 24; // Medium margin for large phones
    } else if (viewportWidth <= 768) {
      deviceType = 'tablet-portrait';
      margin = 32; // Larger margin for tablets
    } else {
      deviceType = 'desktop';
      margin = 64; // Full margin for desktop
    }

    const isMobile = viewportWidth < 768;
    const maxWidth = viewportWidth - margin;
    const maxHeight = viewportHeight - 200; // Account for header and info sections

    return { maxWidth, maxHeight, isMobile, deviceType, viewportWidth, viewportHeight };
  };

  const { maxWidth, maxHeight, isMobile, deviceType } = getViewportConstraints();

  // Calculate responsive scaling
  let containerWidth, containerHeight, pageScale;

  if (isMobile) {
    // Mobile: Scale to fit viewport width with minimum readability scale
    const mobileMinScale = 0.4; // Minimum scale for mobile
    const widthScale = maxWidth / baseWidthPx;
    const heightScale = maxHeight / baseHeightPx;

    // Use the smaller scale to ensure it fits in both dimensions
    const calculatedScale = Math.min(widthScale, heightScale);
    pageScale = Math.max(mobileMinScale, calculatedScale);

    containerWidth = baseWidthPx * pageScale;
    containerHeight = baseHeightPx * pageScale;
  } else {
    // Desktop: Use a comfortable preview scale
    const desktopScale = 0.75;
    const widthScale = maxWidth / baseWidthPx;

    pageScale = Math.min(desktopScale, widthScale);
    containerWidth = baseWidthPx * pageScale;
    containerHeight = baseHeightPx * pageScale;
  }

  console.log('🔍 COVER PREVIEW DEBUG: Responsive scaling', {
    deviceType,
    viewport: { width: maxWidth, height: maxHeight },
    base: { width: baseWidthPx, height: baseHeightPx },
    calculated: { scale: pageScale, width: containerWidth, height: containerHeight },
    isMobile
  });

  return (
    <div className="cover-preview-container p-4 sm:p-6 md:p-8 flex items-center justify-center min-h-full">
      <div className="cover-preview-wrapper w-full flex flex-col items-center">
        {/* Responsive Cover Preview */}
        <div
          className="cover-preview-page shadow-lg overflow-hidden"
          style={{
            width: `${containerWidth}px`,
            height: `${containerHeight}px`,
            maxWidth: '100%',
            margin: '0 auto',
            position: 'relative'
          }}
        >
          <div
            className="cover-page-scaled"
            style={{
              width: `${baseWidthPx}px`,
              height: `${baseHeightPx}px`,
              transform: `scale(${pageScale})`,
              transformOrigin: 'center top',
              position: 'absolute',
              top: 0,
              left: '50%',
              marginLeft: `-${baseWidthPx / 2}px`
            }}
          >
            <div
              className="cover-content w-full h-full"
              dangerouslySetInnerHTML={{ __html: coverPreviewData.coverHTML }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Cover Preview Header Component
 */
const CoverPreviewHeader = ({
  selectedTemplate,
  templates,
  onTemplateChange,
  onBack,
  onProceedToExport
}) => {
  return (
    <div className="cover-preview-header border-b border-gray-200 bg-white">
      {/* Mobile-First Responsive Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-2 sm:p-4 gap-2 sm:gap-4">

        {/* Top Row on Mobile / Left Section on Desktop */}
        <div className="flex items-center justify-between sm:justify-start">
          {/* Back Button */}
          <button
            onClick={onBack}
            className="flex items-center space-x-1 sm:space-x-2 text-gray-600 hover:text-gray-800 transition-colors p-2 sm:p-0 -ml-2 sm:ml-0"
          >
            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span className="text-sm sm:text-base hidden xs:inline sm:inline">Back</span>
            <span className="text-sm hidden sm:inline">to Templates</span>
          </button>

          {/* Export Button - Mobile Position */}
          <button
            onClick={onProceedToExport}
            className="sm:hidden bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span>Export</span>
          </button>
        </div>

        {/* Template Selector - Full Width on Mobile */}
        <div className="flex items-center space-x-2 min-w-0 flex-1 sm:flex-initial">
          <label className="text-xs sm:text-sm font-medium text-gray-700 whitespace-nowrap">Template:</label>
          <select
            value={selectedTemplate?.id || ''}
            onChange={(e) => {
              const template = templates.find(t => t.id === e.target.value);
              if (template) onTemplateChange(template);
            }}
            className="border border-gray-300 rounded px-2 sm:px-3 py-1 text-xs sm:text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-0 flex-1 sm:flex-initial sm:min-w-[200px] max-w-full"
          >
            {templates.map(template => (
              <option key={template.id} value={template.id}>
                {template.name}
              </option>
            ))}
          </select>
        </div>

        {/* Desktop-Only Right Section */}
        <div className="hidden sm:flex items-center space-x-3">

          <button
            onClick={onProceedToExport}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2 whitespace-nowrap"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
            <span>Proceed to Export</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default CoverPreviewInterface;
