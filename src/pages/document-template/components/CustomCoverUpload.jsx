/**
 * Custom Cover Upload Component
 * Allows users to upload and manage custom cover images for their documents
 */

import React, { useState, useRef } from 'react';
import { Upload, X, Image, AlertCircle, Check } from 'lucide-react';
import customCoverImageService from '../../../services/customCoverImageService.js';

const CustomCoverUpload = ({ 
  documentData, 
  onCoverImageUpdate, 
  onToggleCustomCover,
  className = '' 
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState('');
  const [previewUrl, setPreviewUrl] = useState(null);
  const fileInputRef = useRef(null);

  // Get current custom cover configuration
  const customCoverConfig = documentData?.contentDetails?.customCoverImage || {};
  const isCustomCoverEnabled = customCoverConfig.enabled;
  const currentImageUrl = customCoverConfig.imageUrl;

  // Handle file selection
  const handleFileSelect = async (file) => {
    if (!file) return;

    setUploadError('');
    setIsUploading(true);

    try {
      // Validate file
      const validation = customCoverImageService.validateCoverImage(file);
      if (!validation.success) {
        throw new Error(validation.error);
      }

      // Create preview URL
      const preview = URL.createObjectURL(file);
      setPreviewUrl(preview);

      // Upload the image
      const uploadResult = await customCoverImageService.uploadCustomCoverImage(
        file,
        documentData.id || 'temp-doc-id',
        'current-user-id' // This should come from auth context
      );

      if (!uploadResult.success) {
        throw new Error(uploadResult.error);
      }

      // Update document data with new cover image
      const updatedCoverConfig = {
        enabled: true,
        imageUrl: uploadResult.imageUrl,
        imageMetadata: uploadResult.metadata,
        fallbackTemplate: customCoverConfig.fallbackTemplate || null
      };

      onCoverImageUpdate(updatedCoverConfig);

      console.log('✅ Custom cover image uploaded successfully');

    } catch (error) {
      console.error('❌ Error uploading custom cover image:', error);
      setUploadError(error.message);
      setPreviewUrl(null);
    } finally {
      setIsUploading(false);
    }
  };

  // Handle file input change
  const handleFileInputChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // Trigger file input
  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Remove custom cover image
  const handleRemoveCover = async () => {
    try {
      // Delete from storage if we have the file path
      if (customCoverConfig.imageMetadata?.filePath) {
        await customCoverImageService.deleteCustomCoverImage(
          customCoverConfig.imageMetadata.filePath
        );
      }

      // Update document data to disable custom cover
      const updatedCoverConfig = {
        enabled: false,
        imageUrl: null,
        imageMetadata: null,
        fallbackTemplate: customCoverConfig.fallbackTemplate
      };

      onCoverImageUpdate(updatedCoverConfig);
      setPreviewUrl(null);
      setUploadError('');

      // Clear file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

    } catch (error) {
      console.error('❌ Error removing custom cover image:', error);
      setUploadError('Failed to remove cover image');
    }
  };

  // Toggle custom cover mode
  const handleToggleCustomCover = () => {
    const newEnabled = !isCustomCoverEnabled;
    
    if (newEnabled && !currentImageUrl) {
      // If enabling but no image, trigger upload
      triggerFileInput();
    } else {
      // Just toggle the enabled state
      const updatedCoverConfig = {
        ...customCoverConfig,
        enabled: newEnabled
      };
      onCoverImageUpdate(updatedCoverConfig);
    }

    if (onToggleCustomCover) {
      onToggleCustomCover(newEnabled);
    }
  };

  return (
    <div className={`custom-cover-upload ${className}`}>
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileInputChange}
        style={{ display: 'none' }}
      />

      {/* Toggle Switch */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Image className="w-5 h-5 text-gray-600" />
          <span className="font-medium text-gray-900">Custom Cover Image</span>
        </div>
        <button
          onClick={handleToggleCustomCover}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
            isCustomCoverEnabled ? 'bg-blue-600' : 'bg-gray-200'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              isCustomCoverEnabled ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </div>

      {/* Upload Area */}
      {isCustomCoverEnabled && (
        <div className="space-y-4">
          {/* Current Image or Upload Area */}
          {currentImageUrl || previewUrl ? (
            <div className="relative">
              <div className="aspect-[3/4] w-full max-w-xs mx-auto rounded-lg overflow-hidden border-2 border-gray-200">
                <img
                  src={previewUrl || currentImageUrl}
                  alt="Custom Cover Preview"
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* Remove Button */}
              <button
                onClick={handleRemoveCover}
                className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                title="Remove cover image"
              >
                <X className="w-4 h-4" />
              </button>

              {/* Replace Button */}
              <button
                onClick={triggerFileInput}
                disabled={isUploading}
                className="mt-2 w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
              >
                {isUploading ? 'Uploading...' : 'Replace Image'}
              </button>
            </div>
          ) : (
            <div
              onClick={triggerFileInput}
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-gray-400 transition-colors"
            >
              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-900 mb-2">
                Upload Cover Image
              </p>
              <p className="text-sm text-gray-500 mb-4">
                Click to select an image file
              </p>
              <p className="text-xs text-gray-400">
                Supports JPG, PNG, WebP • Max 10MB
              </p>
            </div>
          )}

          {/* Upload Status */}
          {isUploading && (
            <div className="flex items-center space-x-2 text-blue-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-sm">Uploading image...</span>
            </div>
          )}

          {/* Success Message */}
          {currentImageUrl && !isUploading && !uploadError && (
            <div className="flex items-center space-x-2 text-green-600">
              <Check className="w-4 h-4" />
              <span className="text-sm">Cover image uploaded successfully</span>
            </div>
          )}

          {/* Error Message */}
          {uploadError && (
            <div className="flex items-center space-x-2 text-red-600">
              <AlertCircle className="w-4 h-4" />
              <span className="text-sm">{uploadError}</span>
            </div>
          )}

          {/* Help Text */}
          <div className="text-xs text-gray-500 space-y-1">
            <p>• Your custom image will replace the template cover</p>
            <p>• Image will be optimized automatically for best quality</p>
            <p>• You can switch back to template covers anytime</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomCoverUpload;
