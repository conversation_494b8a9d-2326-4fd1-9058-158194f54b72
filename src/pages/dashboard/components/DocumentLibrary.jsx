import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import DocumentCard from './DocumentCard';

const DocumentLibrary = ({ 
  documents = [],
  view = 'grid',
  filterStatus = 'all',
  filterType = 'all',
  onEdit,
  onDuplicate,
  onDelete
}) => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('recent');
  const [viewMode, setViewMode] = useState('grid');
  const navigate = useNavigate();

  const filterOptions = [
    { key: 'all', label: 'All Documents', count: documents.length },
    { key: 'ebook', label: 'eBooks', count: documents.filter(d => d.type === 'ebook').length },
    { key: 'academic', label: 'Academic', count: documents.filter(d => d.type === 'academic').length },
    { key: 'business', label: 'Business', count: documents.filter(d => d.type === 'business').length },
    { key: 'draft', label: 'Drafts', count: documents.filter(d => d.status === 'draft').length }
  ];

  const sortOptions = [
    { key: 'recent', label: 'Most Recent' },
    { key: 'oldest', label: 'Oldest First' },
    { key: 'name', label: 'Name A-Z' },
    { key: 'type', label: 'Type' },
    { key: 'status', label: 'Status' }
  ];

  const getFilteredAndSortedDocuments = () => {
    let filtered = documents;

    // Apply filter
    if (activeFilter !== 'all') {
      if (activeFilter === 'draft') {
        filtered = filtered.filter(doc => doc.status === 'draft');
      } else {
        filtered = filtered.filter(doc => doc.type === activeFilter);
      }
    }

    // Apply search
    if (searchQuery) {
      filtered = filtered.filter(doc =>
        doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.type.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply sort
    switch (sortBy) {
      case 'oldest':
        filtered.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
        break;
      case 'name':
        filtered.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'type':
        filtered.sort((a, b) => a.type.localeCompare(b.type));
        break;
      case 'status':
        filtered.sort((a, b) => a.status.localeCompare(b.status));
        break;
      case 'recent':
      default:
        filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        break;
    }

    return filtered;
  };

  let filteredDocuments = getFilteredAndSortedDocuments();

  // Apply maxItems limit if specified
  if (maxItems) {
    filteredDocuments = filteredDocuments.slice(0, maxItems);
  }

  const handleCreateNew = () => {
    navigate('/document-creator');
  };

  return (
    <div className={compact ? "space-y-4" : "space-y-6"}>
      {/* Header - Only show if not compact */}
      {!compact && (
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold text-text-primary">Document Library</h2>
            <p className="text-text-secondary">Manage and organize your documents</p>
          </div>
          <Button
            variant="primary"
            onClick={handleCreateNew}
            iconName="Plus"
          >
            Create New Document
          </Button>
        </div>
      )}

      {/* Filters and Search - Only show if not compact and showFilters is true */}
      {!compact && showFilters && (
        <div className="bg-white rounded-xl border border-border p-6 shadow-card">
          {/* Filter Tabs */}
          <div className="flex flex-wrap gap-2 mb-4">
            {filterOptions.map((option) => (
              <button
                key={option.key}
                onClick={() => setActiveFilter(option.key)}
                className={`px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 flex items-center space-x-2 ${
                  activeFilter === option.key
                    ? 'bg-primary text-white shadow-card'
                    : 'bg-surface-secondary text-text-secondary hover:text-text-primary hover:bg-surface-hover'
                }`}
              >
                <span>{option.label}</span>
              <span className={`px-1.5 py-0.5 rounded-full text-xs ${
                activeFilter === option.key
                  ? 'bg-primary-foreground/20'
                  : 'bg-text-secondary/20'
              }`}>
                {option.count}
              </span>
            </button>
          ))}
        </div>

        {/* Search and Controls */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Input
              type="search"
              placeholder="Search documents..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-border rounded-lg text-sm bg-surface text-text-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
            >
              {sortOptions.map((option) => (
                <option key={option.key} value={option.key}>
                  {option.label}
                </option>
              ))}
            </select>

            <div className="flex items-center border border-border rounded-lg">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 ${viewMode === 'grid' ? 'bg-primary text-primary-foreground' : 'text-text-secondary hover:text-text-primary'}`}
              >
                <Icon name="Grid3X3" size={16} />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 ${viewMode === 'list' ? 'bg-primary text-primary-foreground' : 'text-text-secondary hover:text-text-primary'}`}
              >
                <Icon name="List" size={16} />
              </button>
            </div>
          </div>
        </div>
      </div>
      )}

      {/* Documents Grid/List */}
      {filteredDocuments.length === 0 ? (
        !compact ? (
          <div className="bg-white rounded-xl border border-border p-12 text-center shadow-card">
            <Icon name="FileText" size={48} variant="muted" className="mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-text-primary mb-2">
              {searchQuery ? 'No documents found' : 'No documents yet'}
            </h3>
            <p className="text-text-secondary mb-6">
              {searchQuery
                ? `No documents match "${searchQuery}". Try adjusting your search.`
                : 'Start creating your first document with AI assistance.'
              }
            </p>
            <Button
              variant="primary"
              onClick={handleCreateNew}
              iconName="Plus"
            >
              Create Your First Document
            </Button>
          </div>
        ) : null
      ) : (
        <div className={
          compact
            ? 'space-y-3'
            : viewMode === 'grid'
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
              : 'space-y-4'
        }>
          {filteredDocuments.map((document) => (
            <DocumentCard
              key={document.id}
              document={document}
              onEdit={onEdit}
              onDuplicate={onDuplicate}
              onDelete={onDelete}
              compact={compact}
            />
          ))}
        </div>
      )}

      {/* Load More - Only show if not compact */}
      {!compact && filteredDocuments.length > 0 && filteredDocuments.length % 12 === 0 && (
        <div className="text-center">
          <Button
            variant="secondary"
            onClick={() => console.log('Load more documents')}
            iconName="ChevronDown"
          >
            Load More Documents
          </Button>
        </div>
      )}
    </div>
  );
};

export default DocumentLibrary;