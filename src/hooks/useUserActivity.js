import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { userActivityService } from '../services/userActivityService';

/**
 * Hook for tracking user activities and managing usage statistics
 */
export const useUserActivity = () => {
  const { user, profile } = useAuth();
  const [usageStats, setUsageStats] = useState({
    documentsCreated: 0,
    documentsLimit: 10,
    aiGenerationsUsed: 0,
    aiGenerationsLimit: 50,
    storageUsedMB: 0,
    storageLimitMB: 100
  });
  const [activityHistory, setActivityHistory] = useState([]);
  const [loading, setLoading] = useState(false);

  // Load usage statistics
  const loadUsageStats = useCallback(async () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      const stats = await userActivityService.getUserUsageStats(user.id);
      setUsageStats(stats);
    } catch (error) {
      console.error('Failed to load usage stats:', error);
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Load activity history
  const loadActivityHistory = useCallback(async () => {
    if (!user?.id) return;

    try {
      const history = await userActivityService.getUserActivityHistory(user.id);
      setActivityHistory(history);
    } catch (error) {
      console.error('Failed to load activity history:', error);
    }
  }, [user?.id]);

  // Track document creation
  const trackDocumentCreation = useCallback(async () => {
    if (!user?.id) return;

    try {
      await userActivityService.trackDocumentCreation(user.id);
      // Refresh stats after tracking
      await loadUsageStats();
    } catch (error) {
      console.error('Failed to track document creation:', error);
    }
  }, [user?.id, loadUsageStats]);

  // Track AI generation
  const trackAIGeneration = useCallback(async () => {
    if (!user?.id) return;

    try {
      await userActivityService.trackAIGeneration(user.id);
      // Refresh stats after tracking
      await loadUsageStats();
    } catch (error) {
      console.error('Failed to track AI generation:', error);
    }
  }, [user?.id, loadUsageStats]);

  // Update storage usage
  const updateStorageUsage = useCallback(async (sizeInMB) => {
    if (!user?.id) return;

    try {
      await userActivityService.updateStorageUsage(user.id, sizeInMB);
      // Refresh stats after updating
      await loadUsageStats();
    } catch (error) {
      console.error('Failed to update storage usage:', error);
    }
  }, [user?.id, loadUsageStats]);

  // Load data when user changes
  useEffect(() => {
    if (user?.id) {
      loadUsageStats();
      loadActivityHistory();
    }
  }, [user?.id, loadUsageStats, loadActivityHistory]);

  // Update stats when profile changes
  useEffect(() => {
    if (profile) {
      setUsageStats(prev => ({
        ...prev,
        documentsCreated: profile.documents_created || 0,
        documentsLimit: profile.documents_limit || 10,
        aiGenerationsUsed: profile.ai_generations_used || 0,
        aiGenerationsLimit: profile.ai_generations_limit || 50,
        storageUsedMB: profile.storage_used_mb || 0,
        storageLimitMB: profile.storage_limit_mb || 100
      }));
    }
  }, [profile]);

  // Calculate usage percentages
  const documentsUsagePercentage = usageStats.documentsLimit > 0 
    ? (usageStats.documentsCreated / usageStats.documentsLimit) * 100 
    : 0;

  const aiGenerationsUsagePercentage = usageStats.aiGenerationsLimit > 0 
    ? (usageStats.aiGenerationsUsed / usageStats.aiGenerationsLimit) * 100 
    : 0;

  const storageUsagePercentage = usageStats.storageLimitMB > 0 
    ? (usageStats.storageUsedMB / usageStats.storageLimitMB) * 100 
    : 0;

  // Check if user is approaching limits
  const isApproachingDocumentLimit = documentsUsagePercentage > 80;
  const isApproachingAILimit = aiGenerationsUsagePercentage > 80;
  const isApproachingStorageLimit = storageUsagePercentage > 80;

  // Check if user has reached limits
  const hasReachedDocumentLimit = usageStats.documentsCreated >= usageStats.documentsLimit;
  const hasReachedAILimit = usageStats.aiGenerationsUsed >= usageStats.aiGenerationsLimit;
  const hasReachedStorageLimit = usageStats.storageUsedMB >= usageStats.storageLimitMB;

  return {
    // Data
    usageStats,
    activityHistory,
    loading,

    // Usage percentages
    documentsUsagePercentage,
    aiGenerationsUsagePercentage,
    storageUsagePercentage,

    // Limit checks
    isApproachingDocumentLimit,
    isApproachingAILimit,
    isApproachingStorageLimit,
    hasReachedDocumentLimit,
    hasReachedAILimit,
    hasReachedStorageLimit,

    // Actions
    trackDocumentCreation,
    trackAIGeneration,
    updateStorageUsage,
    loadUsageStats,
    loadActivityHistory
  };
};

export default useUserActivity;
