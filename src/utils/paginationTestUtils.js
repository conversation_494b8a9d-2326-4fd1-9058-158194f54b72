/**
 * Pagination Test Utilities
 * Helper functions to test and validate pagination functionality
 */

/**
 * Generate test content of varying lengths for pagination testing
 * @param {string} type - Type of content to generate ('short', 'medium', 'long', 'mixed')
 * @returns {string} HTML content for testing
 */
export const generateTestContent = (type = 'medium') => {
  const paragraph = `<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>`;
  
  const heading = `<h2>Chapter Heading</h2>`;
  const subheading = `<h3>Section Subheading</h3>`;
  const list = `<ul><li>First item in the list</li><li>Second item with more content</li><li>Third item to complete the list</li></ul>`;
  const blockquote = `<blockquote>This is a blockquote that should be kept together on the same page when possible.</blockquote>`;
  const codeBlock = `<pre><code>function example() {
  console.log("This is a code block");
  return "Should stay together";
}</code></pre>`;

  switch (type) {
    case 'short':
      return `${heading}${paragraph}${paragraph}`;
    
    case 'medium':
      return `${heading}${paragraph}${subheading}${paragraph}${list}${paragraph}${blockquote}${paragraph}`;
    
    case 'long':
      return Array(15).fill(`${heading}${paragraph}${subheading}${paragraph}${list}${paragraph}`).join('');
    
    case 'mixed':
      return `${heading}${paragraph}${subheading}${paragraph}${list}${codeBlock}${blockquote}${paragraph}${heading}${paragraph}${subheading}${paragraph}${list}${paragraph}${blockquote}${paragraph}`;
    
    default:
      return `${heading}${paragraph}${subheading}${paragraph}${list}${paragraph}`;
  }
};

/**
 * Test pagination with different content types
 * @param {Function} paginationFunction - The pagination function to test
 * @returns {Object} Test results
 */
export const testPagination = (paginationFunction) => {
  const testCases = [
    { name: 'Short Content', content: generateTestContent('short') },
    { name: 'Medium Content', content: generateTestContent('medium') },
    { name: 'Long Content', content: generateTestContent('long') },
    { name: 'Mixed Content', content: generateTestContent('mixed') }
  ];

  const results = {};

  testCases.forEach(testCase => {
    try {
      const pages = paginationFunction(testCase.content);
      results[testCase.name] = {
        success: true,
        pageCount: pages.length,
        pages: pages,
        averagePageLength: pages.reduce((sum, page) => sum + page.length, 0) / pages.length
      };
    } catch (error) {
      results[testCase.name] = {
        success: false,
        error: error.message
      };
    }
  });

  return results;
};

/**
 * Validate that pagination results meet expected criteria
 * @param {Array} pages - Array of paginated content
 * @param {Object} criteria - Validation criteria
 * @returns {Object} Validation results
 */
export const validatePagination = (pages, criteria = {}) => {
  const {
    minPages = 1,
    maxPages = 50,
    minPageLength = 10,
    maxPageLength = 10000
  } = criteria;

  const validation = {
    valid: true,
    issues: [],
    pageCount: pages.length,
    pageLengths: pages.map(page => page.length)
  };

  // Check page count
  if (pages.length < minPages) {
    validation.valid = false;
    validation.issues.push(`Too few pages: ${pages.length} < ${minPages}`);
  }

  if (pages.length > maxPages) {
    validation.valid = false;
    validation.issues.push(`Too many pages: ${pages.length} > ${maxPages}`);
  }

  // Check individual page lengths
  pages.forEach((page, index) => {
    if (page.length < minPageLength) {
      validation.valid = false;
      validation.issues.push(`Page ${index + 1} too short: ${page.length} < ${minPageLength}`);
    }

    if (page.length > maxPageLength) {
      validation.valid = false;
      validation.issues.push(`Page ${index + 1} too long: ${page.length} > ${maxPageLength}`);
    }
  });

  // Check for empty pages
  const emptyPages = pages.filter(page => !page.trim()).length;
  if (emptyPages > 0) {
    validation.valid = false;
    validation.issues.push(`Found ${emptyPages} empty pages`);
  }

  return validation;
};

/**
 * Log pagination test results to console
 * @param {Object} results - Test results from testPagination
 */
export const logTestResults = (results) => {
  console.log('📊 Pagination Test Results:');
  console.log('================================');
  
  Object.entries(results).forEach(([testName, result]) => {
    if (result.success) {
      console.log(`✅ ${testName}:`);
      console.log(`   Pages: ${result.pageCount}`);
      console.log(`   Avg Length: ${Math.round(result.averagePageLength)} chars`);
    } else {
      console.log(`❌ ${testName}: ${result.error}`);
    }
  });
  
  console.log('================================');
};

/**
 * Run comprehensive pagination tests
 * @param {Function} paginationFunction - The pagination function to test
 * @returns {Object} Complete test results
 */
export const runPaginationTests = (paginationFunction) => {
  console.log('🧪 Running pagination tests...');
  
  const testResults = testPagination(paginationFunction);
  logTestResults(testResults);
  
  // Validate each test result
  const validationResults = {};
  Object.entries(testResults).forEach(([testName, result]) => {
    if (result.success) {
      validationResults[testName] = validatePagination(result.pages);
    }
  });
  
  console.log('🔍 Validation Results:');
  console.log('================================');
  Object.entries(validationResults).forEach(([testName, validation]) => {
    if (validation.valid) {
      console.log(`✅ ${testName}: Valid`);
    } else {
      console.log(`❌ ${testName}: Issues found`);
      validation.issues.forEach(issue => console.log(`   - ${issue}`));
    }
  });
  console.log('================================');
  
  return {
    testResults,
    validationResults
  };
};

/**
 * Test pagination with real document data
 * @param {Function} paginationFunction - The pagination function to test
 * @param {string} documentHTML - Real document HTML content
 * @returns {Object} Test results for real content
 */
export const testWithRealContent = (paginationFunction, documentHTML) => {
  console.log('📄 Testing with real document content...');
  
  try {
    const pages = paginationFunction(documentHTML);
    const validation = validatePagination(pages);
    
    console.log(`📊 Real Content Results:`);
    console.log(`   Pages: ${pages.length}`);
    console.log(`   Valid: ${validation.valid ? '✅' : '❌'}`);
    
    if (!validation.valid) {
      console.log(`   Issues:`);
      validation.issues.forEach(issue => console.log(`   - ${issue}`));
    }
    
    return {
      success: true,
      pageCount: pages.length,
      validation,
      pages
    };
  } catch (error) {
    console.log(`❌ Real Content Test Failed: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
};
