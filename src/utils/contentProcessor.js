/**
 * Content Processor Utilities for DocGenerate
 * Handles extraction and replacement of content in TipTap editor
 */

/**
 * Extract content from the current editor context
 * @param {Object} editor - TipTap editor instance
 * @param {string} nodeType - Type of the current node
 * @returns {Object} - Content extraction result
 */
export const extractNodeContent = (editor, nodeType = null) => {
  if (!editor) {
    throw new Error("Editor instance is required");
  }

  const { state } = editor;
  const { selection, doc } = state;
  const { $from, from, to } = selection;

  // Get the current node
  const currentNode = $from.node();
  const detectedNodeType = nodeType || currentNode.type.name;

  // Extract selected text if there's a selection
  const selectedText = from !== to ? doc.textBetween(from, to) : "";

  // Get the full node content as fallback
  const nodeContent = currentNode.textContent || "";

  // Determine what text to process
  const textToProcess = selectedText || nodeContent;

  // Get surrounding context for better AI results
  const documentContext = getDocumentContext(editor);

  return {
    text: textToProcess,
    selectedText,
    nodeContent,
    nodeType: detectedNodeType,
    hasSelection: from !== to,
    selectionRange: { from, to },
    documentContext,
    nodeSize: currentNode.nodeSize,
    nodePosition: $from.start(),
    isEmpty: !textToProcess.trim(),
  };
};

/**
 * Replace content in the editor while preserving formatting and cursor position
 * @param {Object} editor - TipTap editor instance
 * @param {string} newContent - New content to insert
 * @param {Object} extractionResult - Result from extractNodeContent
 * @param {Object} options - Replacement options
 */
export const replaceNodeContent = (
  editor,
  newContent,
  extractionResult,
  options = {}
) => {
  if (!editor || !newContent) {
    throw new Error("Editor instance and new content are required");
  }

  const { preserveFormatting = true, focusAfter = true } = options;
  const { hasSelection, selectionRange, nodeType } = extractionResult;

  try {
    // Start a transaction to group all operations
    const { tr } = editor.state;

    if (hasSelection) {
      // Replace selected text
      const newTr = tr.replaceWith(
        selectionRange.from,
        selectionRange.to,
        editor.schema.text(newContent)
      );

      // Apply the transaction
      editor.view.dispatch(newTr);

      if (focusAfter) {
        // Position cursor at the end of the new content
        const newPos = selectionRange.from + newContent.length;
        editor.commands.setTextSelection(newPos);
      }
    } else {
      // Replace entire node content using proper TipTap transaction
      const { state } = editor;
      const { selection } = state;
      const { $from } = selection;
      const currentNode = $from.node();

      // Get the content boundaries within the current node (excluding node boundaries)
      const nodeStart = $from.start();
      const nodeEnd = $from.end();

      // Create a new node with the same type and attributes but new content
      let newNode;
      if (nodeType === "heading") {
        // For headings, preserve the heading level
        const level = currentNode.attrs?.level || 2;
        newNode = editor.schema.nodes.heading.create(
          { level },
          editor.schema.text(newContent)
        );
      } else if (nodeType === "blockquote") {
        // For blockquotes, maintain quote formatting
        newNode = editor.schema.nodes.blockquote.create(
          currentNode.attrs,
          editor.schema.nodes.paragraph.create({}, editor.schema.text(newContent))
        );
      } else if (nodeType === "codeBlock") {
        // For code blocks, preserve code formatting
        newNode = editor.schema.nodes.codeBlock.create(
          currentNode.attrs,
          editor.schema.text(newContent)
        );
      } else if (nodeType === "listItem") {
        // For list items, preserve list structure
        newNode = editor.schema.nodes.listItem.create(
          currentNode.attrs,
          editor.schema.nodes.paragraph.create({}, editor.schema.text(newContent))
        );
      } else {
        // For paragraphs and other content
        newNode = editor.schema.nodes.paragraph.create(
          currentNode.attrs,
          editor.schema.text(newContent)
        );
      }

      // Use a transaction to replace the node content atomically
      const { tr } = state;
      const newTr = tr.replaceWith(nodeStart, nodeEnd, newNode.content);

      // Apply the transaction
      editor.view.dispatch(newTr);

      if (focusAfter) {
        // Position cursor at the end of the new content within the current node
        setTimeout(() => {
          try {
            // Calculate the position at the end of the replaced content
            const newContentLength = newContent.length;
            const targetPosition = nodeStart + newContentLength;

            // Ensure position is within document bounds
            const maxPos = editor.state.doc.content.size;
            const safePosition = Math.min(targetPosition, maxPos);

            // Set cursor to the end of the new content
            editor.commands.setTextSelection(safePosition);
            editor.commands.focus();
          } catch (error) {
            console.warn('Failed to position cursor after content replacement:', error);
            // Fallback: just focus the editor
            editor.commands.focus();
          }
        }, 0);
      }
    }

    return true;
  } catch (error) {
    console.error("Failed to replace content:", error);
    throw new Error(`Content replacement failed: ${error.message}`);
  }
};

/**
 * Get document context to provide better AI prompts
 * @param {Object} editor - TipTap editor instance
 * @returns {string} - Document context description
 */
export const getDocumentContext = (editor) => {
  if (!editor) return "";

  try {
    const { doc } = editor.state;
    const contentLength = editor.getText().length;

    // Analyze document structure
    let headingCount = 0;
    let listCount = 0;
    let quoteCount = 0;
    let codeBlockCount = 0;
    let imageCount = 0;

    doc.descendants((node) => {
      switch (node.type.name) {
        case "heading":
          headingCount++;
          break;
        case "bulletList":
        case "orderedList":
          listCount++;
          break;
        case "blockquote":
          quoteCount++;
          break;
        case "codeBlock":
          codeBlockCount++;
          break;
        case "image":
        case "enhancedImage":
          imageCount++;
          break;
      }
    });

    // Determine document type based on structure
    if (headingCount > 3 && listCount > 2) {
      return "structured document or article";
    } else if (codeBlockCount > 0) {
      return "technical documentation";
    } else if (quoteCount > 0) {
      return "academic or reference document";
    } else if (contentLength > 1000) {
      return "long-form content";
    } else {
      return "general document";
    }
  } catch (error) {
    console.warn("Failed to analyze document context:", error);
    return "";
  }
};

/**
 * Preserve cursor position during content operations
 * @param {Object} editor - TipTap editor instance
 * @param {Function} operation - Operation to perform
 * @returns {Promise} - Operation result
 */
export const withCursorPreservation = async (editor, operation) => {
  if (!editor || typeof operation !== "function") {
    throw new Error("Editor instance and operation function are required");
  }

  // Store current selection
  const { selection } = editor.state;
  const { from, to } = selection;

  try {
    // Perform the operation
    const result = await operation();

    // Restore cursor position if possible
    const newDoc = editor.state.doc;
    const maxPos = newDoc.content.size;
    const newFrom = Math.min(from, maxPos);
    const newTo = Math.min(to, maxPos);

    // Set selection back to preserved position
    editor.commands.setTextSelection({ from: newFrom, to: newTo });

    return result;
  } catch (error) {
    // Even if operation fails, try to restore cursor
    try {
      editor.commands.focus();
    } catch (focusError) {
      console.warn(
        "Failed to restore focus after operation error:",
        focusError
      );
    }
    throw error;
  }
};

/**
 * Extract node level for headings
 * @param {Object} node - TipTap node
 * @returns {number} - Heading level (1-6)
 */
export const getHeadingLevel = (node) => {
  if (!node || node.type.name !== "heading") {
    return 2; // Default to H2
  }

  return node.attrs?.level || 2;
};

/**
 * Check if content has changed significantly
 * @param {string} original - Original content
 * @param {string} modified - Modified content
 * @returns {Object} - Change analysis
 */
export const analyzeContentChanges = (original, modified) => {
  if (!original || !modified) {
    return { hasChanges: false, similarity: 0 };
  }

  // Basic similarity check (simple word overlap)
  const originalWords = original.toLowerCase().split(/\s+/);
  const modifiedWords = modified.toLowerCase().split(/\s+/);

  const commonWords = originalWords.filter((word) =>
    modifiedWords.includes(word)
  );
  const similarity =
    commonWords.length / Math.max(originalWords.length, modifiedWords.length);

  const lengthChange = (modified.length - original.length) / original.length;

  return {
    hasChanges: original !== modified,
    similarity,
    lengthChange,
    originalLength: original.length,
    modifiedLength: modified.length,
    significantChange: similarity < 0.7 || Math.abs(lengthChange) > 0.3,
  };
};

/**
 * Validate that editor state is ready for content operations
 * @param {Object} editor - TipTap editor instance
 * @returns {Object} - Validation result
 */
export const validateEditorState = (editor) => {
  if (!editor) {
    return { valid: false, error: "Editor instance is required" };
  }

  if (editor.isDestroyed) {
    return { valid: false, error: "Editor has been destroyed" };
  }

  if (!editor.isEditable) {
    return { valid: false, error: "Editor is not editable" };
  }

  if (!editor.state) {
    return { valid: false, error: "Editor state is not available" };
  }

  return { valid: true };
};

/**
 * Format content based on node type requirements
 * @param {string} content - Content to format
 * @param {string} nodeType - Target node type
 * @returns {string} - Formatted content
 */
export const formatContentForNode = (content, nodeType) => {
  if (!content) return content;

  switch (nodeType) {
    case "heading":
      // Remove line breaks and trim
      return content.replace(/\n/g, " ").trim();

    case "listItem":
      // Ensure single line for list items
      return content.replace(/\n/g, " ").trim();

    case "blockquote":
      // Preserve line breaks but clean up spacing
      return content.replace(/\n{3,}/g, "\n\n").trim();

    case "codeBlock":
      // Preserve exact formatting for code
      return content;

    default:
      // Clean up paragraph content
      return content.replace(/\n{3,}/g, "\n\n").trim();
  }
};

/**
 * Create a preview of content changes
 * @param {string} original - Original content
 * @param {string} modified - Modified content
 * @param {number} maxLength - Maximum preview length
 * @returns {Object} - Preview object
 */
export const createContentPreview = (original, modified, maxLength = 200) => {
  const truncate = (text, length) => {
    if (text.length <= length) return text;
    return text.substring(0, length) + "...";
  };

  return {
    original: truncate(original, maxLength),
    modified: truncate(modified, maxLength),
    changes: analyzeContentChanges(original, modified),
  };
};
