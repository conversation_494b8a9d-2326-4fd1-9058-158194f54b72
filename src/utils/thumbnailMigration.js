/**
 * One-time utility to extract thumbnails for existing projects and store them in th// For manual execution from browser console
if (typeof window !== 'undefined') {
  window.migrateProjectThumbnails = migrateProjectThumbnails;
  
  // Add a simple function to execute in console
  window.fixThumbnails = async () => {
    console.log('Starting thumbnail migration...');
    try {
      const result = await migrateProjectThumbnails();
      console.log('Migration completed:', result);
      return result;
    } catch (error) {
      console.error('Migration failed:', error);
      return { success: false, error };
    }
  };
}

export default migrateProjectThumbnails;base
 * This will help migrate existing projects to use the new extracted_thumbnail_url field
 */

import { supabase } from "../lib/supabase";
import { extractProjectThumbnail } from "../utils/projectThumbnails";

/**
 * Extracts and stores thumbnails for all existing projects
 * @returns {Promise<{success: boolean, processed: number, updated: number, errors: number}>}
 */
export const migrateProjectThumbnails = async () => {
  try {
    console.log("Starting thumbnail migration process...");

    // Get all projects with content but without extracted thumbnails
    const { data: projects, error } = await supabase
      .from("projects")
      .select("id, title, generated_content")
      .is("deleted_at", null)
      .is("extracted_thumbnail_url", null)
      .not("generated_content", "is", null);

    if (error) {
      console.error("Failed to fetch projects for thumbnail migration:", error);
      return { success: false, processed: 0, updated: 0, errors: 1 };
    }

    console.log(`Found ${projects?.length || 0} projects to process...`);

    let updated = 0;
    let errors = 0;

    // Process each project
    for (const project of projects || []) {
      try {
        // Extract thumbnail using our client-side utility
        const thumbnailUrl = extractProjectThumbnail(project);

        if (thumbnailUrl) {
          console.log(
            `Extracted thumbnail for project ${project.id} (${project.title}): ${thumbnailUrl}`
          );

          // Update the project with the extracted thumbnail
          const { error: updateError } = await supabase
            .from("projects")
            .update({ extracted_thumbnail_url: thumbnailUrl })
            .eq("id", project.id);

          if (updateError) {
            console.error(
              `Failed to update thumbnail for project ${project.id}:`,
              updateError
            );
            errors++;
          } else {
            updated++;
          }
        } else {
          console.log(
            `No thumbnail found for project ${project.id} (${project.title})`
          );
        }
      } catch (projectError) {
        console.error(`Error processing project ${project.id}:`, projectError);
        errors++;
      }
    }

    console.log("Thumbnail migration completed:");
    console.log(`- Processed: ${projects?.length || 0} projects`);
    console.log(`- Updated: ${updated} projects`);
    console.log(`- Errors: ${errors} projects`);

    return {
      success: true,
      processed: projects?.length || 0,
      updated,
      errors,
    };
  } catch (error) {
    console.error("Error during thumbnail migration:", error);
    return { success: false, processed: 0, updated: 0, errors: 1 };
  }
};

// For manual execution from browser console
if (typeof window !== "undefined") {
  window.migrateProjectThumbnails = migrateProjectThumbnails;
}

export default migrateProjectThumbnails;
