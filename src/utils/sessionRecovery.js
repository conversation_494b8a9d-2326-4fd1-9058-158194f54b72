// Session Recovery Utility
// This utility helps recover from stuck or corrupted sessions

import { supabase } from "../lib/supabase";

export const sessionRecovery = {
  // Check if we're in a potentially stuck session state
  isStuckSession: () => {
    // Check if we've been on the loading screen for too long
    const loadStartTime = sessionStorage.getItem("rapiddoc_load_start_time");
    if (loadStartTime) {
      const loadDuration = Date.now() - parseInt(loadStartTime);

      // Mobile-friendly timeout (8 seconds)
      const stuckThreshold = 8000;

      // If loading for more than threshold, consider it stuck
      if (loadDuration > stuckThreshold) {
        console.warn(
          `⚠️ Detected potentially stuck session (loading > ${stuckThreshold}ms)`
        );
        return true;
      }
    }
    return false;
  },

  // Mark the start of loading - but don't start recovery timers immediately
  markLoadStart: () => {
    sessionStorage.setItem("rapiddoc_load_start_time", Date.now().toString());
    console.log("📱 Load started at:", new Date().toISOString());
  },

  // Clear the loading marker
  clearLoadMarker: () => {
    const loadTime = sessionStorage.getItem("rapiddoc_load_start_time");
    if (loadTime) {
      const duration = Date.now() - parseInt(loadTime);
      console.log(`📱 Load completed in ${duration}ms`);
    }
    sessionStorage.removeItem("rapiddoc_load_start_time");
  },

  // Attempt to recover from a stuck session
  recoverSession: async () => {
    console.log("🔄 Attempting to recover from stuck session...");

    try {
      // Clear all session-related storage
      localStorage.removeItem("sb-supabase-auth-token");

      // Clear all rapiddoc_ prefixed items
      Object.keys(localStorage).forEach((key) => {
        if (key.startsWith("rapiddoc_")) {
          localStorage.removeItem(key);
        }
      });

      Object.keys(sessionStorage).forEach((key) => {
        if (key.startsWith("rapiddoc_")) {
          sessionStorage.removeItem(key);
        }
      });

      // Sign out from Supabase (but don't wait for it)
      supabase.auth.signOut().catch((err) => {
        console.error("Error during recovery sign out:", err);
      });

      console.log("✅ Session recovery complete - reloading page");

      // Reload the page to start fresh
      window.location.reload();

      return true;
    } catch (err) {
      console.error("❌ Session recovery failed:", err);
      return false;
    }
  },

  // Add a recovery button to the UI if we detect a stuck session
  addRecoveryButton: () => {
    // Check if button already exists
    if (document.getElementById("session-recovery-btn")) {
      return;
    }

    const button = document.createElement("button");
    button.id = "session-recovery-btn";
    button.innerText = "Unstuck Session";
    button.style.position = "fixed";
    button.style.bottom = "20px";
    button.style.left = "20px";
    button.style.padding = "8px 16px";
    button.style.backgroundColor = "#f44336";
    button.style.color = "white";
    button.style.border = "none";
    button.style.borderRadius = "4px";
    button.style.cursor = "pointer";
    button.style.zIndex = "9999";
    button.style.boxShadow = "0 2px 5px rgba(0,0,0,0.2)";

    button.onclick = () => {
      sessionRecovery.recoverSession();
    };

    document.body.appendChild(button);
  },
};

export default sessionRecovery;
