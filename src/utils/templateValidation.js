/**
 * Template Validation Utilities
 * Comprehensive validation for template creation and editing
 */

/**
 * Validate template name
 * @param {string} name - Template name
 * @returns {Object} Validation result
 */
export const validateTemplateName = (name) => {
  if (!name || typeof name !== 'string') {
    return { isValid: false, error: 'Template name is required' };
  }
  
  const trimmedName = name.trim();
  if (trimmedName.length === 0) {
    return { isValid: false, error: 'Template name cannot be empty' };
  }
  
  if (trimmedName.length > 100) {
    return { isValid: false, error: 'Template name must be 100 characters or less' };
  }
  
  if (trimmedName.length < 3) {
    return { isValid: false, error: 'Template name must be at least 3 characters long' };
  }
  
  // Check for invalid characters
  const invalidChars = /[<>:"/\\|?*]/;
  if (invalidChars.test(trimmedName)) {
    return { isValid: false, error: 'Template name contains invalid characters' };
  }
  
  return { isValid: true, value: trimmedName };
};

/**
 * Validate template description
 * @param {string} description - Template description
 * @returns {Object} Validation result
 */
export const validateTemplateDescription = (description) => {
  if (!description) {
    return { isValid: true, value: '' }; // Description is optional
  }
  
  if (typeof description !== 'string') {
    return { isValid: false, error: 'Description must be a string' };
  }
  
  const trimmedDescription = description.trim();
  if (trimmedDescription.length > 500) {
    return { isValid: false, error: 'Description must be 500 characters or less' };
  }
  
  return { isValid: true, value: trimmedDescription };
};

/**
 * Validate template category
 * @param {string} category - Template category
 * @returns {Object} Validation result
 */
export const validateTemplateCategory = (category) => {
  const validCategories = [
    'Business', 'Academic', 'Creative', 'Literature', 'Technical', 
    'Marketing', 'Education', 'Personal', 'Professional', 'Minimal'
  ];
  
  if (!category || typeof category !== 'string') {
    return { isValid: false, error: 'Category is required' };
  }
  
  const trimmedCategory = category.trim();
  if (!validCategories.includes(trimmedCategory)) {
    return { isValid: false, error: 'Invalid category selected' };
  }
  
  return { isValid: true, value: trimmedCategory };
};

/**
 * Validate template tags
 * @param {Array} tags - Template tags
 * @returns {Object} Validation result
 */
export const validateTemplateTags = (tags) => {
  if (!tags) {
    return { isValid: true, value: [] }; // Tags are optional
  }
  
  if (!Array.isArray(tags)) {
    return { isValid: false, error: 'Tags must be an array' };
  }
  
  if (tags.length > 10) {
    return { isValid: false, error: 'Maximum 10 tags allowed' };
  }
  
  const validTags = [];
  const errors = [];
  
  for (let i = 0; i < tags.length; i++) {
    const tag = tags[i];
    
    if (typeof tag !== 'string') {
      errors.push(`Tag ${i + 1}: Must be a string`);
      continue;
    }
    
    const trimmedTag = tag.trim();
    if (trimmedTag.length === 0) {
      errors.push(`Tag ${i + 1}: Cannot be empty`);
      continue;
    }
    
    if (trimmedTag.length > 30) {
      errors.push(`Tag ${i + 1}: Must be 30 characters or less`);
      continue;
    }
    
    if (validTags.includes(trimmedTag)) {
      errors.push(`Tag ${i + 1}: Duplicate tag "${trimmedTag}"`);
      continue;
    }
    
    validTags.push(trimmedTag);
  }
  
  if (errors.length > 0) {
    return { isValid: false, error: errors.join(', ') };
  }
  
  return { isValid: true, value: validTags };
};

/**
 * Validate image file
 * @param {File} imageFile - Image file to validate
 * @returns {Object} Validation result
 */
export const validateImageFile = (imageFile) => {
  if (!imageFile) {
    return { isValid: false, error: 'Background image is required' };
  }
  
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!allowedTypes.includes(imageFile.type)) {
    return { 
      isValid: false, 
      error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.' 
    };
  }
  
  // Check file size (max 10MB)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (imageFile.size > maxSize) {
    return { 
      isValid: false, 
      error: 'File size too large. Maximum size is 10MB.' 
    };
  }
  
  // Check minimum file size (1KB to avoid empty files)
  const minSize = 1024; // 1KB
  if (imageFile.size < minSize) {
    return { 
      isValid: false, 
      error: 'File size too small. Minimum size is 1KB.' 
    };
  }
  
  return { isValid: true, value: imageFile };
};

/**
 * Validate image dimensions
 * @param {Object} dimensions - Image dimensions {width, height}
 * @returns {Object} Validation result
 */
export const validateImageDimensions = (dimensions) => {
  if (!dimensions || typeof dimensions !== 'object') {
    return { isValid: false, error: 'Image dimensions are required' };
  }
  
  const { width, height } = dimensions;
  
  if (!Number.isInteger(width) || !Number.isInteger(height)) {
    return { isValid: false, error: 'Invalid image dimensions' };
  }
  
  if (width <= 0 || height <= 0) {
    return { isValid: false, error: 'Image dimensions must be positive' };
  }
  
  // Minimum dimensions
  const minWidth = 400;
  const minHeight = 300;
  if (width < minWidth || height < minHeight) {
    return { 
      isValid: false, 
      error: `Image too small. Minimum dimensions: ${minWidth}×${minHeight} pixels` 
    };
  }
  
  // Maximum dimensions
  const maxWidth = 5000;
  const maxHeight = 5000;
  if (width > maxWidth || height > maxHeight) {
    return { 
      isValid: false, 
      error: `Image too large. Maximum dimensions: ${maxWidth}×${maxHeight} pixels` 
    };
  }
  
  return { isValid: true, value: dimensions };
};

/**
 * Validate text overlay
 * @param {Object} overlay - Text overlay configuration
 * @param {number} index - Overlay index for error messages
 * @returns {Object} Validation result
 */
export const validateTextOverlay = (overlay, index = 0) => {
  const errors = [];
  
  if (!overlay || typeof overlay !== 'object') {
    return { isValid: false, error: `Overlay ${index + 1}: Invalid overlay configuration` };
  }
  
  // Validate ID
  if (!overlay.id || typeof overlay.id !== 'string' || overlay.id.trim().length === 0) {
    errors.push(`Overlay ${index + 1}: ID is required`);
  }
  
  // Validate type
  if (!overlay.type || overlay.type !== 'text') {
    errors.push(`Overlay ${index + 1}: Type must be 'text'`);
  }
  
  // Validate placeholder
  if (!overlay.placeholder || typeof overlay.placeholder !== 'string') {
    errors.push(`Overlay ${index + 1}: Placeholder text is required`);
  }
  
  // Validate position
  if (!overlay.position || typeof overlay.position !== 'object') {
    errors.push(`Overlay ${index + 1}: Position configuration is required`);
  } else {
    const { x, y, width, height } = overlay.position;
    if (!Number.isInteger(x) || x < 0) {
      errors.push(`Overlay ${index + 1}: Invalid X position`);
    }
    if (!Number.isInteger(y) || y < 0) {
      errors.push(`Overlay ${index + 1}: Invalid Y position`);
    }
    if (!Number.isInteger(width) || width <= 0) {
      errors.push(`Overlay ${index + 1}: Invalid width`);
    }
    if (!Number.isInteger(height) || height <= 0) {
      errors.push(`Overlay ${index + 1}: Invalid height`);
    }
  }
  
  // Validate styling
  if (!overlay.styling || typeof overlay.styling !== 'object') {
    errors.push(`Overlay ${index + 1}: Styling configuration is required`);
  } else {
    const { fontSize, fontFamily, color } = overlay.styling;
    if (!Number.isInteger(fontSize) || fontSize <= 0 || fontSize > 200) {
      errors.push(`Overlay ${index + 1}: Font size must be between 1 and 200`);
    }
    if (!fontFamily || typeof fontFamily !== 'string') {
      errors.push(`Overlay ${index + 1}: Font family is required`);
    }
    if (!color || typeof color !== 'string' || !/^#[0-9A-Fa-f]{6}$/.test(color)) {
      errors.push(`Overlay ${index + 1}: Valid color (hex format) is required`);
    }
  }
  
  if (errors.length > 0) {
    return { isValid: false, error: errors.join(', ') };
  }
  
  return { isValid: true, value: overlay };
};

/**
 * Validate complete template data
 * @param {Object} templateData - Complete template data
 * @returns {Object} Validation result with detailed errors
 */
export const validateCompleteTemplate = (templateData) => {
  const errors = {};
  let isValid = true;
  
  // Validate name
  const nameValidation = validateTemplateName(templateData.name);
  if (!nameValidation.isValid) {
    errors.name = nameValidation.error;
    isValid = false;
  }
  
  // Validate description
  const descriptionValidation = validateTemplateDescription(templateData.description);
  if (!descriptionValidation.isValid) {
    errors.description = descriptionValidation.error;
    isValid = false;
  }
  
  // Validate category
  const categoryValidation = validateTemplateCategory(templateData.category);
  if (!categoryValidation.isValid) {
    errors.category = categoryValidation.error;
    isValid = false;
  }
  
  // Validate tags
  const tagsValidation = validateTemplateTags(templateData.tags);
  if (!tagsValidation.isValid) {
    errors.tags = tagsValidation.error;
    isValid = false;
  }
  
  // Validate image dimensions
  if (templateData.background_image_width && templateData.background_image_height) {
    const dimensionsValidation = validateImageDimensions({
      width: templateData.background_image_width,
      height: templateData.background_image_height
    });
    if (!dimensionsValidation.isValid) {
      errors.image = dimensionsValidation.error;
      isValid = false;
    }
  }
  
  // Validate text overlays
  if (templateData.text_overlays && templateData.text_overlays.overlays) {
    const overlayErrors = [];
    templateData.text_overlays.overlays.forEach((overlay, index) => {
      const overlayValidation = validateTextOverlay(overlay, index);
      if (!overlayValidation.isValid) {
        overlayErrors.push(overlayValidation.error);
      }
    });
    
    if (overlayErrors.length > 0) {
      errors.overlays = overlayErrors.join('; ');
      isValid = false;
    }
  }
  
  return {
    isValid,
    errors,
    errorCount: Object.keys(errors).length
  };
};

export default {
  validateTemplateName,
  validateTemplateDescription,
  validateTemplateCategory,
  validateTemplateTags,
  validateImageFile,
  validateImageDimensions,
  validateTextOverlay,
  validateCompleteTemplate
};
