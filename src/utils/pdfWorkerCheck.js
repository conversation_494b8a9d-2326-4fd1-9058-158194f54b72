/**
 * PDF.js Worker Health Check Utility
 * Tests PDF.js configuration and worker availability
 */

import { testPdfLibrary } from "../services/pdfExtractionService";

/**
 * Perform comprehensive PDF.js health check
 * @returns {Promise<Object>} Health check result
 */
export const performPdfHealthCheck = async () => {
  console.log("🔍 Starting PDF.js health check...");

  try {
    // Test library loading
    const libraryTest = testPdfLibrary();
    if (!libraryTest.success) {
      return {
        success: false,
        error: libraryTest.error,
        recommendation: "Refresh the page and check your internet connection",
      };
    }

    // Test worker file accessibility
    try {
      const workerResponse = await fetch("/pdf.worker.min.js", {
        method: "HEAD",
      });
      if (!workerResponse.ok) {
        throw new Error(`Worker file not accessible: ${workerResponse.status}`);
      }
    } catch (workerError) {
      console.warn("Local worker file not accessible, will use CDN fallback");
      return {
        success: true,
        warning: "Using CDN fallback for PDF worker",
        workerSource: "cdn",
      };
    }

    console.log("✅ PDF.js health check passed");
    return {
      success: true,
      message: "PDF.js is ready for use",
      workerSource: "local",
    };
  } catch (error) {
    console.error("❌ PDF.js health check failed:", error);
    return {
      success: false,
      error: error.message,
      recommendation:
        "Please refresh the page or contact support if the issue persists",
    };
  }
};

/**
 * Display health check results to user if needed
 * @param {Object} result - Health check result
 */
export const handleHealthCheckResult = (result) => {
  if (!result.success) {
    console.error("PDF functionality may not work properly:", result.error);
    // Could show a toast notification to user here
  } else if (result.warning) {
    console.warn("PDF functionality:", result.warning);
  } else {
    console.log("PDF functionality is ready");
  }
};
