/**
 * Validation utilities for user preferences
 * Provides client-side validation with detailed error messages
 */

import { TIMEZONE_OPTIONS } from './timezones';

// Valid enum values for preferences (MVP Essential only - language removed)

/**
 * Validate a single preference field
 * @param {string} field - Field name
 * @param {any} value - Field value
 * @returns {Object} Validation result with isValid and error message
 */
export const validatePreferenceField = (field, value) => {
  const result = { isValid: true, error: null };

  switch (field) {
    case 'timezone':
      if (!value || typeof value !== 'string') {
        result.isValid = false;
        result.error = 'Timezone is required';
      } else if (!TIMEZONE_OPTIONS.some(tz => tz.value === value)) {
        result.isValid = false;
        result.error = 'Invalid timezone';
      }
      break;

    case 'autoSave':
    case 'emailNotifications':
      if (typeof value !== 'boolean') {
        result.isValid = false;
        result.error = `${field} must be a boolean value`;
      }
      break;

    default:
      // Unknown field - allow it but warn
      console.warn(`Unknown preference field: ${field}`);
      break;
  }

  return result;
};

/**
 * Validate all preferences at once
 * @param {Object} preferences - Preferences object
 * @returns {Object} Validation result with isValid, errors object, and summary
 */
export const validateAllPreferences = (preferences) => {
  const errors = {};
  let isValid = true;

  // Validate each field
  Object.keys(preferences).forEach(field => {
    const validation = validatePreferenceField(field, preferences[field]);
    if (!validation.isValid) {
      errors[field] = validation.error;
      isValid = false;
    }
  });

  // Additional cross-field validations
  if (preferences.pushNotifications && !preferences.emailNotifications) {
    // This is just a warning, not an error
    console.warn('Push notifications enabled without email notifications');
  }

  return {
    isValid,
    errors,
    errorCount: Object.keys(errors).length,
    summary: isValid ? 'All preferences are valid' : `${Object.keys(errors).length} validation errors found`
  };
};

/**
 * Sanitize preference values to prevent XSS and ensure data integrity
 * @param {Object} preferences - Raw preferences object
 * @returns {Object} Sanitized preferences object
 */
export const sanitizePreferences = (preferences) => {
  const sanitized = {};

  Object.keys(preferences).forEach(key => {
    const value = preferences[key];

    if (typeof value === 'string') {
      // Basic string sanitization - remove HTML tags and trim
      sanitized[key] = value.replace(/<[^>]*>/g, '').trim();
    } else if (typeof value === 'boolean') {
      // Ensure boolean values
      sanitized[key] = Boolean(value);
    } else {
      // Keep other types as-is but log warning
      console.warn(`Unexpected preference value type for ${key}:`, typeof value);
      sanitized[key] = value;
    }
  });

  return sanitized;
};

/**
 * Get user-friendly error messages for common validation failures (MVP Essential only - language removed)
 */
export const getFieldErrorMessage = (field, error) => {
  const friendlyMessages = {
    timezone: 'Please select a valid timezone',
    emailNotifications: 'Email notification setting must be true or false',
    autoSave: 'Auto-save setting must be true or false'
  };

  return friendlyMessages[field] || error || 'Invalid value';
};

export default {
  validatePreferenceField,
  validateAllPreferences,
  sanitizePreferences,
  getFieldErrorMessage
};
