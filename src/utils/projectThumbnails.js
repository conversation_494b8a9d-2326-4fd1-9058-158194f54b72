/**
 * Document Image Utilities
 *
 * Functions for extracting and processing images from document content
 * to use as thumbnail images for document cards.
 */

/**
 * Extract the first image URL from document content
 * @param {Object} document - Document object with generated_content
 * @returns {string|null} First image URL found or null
 */
export const extractDocumentThumbnail = (document) => {
  if (!document?.generated_content) {
    return null;
  }

  const generatedContent = document.generated_content;

  // First check for editorHTML at the root level
  if (generatedContent.editorHTML) {
    const rootEditorImage = extractImageUrlDirectly(
      generatedContent.editorHTML
    );
    if (rootEditorImage) {
      return rootEditorImage;
    }
  }

  // Check introduction content next
  const introImage = extractImageFromContent(generatedContent.introduction);
  if (introImage) {
    return introImage;
  }

  // Check chapters content
  if (generatedContent.chapters && Array.isArray(generatedContent.chapters)) {
    for (let i = 0; i < generatedContent.chapters.length; i++) {
      const chapter = generatedContent.chapters[i];
      const chapterImage = extractImageFromContent(chapter);
      if (chapterImage) {
        return chapterImage;
      }
    }
  }

  // Check conclusion content as last resort
  const conclusionImage = extractImageFromContent(generatedContent.conclusion);
  if (conclusionImage) {
    console.debug(
      "ProjectThumbnails: Found image in conclusion:",
      conclusionImage
    );
    return conclusionImage;
  }

  console.debug(
    "DocumentThumbnails: No images found in content for document:",
    document.title
  );
  return null;
};

/**
 * Extract image URLs from HTML string without using regex
 * @param {string} html - HTML string to analyze
 * @returns {string|null} First image URL found or null
 */
export const extractImageUrlDirectly = (html) => {
  if (!html || typeof html !== "string") return null;

  // Find first occurrence of <img
  const imgTagPos = html.indexOf("<img");
  if (imgTagPos === -1) return null;

  // Find src attribute
  const srcPos = html.indexOf("src=", imgTagPos);
  if (srcPos === -1) return null;

  // Find the quote character used (single or double)
  const afterSrc = html.substring(srcPos + 4);
  const quoteChar = afterSrc[0];

  if (quoteChar !== '"' && quoteChar !== "'") return null;

  // Find the closing quote
  const startUrlPos = srcPos + 5; // src=" = 5 chars
  const closeQuotePos = html.indexOf(quoteChar, startUrlPos);
  if (closeQuotePos === -1) return null;

  // Extract the URL
  return html.substring(startUrlPos, closeQuotePos);
};

/**
 * Extract first image URL from section content
 * @param {Object|string} section - Section object or content string
 * @returns {string|null} First image URL found or null
 */
const extractImageFromContent = (section) => {
  // Handle both string content and section objects
  if (typeof section === "object" && section !== null) {
    // First check editorHTML if available (likely to have HTML images)
    if (section.editorHTML && typeof section.editorHTML === "string") {
      // Try direct string extraction (no regex)
      const imageUrl = extractImageUrlDirectly(section.editorHTML);
      if (imageUrl) {
        return imageUrl.trim();
      }
    }

    // If no images in editorHTML, check content field
    if (section.content && typeof section.content === "string") {
      return extractImageFromContent(section.content);
    }

    return null;
  }

  // Process string content
  if (!section || typeof section !== "string") {
    return null;
  }

  // Match markdown image syntax: ![alt](url)
  const markdownImageRegex = /!\[.*?\]\((.*?)\)/;
  const markdownMatch = section.match(markdownImageRegex);
  if (markdownMatch && markdownMatch[1]) {
    return markdownMatch[1].trim();
  }

  // Try direct extraction for HTML images
  if (section.includes("<img")) {
    const imageUrl = extractImageUrlDirectly(section);
    if (imageUrl) {
      return imageUrl.trim();
    }
  }

  // Fallback to regex for HTML
  const htmlImageRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/i;
  const htmlMatch = section.match(htmlImageRegex);
  if (htmlMatch && htmlMatch[1]) {
    return htmlMatch[1].trim();
  }

  return null;
};

/**
 * Get the best thumbnail URL for a document
 * Uses the following priority:
 * 1. Database-extracted thumbnail (extracted_thumbnail_url)
 * 2. Explicit thumbnail_url (if set)
 * 3. Client-side extraction from content (only if content is available)
 * 4. Returns null if no images found
 *
 * @param {Object} document - Document object
 * @returns {string|null} Best thumbnail URL for the document or null if no images
 */
export const getDocumentThumbnail = (document) => {
  // First check if we have a database-extracted thumbnail
  if (document?.extracted_thumbnail_url) {
    return document.extracted_thumbnail_url;
  }

  // Then try the explicit thumbnail_url if set
  if (document?.thumbnail_url) {
    return document.thumbnail_url;
  }

  // Only try client-side extraction if we have content (will be skipped in list views now)
  // This is a fallback for when viewing a single document that doesn't have an extracted thumbnail
  if (document?.generated_content) {
    const contentImage = extractDocumentThumbnail(document);
    if (contentImage) {
      return contentImage;
    }
  }

  // Return null if no images found - let the component handle the blank state
  return null;
};

// Backward compatibility aliases
export const extractProjectThumbnail = extractDocumentThumbnail;
export const getProjectThumbnail = getDocumentThumbnail;
