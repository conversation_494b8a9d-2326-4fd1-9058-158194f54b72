/**
 * Cache Manager - Handles clearing and managing RapidDoc AI cached data
 * Provides specific solutions for data synchronization issues
 */

// Get document ID from current URL
const getCurrentDocumentId = () => {
  const path = window.location.pathname;
  const match = path.match(/\/document-editor\/([^\/]+)/);
  return match ? match[1] : null;
};

// Clear all RapidDoc AI related data
export const clearAllRapidDocData = () => {
  console.log('🧹 CLEARING ALL RapidDoc AI DATA');
  console.log('==================================');

  const keysToRemove = [];

  // Find all RapidDoc related keys
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (
      key.startsWith('document-') ||
      key.startsWith('rapiddoc_') ||
      key === 'sidebar-collapsed' ||
      key === 'auth-token' ||
      key === 'user-data'
    )) {
      keysToRemove.push(key);
    }
  }

  // Remove all found keys
  keysToRemove.forEach(key => {
    localStorage.removeItem(key);
    console.log('✅ Removed:', key);
  });

  console.log(`✅ Cleared ${keysToRemove.length} items from localStorage`);
  console.log('💡 Recommendation: Refresh the page completely (Ctrl+F5)');

  return keysToRemove.length;
};

// Clear specific document data
export const clearDocumentData = (documentId = null) => {
  const docId = documentId || getCurrentDocumentId();

  if (!docId) {
    console.log('❌ No document ID provided or found in URL');
    return false;
  }

  console.log('🧹 CLEARING DOCUMENT DATA');
  console.log('==========================');
  console.log('Document ID:', docId);

  const keysToRemove = [
    `document-${docId}`,
    `rapiddoc_cached_titleSelection.generatedTitles`,
    `rapiddoc_cached_topicAndNiche.availableSubNiches`,
    `rapiddoc_cached_documentOutline.generatedOutline`,
    'rapiddoc_session_metadata'
  ];

  let removedCount = 0;
  keysToRemove.forEach(key => {
    if (localStorage.getItem(key)) {
      localStorage.removeItem(key);
      console.log('✅ Removed:', key);
      removedCount++;
    }
  });

  console.log(`✅ Cleared ${removedCount} document-related items`);
  console.log('💡 Recommendation: Navigate back to document creator and recreate document');

  return removedCount > 0;
};

// Clear only image-related data while preserving document structure
export const clearImageData = (documentId = null) => {
  const docId = documentId || getCurrentDocumentId();

  if (!docId) {
    console.log('❌ No document ID provided or found in URL');
    return false;
  }

  console.log('🖼️ CLEARING IMAGE DATA ONLY');
  console.log('============================');
  console.log('Document ID:', docId);

  try {
    const documentKey = `document-${docId}`;
    const rawData = localStorage.getItem(documentKey);

    if (!rawData) {
      console.log('❌ No document data found');
      return false;
    }

    const documentData = JSON.parse(rawData);

    // Remove legacy placedImages
    if (documentData.generatedContent && documentData.generatedContent.placedImages) {
      delete documentData.generatedContent.placedImages;
      console.log('✅ Removed legacy placedImages');
    }

    // Remove images from chapter content
    if (documentData.generatedContent && documentData.generatedContent.chapters) {
      documentData.generatedContent.chapters.forEach((chapter, index) => {
        if (chapter.content) {
          const originalContent = chapter.content;
          // Remove markdown images but preserve other content
          chapter.content = chapter.content.replace(/!\[.*?\]\(.*?\)/g, '').replace(/\n\n+/g, '\n\n');

          if (originalContent !== chapter.content) {
            console.log(`✅ Removed images from Chapter ${chapter.number || index + 1}`);
          }
        }
      });
    }

    // Update timestamp
    documentData.lastModified = new Date().toISOString();
    documentData.imageDataCleared = new Date().toISOString();

    // Save cleaned data
    localStorage.setItem(documentKey, JSON.stringify(documentData));
    console.log('✅ Saved cleaned document data');
    console.log('💡 Recommendation: Refresh the page to see changes');

    return true;

  } catch (error) {
    console.error('❌ Error clearing image data:', error);
    return false;
  }
};

// Force document migration and cleanup
export const forceDocumentCleanup = (documentId = null) => {
  const docId = documentId || getCurrentDocumentId();

  if (!docId) {
    console.log('❌ No document ID provided or found in URL');
    return false;
  }

  console.log('🔧 FORCING DOCUMENT CLEANUP');
  console.log('============================');
  console.log('Document ID:', docId);

  try {
    const documentKey = `document-${docId}`;
    const rawData = localStorage.getItem(documentKey);

    if (!rawData) {
      console.log('❌ No document data found');
      return false;
    }

    const documentData = JSON.parse(rawData);

    // Force clean legacy data
    if (documentData.generatedContent) {
      // Remove legacy placedImages completely
      delete documentData.generatedContent.placedImages;

      // Mark as migrated
      documentData.migrated = true;
      documentData.migratedAt = new Date().toISOString();
      documentData.forceCleaned = new Date().toISOString();

      console.log('✅ Removed all legacy image data');
      console.log('✅ Marked document as migrated');
    }

    // Save cleaned data
    localStorage.setItem(documentKey, JSON.stringify(documentData));
    console.log('✅ Document cleanup complete');
    console.log('💡 Recommendation: Refresh the page to see changes');

    return true;

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    return false;
  }
};

// Diagnostic function to check current state
export const checkDocumentState = (documentId = null) => {
  const docId = documentId || getCurrentDocumentId();

  if (!docId) {
    console.log('❌ No document ID provided or found in URL');
    return null;
  }

  console.log('🔍 DOCUMENT STATE CHECK');
  console.log('========================');
  console.log('Document ID:', docId);

  try {
    const documentKey = `document-${docId}`;
    const rawData = localStorage.getItem(documentKey);

    if (!rawData) {
      console.log('❌ No document data found');
      return null;
    }

    const documentData = JSON.parse(rawData);
    const generatedContent = documentData.generatedContent;

    const state = {
      hasGeneratedContent: !!generatedContent,
      hasLegacyPlacedImages: !!(generatedContent && generatedContent.placedImages),
      legacyImageCount: 0,
      chaptersWithBlockImages: 0,
      totalChapters: 0,
      isMigrated: !!documentData.migrated,
      lastModified: documentData.lastModified
    };

    if (generatedContent) {
      // Count legacy images
      if (generatedContent.placedImages) {
        state.legacyImageCount = Object.keys(generatedContent.placedImages)
          .reduce((total, key) => total + generatedContent.placedImages[key].length, 0);
      }

      // Count chapters with block images
      if (generatedContent.chapters) {
        state.totalChapters = generatedContent.chapters.length;
        state.chaptersWithBlockImages = generatedContent.chapters
          .filter(ch => ch.content && ch.content.includes('![')).length;
      }
    }

    console.log('📊 Current State:', state);

    // Recommendations
    console.log('\n💡 Recommendations:');
    if (state.hasLegacyPlacedImages) {
      console.log('⚠️ Legacy placedImages detected - run forceDocumentCleanup()');
    }
    if (!state.isMigrated) {
      console.log('⚠️ Document not marked as migrated - run forceDocumentCleanup()');
    }
    if (state.legacyImageCount > 0 && state.chaptersWithBlockImages > 0) {
      console.log('⚠️ Both legacy and block images present - data conflict possible');
    }
    if (state.legacyImageCount === 0 && state.chaptersWithBlockImages === 0) {
      console.log('✅ No image data conflicts detected');
    }

    return state;

  } catch (error) {
    console.error('❌ Error checking document state:', error);
    return null;
  }
};

// Export functions to global scope for console access
if (typeof window !== 'undefined') {
  window.RapidDocCache = {
    clearAll: clearAllRapidDocData,
    clearDocument: clearDocumentData,
    clearImages: clearImageData,
    forceCleanup: forceDocumentCleanup,
    checkState: checkDocumentState
  };

  console.log('🔧 RapidDoc Cache Manager Available:');
  console.log('- window.RapidDocCache.clearAll() - Clear all RapidDoc data');
  console.log('- window.RapidDocCache.clearDocument(docId) - Clear specific document');
  console.log('- window.RapidDocCache.clearImages(docId) - Clear only image data');
  console.log('- window.RapidDocCache.forceCleanup(docId) - Force cleanup and migration');
  console.log('- window.RapidDocCache.checkState(docId) - Check current document state');
}
