// Environment Variables Checker
export const checkEnvironment = () => {
  console.log('🔍 Checking environment variables...')

  const requiredVars = {
    'VITE_SUPABASE_URL': import.meta.env.VITE_SUPABASE_URL,
    'VITE_SUPABASE_ANON_KEY': import.meta.env.VITE_SUPABASE_ANON_KEY
  }

  const optionalVars = {
    'VITE_GEMINI_API_KEY': import.meta.env.VITE_GEMINI_API_KEY,
    'VITE_UNSPLASH_ACCESS_KEY': import.meta.env.VITE_UNSPLASH_ACCESS_KEY,
    'VITE_REPLICATE_API_KEY': import.meta.env.VITE_REPLICATE_API_KEY
  }

  // Check specifically for image generation capabilities
  const imageGenerationStatus = checkImageGenerationCapabilities()

  console.log('Required environment variables:')
  Object.entries(requiredVars).forEach(([key, value]) => {
    if (value) {
      console.log(`✅ ${key}: ${value.substring(0, 20)}...`)
    } else {
      console.log(`❌ ${key}: NOT SET`)
    }
  })

  console.log('\nOptional environment variables (for enhanced features):')
  Object.entries(optionalVars).forEach(([key, value]) => {
    if (value && value !== 'your-api-key-here' && value !== 'your-gemini-api-key-here' && value !== 'your-unsplash-access-key-here' && value !== 'your-replicate-api-key-here') {
      console.log(`✅ ${key}: ${value.substring(0, 20)}...`)
    } else {
      console.log(`⚠️ ${key}: NOT SET (feature will use fallbacks)`)
    }
  })

  // Display image generation status
  console.log('\n🎨 Image Generation Capabilities:')
  console.log(`${imageGenerationStatus.gemini ? '✅' : '⚠️'} Gemini AI Image Generation: ${imageGenerationStatus.gemini ? 'ENABLED' : 'DISABLED (will use mock images)'}`)
  console.log(`${imageGenerationStatus.replicate ? '✅' : '⚠️'} Replicate Image Generation: ${imageGenerationStatus.replicate ? 'ENABLED' : 'DISABLED'}`)
  console.log(`📋 Primary image service: ${imageGenerationStatus.primary}`)

  const allRequiredSet = Object.values(requiredVars).every(Boolean)
  const optionalCount = Object.values(optionalVars).filter(value =>
    value &&
    value !== 'your-api-key-here' &&
    value !== 'your-gemini-api-key-here' &&
    value !== 'your-unsplash-access-key-here' &&
    value !== 'your-replicate-api-key-here'
  ).length

  if (allRequiredSet) {
    console.log(`✅ All required environment variables are set`)
    console.log(`📊 Optional features configured: ${optionalCount}/${Object.keys(optionalVars).length}`)
  } else {
    console.log('❌ Some required environment variables are missing')
  }

  return {
    success: allRequiredSet,
    variables: requiredVars,
    optionalVariables: optionalVars,
    optionalCount
  }
}

// Auto-check on import in development
if (import.meta.env.DEV) {
  checkEnvironment()
}

/**
 * Check image generation capabilities
 * @returns {Object} Image generation status
 */
const checkImageGenerationCapabilities = () => {
  const geminiKey = import.meta.env.VITE_GEMINI_API_KEY;
  const replicateKey = import.meta.env.VITE_REPLICATE_API_KEY;

  const hasGemini = geminiKey && geminiKey !== 'your-gemini-api-key-here';
  const hasReplicate = replicateKey && replicateKey !== 'your-replicate-api-key-here';

  let primary = 'mock';
  if (hasGemini) {
    primary = 'gemini';
  } else if (hasReplicate) {
    primary = 'replicate';
  }

  return {
    gemini: hasGemini,
    replicate: hasReplicate,
    primary,
    hasFunctionalService: hasGemini || hasReplicate
  };
};

export { checkImageGenerationCapabilities };
export default checkEnvironment;
