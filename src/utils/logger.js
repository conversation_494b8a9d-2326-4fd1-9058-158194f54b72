/**
 * Production-Ready Logging Service
 * 
 * Provides structured logging with environment-aware output.
 * In production, only errors are logged to console.
 * In development, all log levels are available.
 */

const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  CRITICAL: 4
};

const LOG_LEVEL_NAMES = {
  [LOG_LEVELS.DEBUG]: 'DEBUG',
  [LOG_LEVELS.INFO]: 'INFO',
  [LOG_LEVELS.WARN]: 'WARN',
  [LOG_LEVELS.ERROR]: 'ERROR',
  [LOG_LEVELS.CRITICAL]: 'CRITICAL'
};

class Logger {
  constructor() {
    // In production, only log errors and critical issues
    this.minLevel = import.meta.env.PROD ? LOG_LEVELS.ERROR : LOG_LEVELS.DEBUG;
    this.isDevelopment = import.meta.env.DEV;
  }

  /**
   * Log a debug message (development only)
   */
  debug(message, ...args) {
    this._log(LOG_LEVELS.DEBUG, message, ...args);
  }

  /**
   * Log an info message (development only)
   */
  info(message, ...args) {
    this._log(LOG_LEVELS.INFO, message, ...args);
  }

  /**
   * Log a warning message (development only)
   */
  warn(message, ...args) {
    this._log(LOG_LEVELS.WARN, message, ...args);
  }

  /**
   * Log an error message (always logged)
   */
  error(message, ...args) {
    this._log(LOG_LEVELS.ERROR, message, ...args);
  }

  /**
   * Log a critical error (always logged)
   */
  critical(message, ...args) {
    this._log(LOG_LEVELS.CRITICAL, message, ...args);
  }

  /**
   * Internal logging method
   */
  _log(level, message, ...args) {
    if (level < this.minLevel) {
      return;
    }

    const timestamp = new Date().toISOString();
    const levelName = LOG_LEVEL_NAMES[level];
    
    // Format the message
    const formattedMessage = `[${timestamp}] ${levelName}: ${message}`;

    // Choose appropriate console method
    switch (level) {
      case LOG_LEVELS.DEBUG:
        console.debug(formattedMessage, ...args);
        break;
      case LOG_LEVELS.INFO:
        console.info(formattedMessage, ...args);
        break;
      case LOG_LEVELS.WARN:
        console.warn(formattedMessage, ...args);
        break;
      case LOG_LEVELS.ERROR:
        console.error(formattedMessage, ...args);
        break;
      case LOG_LEVELS.CRITICAL:
        console.error(`🚨 ${formattedMessage}`, ...args);
        break;
      default:
        console.log(formattedMessage, ...args);
    }
  }

  /**
   * Create a logger instance for a specific component/service
   */
  createLogger(component) {
    return {
      debug: (message, ...args) => this.debug(`[${component}] ${message}`, ...args),
      info: (message, ...args) => this.info(`[${component}] ${message}`, ...args),
      warn: (message, ...args) => this.warn(`[${component}] ${message}`, ...args),
      error: (message, ...args) => this.error(`[${component}] ${message}`, ...args),
      critical: (message, ...args) => this.critical(`[${component}] ${message}`, ...args),
    };
  }
}

// Create and export singleton instance
const logger = new Logger();

export default logger;

// Export convenience methods
export const { debug, info, warn, error, critical } = logger;

// Export component logger creator
export const createLogger = (component) => logger.createLogger(component);
