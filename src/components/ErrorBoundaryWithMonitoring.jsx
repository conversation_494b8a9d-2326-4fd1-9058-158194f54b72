import React from 'react';
import PropTypes from 'prop-types';
import errorMonitor, { ErrorSeverity } from '../utils/errorMonitor';

/**
 * Enhanced Error Boundary component with error monitoring integration
 * Captures React component errors and reports them to the error monitoring service
 */
class ErrorBoundaryWithMonitoring extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    };
    
    // Create a context logger for this error boundary
    this.logger = errorMonitor.createContextLogger('ErrorBoundary', {
      boundaryName: props.name || 'unnamed',
      feature: props.feature
    });
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Capture component stack information
    this.setState({ errorInfo });
    
    // Log the error to the monitoring service with component context
    const errorId = this.logger.error(error, {
      componentStack: errorInfo.componentStack,
      source: 'React Error Boundary',
      props: this._sanitizeProps(),
      feature: this.props.feature
    });
    
    this.setState({ errorId });
    
    // Call onError callback if provided
    if (typeof this.props.onError === 'function') {
      try {
        this.props.onError(error, errorInfo, errorId);
      } catch (callbackError) {
        // Log callback errors - these are important for debugging
        if (import.meta.env.DEV) {
          console.error('Error in onError callback:', callbackError);
        }
      }
    }
  }

  _sanitizeProps() {
    // Create a safe copy of props for error reporting
    // Exclude children and functions
    const { children, onError, fallback, ...otherProps } = this.props;
    
    // Convert props to a safe object for logging
    const safeProps = {};
    Object.keys(otherProps).forEach(key => {
      const value = otherProps[key];
      if (typeof value === 'function') {
        safeProps[key] = '[Function]';
      } else if (React.isValidElement(value)) {
        safeProps[key] = '[React Element]';
      } else if (Array.isArray(value) && value.some(React.isValidElement)) {
        safeProps[key] = '[React Elements Array]';
      } else {
        try {
          // Try to stringify to catch circular references
          JSON.stringify(value);
          safeProps[key] = value;
        } catch (e) {
          safeProps[key] = '[Unstringifiable Object]';
        }
      }
    });
    
    return safeProps;
  }

  resetError = () => {
    this.logger.info('Error boundary reset', { errorId: this.state.errorId });
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    });
  }

  render() {
    const { hasError, error, errorInfo, errorId } = this.state;
    const { children, fallback } = this.props;
    
    if (hasError) {
      // If a custom fallback is provided, use it
      if (fallback) {
        if (typeof fallback === 'function') {
          const result = fallback(error, errorInfo, this.resetError, errorId);
          
          // Check if the result is from our new error fallback format
          if (result && typeof result === 'object' && typeof result.render === 'function') {
            return result.render(React);
          }
          
          return result;
        }
        return fallback;
      }
      
      // Default fallback UI
      return (
        <div className="error-boundary-container">
          <div className="error-boundary-content">
            <h2>Something went wrong</h2>
            <p>An error occurred in this component.</p>
            
            {process.env.NODE_ENV !== 'production' && (
              <details style={{ whiteSpace: 'pre-wrap', marginTop: '10px' }}>
                <summary>Error Details</summary>
                <p>{error && error.toString()}</p>
                <p>Component Stack:</p>
                <pre>{errorInfo && errorInfo.componentStack}</pre>
                {errorId && <p>Error ID: {errorId}</p>}
              </details>
            )}
            
            <button 
              onClick={this.resetError}
              style={{
                marginTop: '15px',
                padding: '8px 16px',
                backgroundColor: '#4a90e2',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Try Again
            </button>
          </div>
        </div>
      );
    }

    // When there's no error, render children normally
    return children;
  }
}

ErrorBoundaryWithMonitoring.propTypes = {
  children: PropTypes.node.isRequired,
  fallback: PropTypes.oneOfType([PropTypes.node, PropTypes.func]),
  onError: PropTypes.func,
  name: PropTypes.string,
  feature: PropTypes.string
};

export default ErrorBoundaryWithMonitoring;