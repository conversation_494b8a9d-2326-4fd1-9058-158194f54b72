import React, { createContext, useContext, useState, useEffect } from 'react'
import { supabase, authHelpers, dbHelpers } from '../lib/supabase'
import { sessionManager, sessionStorage } from '../utils/sessionManager'
import { performanceTracker } from '../utils/performanceTracker'

const AuthContext = createContext({})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [profile, setProfile] = useState(null)
  const [session, setSession] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Initialize auth state
  useEffect(() => {
    let mounted = true

    // Listen for auth changes - this handles both initial session and changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return

        // Handle different auth events
        switch (event) {
          case 'INITIAL_SESSION':
          case 'SIGNED_IN':
            if (session?.user) {
              setSession(session);
              setUser(session.user);
              setError(null);
              
              // Load profile in background - don't block the UI
              loadUserProfile(session.user.id).catch(err => {
                console.error('Profile load failed:', err);
              });
              
              // Start minimal session monitoring
              sessionManager.startSessionMonitoring();
            }
            break;
            
          case 'SIGNED_OUT':
          case 'TOKEN_REFRESHED':
            setSession(session);
            setUser(session?.user ?? null);
            setError(null);
            
            if (!session) {
              setProfile(null);
              sessionManager.stopSessionMonitoring();
              sessionStorage.clear();
            }
            break;
            
          default:
            // For other events, just update the session
            setSession(session);
            setUser(session?.user ?? null);
        }

        // Always set loading to false after any auth event
        if (mounted) {
          setLoading(false);
        }
      }
    )

    // Listen for session manager events
    const removeSessionListener = sessionManager.addListener((event, session) => {
      if (!mounted) return

      switch (event) {
        case 'SESSION_EXPIRED':
        case 'FORCED_LOGOUT':
          setUser(null)
          setProfile(null)
          setSession(null)
          setError('Your session has expired. Please sign in again.')
          break
        case 'SESSION_REFRESH_FAILED':
          setError('Session refresh failed. Please sign in again.')
          break
        case 'SESSION_REFRESHED':
          setSession(session)
          break
        case 'SESSION_ERROR':
          setError('A session error occurred. Please try again.')
          break
      }
    })

    return () => {
      mounted = false
      subscription?.unsubscribe()
      removeSessionListener()
      sessionManager.stopSessionMonitoring()
    }
  }, [])

  // Load user profile from database with caching
  const loadUserProfile = async (userId) => {
    try {

      // Check cache first
      try {
        const cachedProfile = localStorage.getItem(`rapiddoc_profile_${userId}`);
        const cacheTime = localStorage.getItem(`rapiddoc_profile_time_${userId}`);

        if (cachedProfile && cacheTime) {
          const parsedProfile = JSON.parse(cachedProfile);
          const now = Date.now();
          const cacheAge = now - parseInt(cacheTime);

          // Use cache if it's less than 1 hour old
          if (cacheAge < 36000000) {
            setProfile(parsedProfile);

            // Refresh cache in background
            dbHelpers.getUserProfile(userId).then(({ data }) => {
              if (data) {
                setProfile(data);
                localStorage.setItem(`rapiddoc_profile_${userId}`, JSON.stringify(data));
                localStorage.setItem(`rapiddoc_profile_time_${userId}`, now.toString());
              }
            }).catch(err => {
              console.error('Background profile refresh error:', err);
            });

            return;
          } else {
            // Profile cache expired
          }
        } else {
          // No profile cache found
        }
      } catch (cacheErr) {
        console.error('Cache retrieval error:', cacheErr);
        // Continue with database fetch if cache fails
      }

      // Fetch from database
      const { data, error } = await dbHelpers.getUserProfile(userId);


      if (error && error.code !== 'PGRST116') {
        console.error('Profile load error:', error);
        // Don't set error state - just log it, user can still use the app
      } else if (data) {
        setProfile(data);

        // Update cache
        try {
          localStorage.setItem(`rapiddoc_profile_${userId}`, JSON.stringify(data));
          localStorage.setItem(`rapiddoc_profile_time_${userId}`, Date.now().toString());
        } catch (cacheErr) {
          console.warn('Cache storage failed:', cacheErr);
        }
      } else {
        // Profile doesn't exist, create one (but don't block the UI)
        createUserProfile(userId).catch(err => {
          console.error('Profile creation failed:', err);
        });
      }
    } catch (err) {
      console.error('Profile loading failed:', err);
      // Don't block the UI - user can still use the app without profile
    }
  }

  // Create user profile
  const createUserProfile = async (userId) => {
    try {
      const profileData = {
        id: userId,
        email: user?.email,
        full_name: user?.user_metadata?.full_name || user?.email?.split('@')[0],
        avatar_url: user?.user_metadata?.avatar_url,
      }

      const { data, error } = await dbHelpers.createUserProfile(profileData)

      if (error) {
        setError(error.message)
      } else {
        setProfile(data)
      }
    } catch (err) {
      setError(err.message)
    }
  }

  // Sign up
  const signUp = async (email, password, userData = {}) => {
    try {
      setLoading(true)
      setError(null)

      const { data, error } = await authHelpers.signUp(email, password, userData)

      if (error) {
        setError(error.message)
        return { success: false, error: error.message }
      }

      // If email verification is disabled, the user might be logged in immediately
      if (data.session) {
        // The auth state change listener will handle setting the user and profile
      } else if (data.user && !data.user.email_confirmed_at) {
        // User created but email verification disabled - user can login immediately
      }

      return { success: true, data }
    } catch (err) {
      const errorMessage = err.message || 'An error occurred during sign up'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  // Sign in
  const signIn = async (email, password) => {
    try {
      setLoading(true)
      setError(null)

      const { data, error } = await authHelpers.signIn(email, password)

      if (error) {
        setError(error.message)
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (err) {
      const errorMessage = err.message || 'An error occurred during sign in'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  // Sign out
  const signOut = async () => {
    try {
      setLoading(true)
      setError(null)

      // Stop session monitoring
      sessionManager.stopSessionMonitoring()

      // Clear session storage
      sessionStorage.clear()
      sessionManager.clearSessionMetadata()

      const { error } = await authHelpers.signOut()

      if (error) {
        setError(error.message)
        return { success: false, error: error.message }
      }

      // Clear local state
      setUser(null)
      setProfile(null)
      setSession(null)

      return { success: true }
    } catch (err) {
      const errorMessage = err.message || 'An error occurred during sign out'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  // Reset password
  const resetPassword = async (email) => {
    try {
      setLoading(true)
      setError(null)

      const { data, error } = await authHelpers.resetPassword(email)

      if (error) {
        setError(error.message)
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (err) {
      const errorMessage = err.message || 'An error occurred during password reset'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  // Update password
  const updatePassword = async (password) => {
    try {
      setLoading(true)
      setError(null)

      const { data, error } = await authHelpers.updatePassword(password)

      if (error) {
        setError(error.message)
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (err) {
      const errorMessage = err.message || 'An error occurred during password update'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  // Update profile
  const updateProfile = async (updates) => {
    try {
      setLoading(true)
      setError(null)

      if (!user?.id) {
        throw new Error('No user logged in')
      }

      const { data, error } = await dbHelpers.updateUserProfile(user.id, updates)

      if (error) {
        setError(error.message)
        return { success: false, error: error.message }
      }

      setProfile(data)
      return { success: true, data }
    } catch (err) {
      const errorMessage = err.message || 'An error occurred during profile update'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  // Clear error
  const clearError = () => setError(null)

  // Get session info
  const getSessionInfo = async () => {
    return await sessionManager.getSessionInfo()
  }

  // Force refresh session
  const refreshSession = async () => {
    try {
      setLoading(true)
      const success = await sessionManager.refreshSession()
      return { success }
    } catch (err) {
      const errorMessage = err.message || 'Failed to refresh session'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  // Check for suspicious activity
  const checkSuspiciousActivity = () => {
    return sessionManager.checkSuspiciousActivity()
  }

  // Force logout (for security reasons)
  const forceLogout = async () => {
    await sessionManager.forceLogout()
  }

  // Check if user is authenticated
  const isAuthenticated = !!user && !!session

  // Check if user has completed profile
  const hasProfile = !!profile

  const value = {
    // State
    user,
    profile,
    session,
    loading,
    error,
    isAuthenticated,
    hasProfile,

    // Actions
    signUp,
    signIn,
    signOut,
    resetPassword,
    updatePassword,
    updateProfile,
    clearError,
    loadUserProfile,

    // Session management
    getSessionInfo,
    refreshSession,
    checkSuspiciousActivity,
    forceLogout
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export default AuthContext
