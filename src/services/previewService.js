/**
 * Preview Service for DocForge AI
 * Handles content extraction from TipTap and combination with templates for preview
 * 
 * Features:
 * - Extract content from TipTap editor
 * - Combine template with document content
 * - Generate paginated preview data
 * - Calculate content metadata
 */

import imageOverlayService from './imageOverlayService.js';
import errorMonitor, { ErrorSeverity } from '../utils/errorMonitor.js';

/**
 * Extract content and metadata from TipTap editor instance
 * @param {Object} editorInstance - TipTap editor instance
 * @returns {Object} Extracted content and metadata
 */
export const extractContentFromEditor = (editorInstance) => {
  try {
    if (!editorInstance) {
      throw new Error('Editor instance is required');
    }

    // Get HTML and text content
    const html = editorInstance.getHTML();
    const text = editorInstance.getText();
    
    // Calculate content statistics
    const words = text.trim().split(/\s+/).filter(word => word.length > 0);
    const wordCount = words.length;
    const characterCount = text.length;
    const characterCountNoSpaces = text.replace(/\s/g, '').length;
    const estimatedReadTime = Math.ceil(wordCount / 200); // 200 words per minute average
    
    // Clean HTML for export/preview
    const cleanHTML = cleanEditorHTMLForPreview(html);
    
    return {
      html: cleanHTML,
      text: text,
      metadata: {
        wordCount,
        characterCount,
        characterCountNoSpaces,
        estimatedReadTime,
        paragraphCount: (html.match(/<p[^>]*>/g) || []).length,
        headingCount: (html.match(/<h[1-6][^>]*>/g) || []).length
      }
    };

  } catch (error) {
    console.error('Error extracting content from editor:', error);
    errorMonitor.captureError(
      error,
      { operation: 'extractContentFromEditor' },
      ErrorSeverity.MEDIUM
    );
    
    return {
      html: '',
      text: '',
      metadata: {
        wordCount: 0,
        characterCount: 0,
        characterCountNoSpaces: 0,
        estimatedReadTime: 0,
        paragraphCount: 0,
        headingCount: 0
      }
    };
  }
};

/**
 * Clean HTML content for preview display
 * @param {string} html - Raw HTML from TipTap
 * @returns {string} Cleaned HTML
 */
const cleanEditorHTMLForPreview = (html) => {
  if (!html) return '';

  let cleanHTML = html;

  // Remove image suggestion cards and other editor-specific elements
  cleanHTML = cleanHTML.replace(
    /<div[^>]*data-type="image-suggestion-card"[^>]*>[\s\S]*?<\/div>/gi,
    ''
  );

  // Handle problematic image sources
  cleanHTML = cleanHTML.replace(
    /<img([^>]+)src="data:[^"]*"([^>]*)>/gi,
    '<div class="image-placeholder">[Image: Base64 data]</div>'
  );

  // Remove empty paragraphs
  cleanHTML = cleanHTML.replace(/<p>\s*<\/p>/g, '');

  // Clean up excessive line breaks
  cleanHTML = cleanHTML.replace(/(<br\s*\/?>){3,}/gi, '<br><br>');

  // Remove line breaks after headings
  cleanHTML = cleanHTML.replace(/(<\/h[1-6]>)\s*(<br\s*\/?>)+/gi, '$1');

  return cleanHTML.trim();
};

/**
 * Generate enhanced document data with content metadata
 * @param {Object} documentData - Original document data
 * @param {Object} contentMetadata - Content statistics
 * @param {number} totalPages - Total pages in preview
 * @returns {Object} Enhanced document data
 */
export const generateEnhancedDocumentData = (documentData, contentMetadata, totalPages = 1) => {
  return {
    ...documentData,
    // Content-derived metadata
    wordCount: contentMetadata.wordCount,
    pageCount: totalPages,
    readTime: contentMetadata.estimatedReadTime,
    characterCount: contentMetadata.characterCount,
    paragraphCount: contentMetadata.paragraphCount,
    headingCount: contentMetadata.headingCount,
    
    // Date information
    date: new Date().toLocaleDateString(),
    year: new Date().getFullYear(),
    
    // Formatting helpers
    wordCountFormatted: contentMetadata.wordCount.toLocaleString(),
    readTimeFormatted: `${contentMetadata.estimatedReadTime} min read`
  };
};

/**
 * Calculate approximate content height in pixels with improved accuracy
 * @param {string} html - HTML content
 * @param {Object} options - Measurement options
 * @returns {number} Estimated height in pixels
 */
const estimateContentHeight = (html, options = {}) => {
  if (!html || html.trim() === '') return 0;

  const { debug = false, includeMargins = true } = options;

  // Create a temporary element to measure content
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  // ENHANCED: Apply more accurate styling that matches actual preview page
  tempDiv.style.cssText = `
    position: absolute;
    visibility: hidden;
    width: 8.5in;
    padding: 32px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #374151;
    top: -9999px;
    left: -9999px;
    box-sizing: border-box;
    overflow: hidden;
  `;

  // ENHANCED: Apply critical CSS rules that affect height
  const criticalStyles = document.createElement('style');
  criticalStyles.textContent = `
    /* Apply the same styles as preview-pagination.css */
    .temp-measurement * {
      max-width: 100%;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    .temp-measurement ul,
    .temp-measurement ol {
      list-style: initial !important;
      margin: 1rem 0 !important;
      padding-left: 1.5rem !important;
    }

    .temp-measurement li {
      display: list-item !important;
      margin-bottom: 0.25rem !important;
      line-height: 1.6 !important;
    }

    .temp-measurement > * + * {
      margin-top: 1rem;
    }

    .temp-measurement h1 + *,
    .temp-measurement h2 + *,
    .temp-measurement h3 + * {
      margin-top: 0.5rem;
    }

    .temp-measurement blockquote {
      border-left: 4px solid #e5e7eb;
      padding-left: 1rem;
      margin: 1rem 0;
      font-style: italic;
    }

    .temp-measurement table {
      font-size: 0.9em;
      border-collapse: collapse;
      width: 100%;
    }

    .temp-measurement th,
    .temp-measurement td {
      padding: 0.5rem;
      border: 1px solid #e5e7eb;
      word-wrap: break-word;
    }
  `;

  tempDiv.className = 'temp-measurement';
  document.head.appendChild(criticalStyles);
  document.body.appendChild(tempDiv);

  // ENHANCED: Get more accurate measurements
  const height = tempDiv.offsetHeight;
  const scrollHeight = tempDiv.scrollHeight;
  const computedStyle = window.getComputedStyle(tempDiv);
  const marginTop = parseFloat(computedStyle.marginTop) || 0;
  const marginBottom = parseFloat(computedStyle.marginBottom) || 0;

  // Account for potential padding and border in the measurement
  const paddingTop = parseFloat(computedStyle.paddingTop) || 0;
  const paddingBottom = parseFloat(computedStyle.paddingBottom) || 0;
  const borderTop = parseFloat(computedStyle.borderTopWidth) || 0;
  const borderBottom = parseFloat(computedStyle.borderBottomWidth) || 0;

  // Clean up
  document.body.removeChild(tempDiv);
  document.head.removeChild(criticalStyles);

  // Use the most accurate height measurement available
  const contentHeight = Math.max(height, scrollHeight);
  const finalHeight = includeMargins ?
    contentHeight + marginTop + marginBottom :
    contentHeight;

  if (debug) {
    console.log('🔍 HEIGHT MEASUREMENT DEBUG:', {
      html: html.substring(0, 100) + '...',
      measurements: {
        offsetHeight: height,
        scrollHeight: scrollHeight,
        contentHeight,
        marginTop,
        marginBottom,
        paddingTop,
        paddingBottom,
        borderTop,
        borderBottom,
        finalHeight
      }
    });
  }

  return finalHeight;
};

/**
 * Check if an element should be kept together (not split across pages)
 * UPDATED: More selective - only truly critical elements
 * @param {HTMLElement} element - The element to check
 * @returns {boolean} True if element should stay together
 */
const checkElementKeepTogether = (element) => {
  if (!element) return false;

  const tagName = element.tagName.toLowerCase();

  // Only critical visual elements that would break if split
  const criticalKeepTogetherTags = ['img', 'table', 'figure', 'svg'];
  if (criticalKeepTogetherTags.includes(tagName)) {
    return true;
  }

  // Check for explicit CSS classes that indicate keep-together
  if (element.classList.contains('page-break-avoid') ||
      element.classList.contains('keep-together')) {
    return true;
  }

  // Check for explicit CSS styles
  const computedStyle = window.getComputedStyle(element);
  if (computedStyle.pageBreakInside === 'avoid' ||
      computedStyle.breakInside === 'avoid') {
    return true;
  }

  // REMOVED: Automatic keep-together for short elements
  // This allows better space utilization by letting small elements flow naturally

  return false;
};

/**
 * Check if current element should be kept with the next element
 * UPDATED: Prioritizes space utilization over content grouping (ebook-style pagination)
 * @param {HTMLElement} element - Current element
 * @param {HTMLElement} nextElement - Next element
 * @param {number} remainingSpace - Available space on current page
 * @returns {boolean} True if elements should stay together
 */
const shouldKeepElementsTogetherLookAhead = (element, nextElement, remainingSpace = 0) => {
  if (!element || !nextElement) return false;

  const currentTag = element.tagName.toLowerCase();
  const nextTag = nextElement.tagName.toLowerCase();
  const nextElementHeight = nextElement.offsetHeight || 0;

  // CRITICAL: Only keep elements together if they're small and can fit in remaining space
  // This prioritizes space utilization over content grouping

  // Keep very short consecutive list items together (only if they fit)
  if (currentTag === 'li' && nextTag === 'li') {
    const currentHeight = element.offsetHeight || 0;
    // Only keep together if both items are very short and fit in remaining space
    if (currentHeight + nextElementHeight < 80 && currentHeight + nextElementHeight <= remainingSpace) {
      return true;
    }
  }

  // Keep consecutive table rows together only if they're small
  if (currentTag === 'tr' && nextTag === 'tr') {
    const currentHeight = element.offsetHeight || 0;
    if (currentHeight + nextElementHeight < 60 && currentHeight + nextElementHeight <= remainingSpace) {
      return true;
    }
  }

  // REMOVED: Automatic heading keep-together logic
  // Headings will only be kept with following content if there's minimal space impact
  // This allows for better space utilization

  return false;
};

/**
 * Check if element should force a page break before it
 * @param {HTMLElement} element - Element to check
 * @returns {boolean} True if element should start on a new page
 */
const shouldForcePageBreakBefore = (element) => {
  if (!element) return false;

  const tagName = element.tagName.toLowerCase();

  // H1 headings (new chapters) should always start on a new page
  if (tagName === 'h1') {
    return true;
  }

  // Check for explicit page break classes or styles
  if (element.classList.contains('page-break-before') ||
      element.style.pageBreakBefore === 'always' ||
      element.style.breakBefore === 'page') {
    return true;
  }

  return false;
};

/**
 * Split HTML content into logical page chunks based on visual layout
 * @param {string} html - HTML content to paginate
 * @param {number} maxPageHeight - Maximum height per page in pixels
 * @returns {Array<string>} Array of HTML strings, one per page
 */
const splitContentByHeight = (html, maxPageHeight = 992) => { // FIXED: More accurate page height (11in - 64px padding)
  if (!html || html.trim() === '') {
    return ['<div class="empty-content">No content to display</div>'];
  }

  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  // CRITICAL FIX: Handle content wrapped in container elements (like ProseMirror)
  let elements = Array.from(tempDiv.children);

  // If we have a single container element (like <div class="ProseMirror">),
  // extract its children for pagination
  if (elements.length === 1 && elements[0].children.length > 0) {
    const containerElement = elements[0];
    const containerTag = containerElement.tagName.toLowerCase();
    const containerClasses = containerElement.className;

    console.log('🔧 PAGINATION DEBUG: Found container element', {
      tag: containerTag,
      classes: containerClasses,
      childrenCount: containerElement.children.length
    });

    // Extract children from container for pagination
    elements = Array.from(containerElement.children);

    // We'll wrap the paginated content back in the container later
    var wrapInContainer = true;
    var containerWrapper = `<${containerTag}${containerClasses ? ` class="${containerClasses}"` : ''}>`;
    var containerClosing = `</${containerTag}>`;
  } else {
    var wrapInContainer = false;
  }

  if (elements.length === 0) {
    return [html];
  }

  console.log('🔧 PAGINATION DEBUG: Processing elements for height-based pagination', {
    elementCount: elements.length,
    maxPageHeight,
    wrapInContainer,
    pageHeightCalculation: {
      pageHeightInches: 11,
      pageHeightPixels: 11 * 96, // 1056px
      paddingTopBottom: 64, // 32px top + 32px bottom
      availableContentHeight: (11 * 96) - 64 // 992px
    }
  });

  const pages = [];
  let currentPageElements = [];
  let currentPageHeight = 0;
  let pageNumber = 1;

  for (let i = 0; i < elements.length; i++) {
    const element = elements[i];
    const elementHTML = element.outerHTML;
    const elementHeight = estimateContentHeight(elementHTML, { debug: true });

    // Check if this element should force a page break (H1 headings, etc.)
    const shouldForceBreak = shouldForcePageBreakBefore(element);
    const isLastElement = i === elements.length - 1;
    const remainingSpace = maxPageHeight - currentPageHeight;

    // SPACE-UTILIZATION-FIRST APPROACH: Only avoid breaks for critical elements
    const shouldKeepTogether = checkElementKeepTogether(element);
    const nextElement = elements[i + 1];
    const shouldKeepWithNext = shouldKeepElementsTogetherLookAhead(element, nextElement, remainingSpace);

    console.log('🔧 PAGINATION DEBUG: Processing element', {
      pageNumber,
      elementIndex: i,
      elementTag: element.tagName,
      elementHeight,
      currentPageHeight,
      remainingSpace,
      wouldExceed: currentPageHeight + elementHeight > maxPageHeight,
      shouldForceBreak,
      shouldKeepTogether,
      shouldKeepWithNext,
      isLastElement,
      utilization: currentPageHeight > 0 ? ((currentPageHeight / maxPageHeight) * 100).toFixed(1) + '%' : '0%'
    });

    // NEW SPACE-UTILIZATION-FIRST LOGIC
    const wouldExceedPageHeight = currentPageHeight + elementHeight > maxPageHeight;
    const hasCurrentContent = currentPageElements.length > 0;

    // Priority 1: Force breaks for H1 headings (new chapters)
    if (shouldForceBreak && hasCurrentContent) {
      console.log('🔧 PAGINATION DEBUG: Forcing page break for H1 heading');
      // Create current page before H1
      const pageContent = currentPageElements.map(el => el.outerHTML).join('');
      const finalPageContent = wrapInContainer ? `${containerWrapper}${pageContent}${containerClosing}` : pageContent;
      pages.push(finalPageContent);
      pageNumber++;

      // Start new page with H1
      currentPageElements = [element];
      currentPageHeight = elementHeight;
      continue;
    }

    // Priority 2: Maximize space utilization - only break if content truly doesn't fit
    // Ignore keep-together rules unless element is critical (images, tables, etc.)
    const criticalKeepTogether = shouldKeepTogether && ['img', 'table', 'figure', 'blockquote'].includes(element.tagName.toLowerCase());
    const shouldBreakPage = wouldExceedPageHeight && hasCurrentContent && !criticalKeepTogether;

    // Safety check: if element is too large for any page, break anyway
    const elementTooLarge = elementHeight > maxPageHeight * 0.9; // 90% of page height
    const forceBreak = wouldExceedPageHeight && hasCurrentContent && elementTooLarge;

    if (shouldBreakPage || forceBreak) {
      // Create current page
      const pageContent = currentPageElements.map(el => el.outerHTML).join('');
      const finalPageContent = wrapInContainer ? `${containerWrapper}${pageContent}${containerClosing}` : pageContent;

      console.log('🔧 PAGINATION DEBUG: Creating page', {
        pageNumber,
        elementCount: currentPageElements.length,
        finalHeight: currentPageHeight,
        utilization: ((currentPageHeight / maxPageHeight) * 100).toFixed(1) + '%',
        unusedSpace: maxPageHeight - currentPageHeight
      });

      pages.push(finalPageContent);
      pageNumber++;

      // Start new page with current element
      currentPageElements = [element];
      currentPageHeight = elementHeight;
    } else {
      // Add element to current page
      currentPageElements.push(element);
      currentPageHeight += elementHeight;
    }
  }

  // Add remaining content
  if (currentPageElements.length > 0) {
    const pageContent = currentPageElements.map(el => el.outerHTML).join('');
    const finalPageContent = wrapInContainer ? `${containerWrapper}${pageContent}${containerClosing}` : pageContent;
    pages.push(finalPageContent);
  }

  // DEBUG: Check for list elements in input and output
  const inputListCount = html.match(/<[uo]l[^>]*>/g)?.length || 0;
  const inputListItemCount = html.match(/<li[^>]*>/g)?.length || 0;

  let outputListCount = 0;
  let outputListItemCount = 0;
  pages.forEach(page => {
    outputListCount += page.match(/<[uo]l[^>]*>/g)?.length || 0;
    outputListItemCount += page.match(/<li[^>]*>/g)?.length || 0;
  });

  console.log('🔧 PAGINATION DEBUG: Height-based pagination completed', {
    inputElementCount: elements.length,
    outputPageCount: pages.length,
    pageLengths: pages.map(page => page.length),
    listPreservation: {
      inputLists: inputListCount,
      outputLists: outputListCount,
      inputListItems: inputListItemCount,
      outputListItems: outputListItemCount,
      listsPreserved: inputListCount === outputListCount,
      listItemsPreserved: inputListItemCount === outputListItemCount
    }
  });

  return pages.length > 0 ? pages : [html];
};

/**
 * Enhanced pagination with proper page break handling
 * @param {string} html - HTML content to paginate
 * @param {Object} options - Pagination options
 * @returns {Array} Array of HTML strings representing pages
 */
export const paginateContentForPreview = (html, options = {}) => {
  const {
    wordsPerPage = 500,
    maxPageHeight = 992, // Consistent with splitContentByHeight: 11in (1056px) - 64px padding
    useHeightBasedPagination = true,
    preserveHeadings = true
  } = options;

  console.log('🔧 PAGINATION FUNCTION DEBUG: Called with', {
    htmlLength: html?.length || 0,
    htmlType: typeof html,
    isEmpty: !html || html.trim() === '',
    options,
    htmlPreview: html?.substring(0, 100)
  });

  if (!html || html.trim() === '') {
    console.log('🔧 PAGINATION FUNCTION DEBUG: Empty content, returning placeholder');
    return ['<div class="empty-content">No content to display</div>'];
  }

  try {
    // Use height-based pagination for better accuracy
    if (useHeightBasedPagination && typeof document !== 'undefined') {
      console.log('🔧 PAGINATION FUNCTION DEBUG: Using height-based pagination');
      const heightBasedPages = splitContentByHeight(html, maxPageHeight);
      console.log('🔧 PAGINATION FUNCTION DEBUG: Height-based result', {
        pageCount: heightBasedPages.length,
        pageLengths: heightBasedPages.map(page => page.length)
      });
      return heightBasedPages;
    }

    // Fallback to improved word-based pagination
    console.log('🔧 PAGINATION FUNCTION DEBUG: Using word-based fallback');
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    const textContent = tempDiv.textContent || tempDiv.innerText || '';
    const words = textContent.trim().split(/\s+/);

    console.log('🔧 PAGINATION FUNCTION DEBUG: Word analysis', {
      totalWords: words.length,
      wordsPerPage,
      willFitOnOnePage: words.length <= wordsPerPage
    });

    if (words.length <= wordsPerPage) {
      console.log('🔧 PAGINATION FUNCTION DEBUG: Content fits on one page');
      return [html];
    }

    // Improved word-based pagination that respects element boundaries
    const pages = [];
    const elements = Array.from(tempDiv.children);

    let currentPageElements = [];
    let currentWordCount = 0;

    for (const element of elements) {
      const elementText = element.textContent || element.innerText || '';
      const elementWords = elementText.trim().split(/\s+/).length;

      // Check if element has explicit page break
      const hasPageBreak = element.style.pageBreakBefore === 'always' ||
                          element.style.pageBreakAfter === 'always' ||
                          element.classList.contains('page-break');

      if ((currentWordCount + elementWords > wordsPerPage || hasPageBreak) && currentPageElements.length > 0) {
        pages.push(currentPageElements.map(el => el.outerHTML).join(''));
        currentPageElements = [element];
        currentWordCount = elementWords;
      } else {
        currentPageElements.push(element);
        currentWordCount += elementWords;
      }
    }

    // Add remaining content
    if (currentPageElements.length > 0) {
      pages.push(currentPageElements.map(el => el.outerHTML).join(''));
    }

    return pages.length > 0 ? pages : [html];

  } catch (error) {
    console.error('Error paginating content:', error);
    return [html];
  }
};

/**
 * Generate complete preview data combining template and content
 * @param {Object} template - Template definition
 * @param {Object} extractedContent - Content from TipTap
 * @param {Object} documentData - Document metadata
 * @param {Object} options - Preview options
 * @returns {Promise<Object>} Complete preview data
 */
export const generatePreviewData = async (template, extractedContent, documentData, options = {}) => {
  try {
    const {
      includeStyles = true,
      wordsPerPage = 500
    } = options;

    // CRITICAL FIX: Handle both old and new content extraction formats
    // Old format: extractedContent.html is a string
    // New format: extractedContent.html is an object with { raw, cleaned, preview } properties
    let htmlContent = '';

    if (typeof extractedContent.html === 'string') {
      // Old format - direct string
      htmlContent = extractedContent.html;
      console.log('🔍 PAGINATION DEBUG: Using old format (string)', {
        length: htmlContent.length,
        preview: htmlContent.substring(0, 100)
      });
    } else if (extractedContent.html && typeof extractedContent.html === 'object') {
      // New format - object with properties
      htmlContent = extractedContent.html.preview || extractedContent.html.raw || extractedContent.html.cleaned || '';
      console.log('🔍 PAGINATION DEBUG: Using new format (object)', {
        hasPreview: !!extractedContent.html.preview,
        hasRaw: !!extractedContent.html.raw,
        hasCleaned: !!extractedContent.html.cleaned,
        selectedContent: htmlContent.length > 0 ? 'found' : 'empty',
        length: htmlContent.length,
        preview: htmlContent.substring(0, 100)
      });
    } else {
      // Fallback - empty content
      htmlContent = '';
      console.warn('⚠️ PAGINATION DEBUG: No valid HTML content found', {
        extractedContentType: typeof extractedContent,
        extractedContentKeys: Object.keys(extractedContent || {}),
        htmlType: typeof extractedContent?.html,
        htmlValue: extractedContent?.html
      });
    }

    console.log('🔍 PAGINATION DEBUG: About to paginate content', {
      htmlContentLength: htmlContent.length,
      wordsPerPage,
      htmlContentPreview: htmlContent.substring(0, 200)
    });

    let contentPages = paginateContentForPreview(htmlContent, {
      wordsPerPage,
      maxPageHeight: 992, // Consistent: 11in (1056px) - 64px padding = 992px
      useHeightBasedPagination: true,
      preserveHeadings: true
    });

    console.log('🔍 PAGINATION DEBUG: Pagination completed', {
      inputLength: htmlContent.length,
      outputPageCount: contentPages.length,
      pageLengths: contentPages.map(page => page.length),
      firstPagePreview: contentPages[0]?.substring(0, 100)
    });

    // Validate pagination results
    if (contentPages.length === 0) {
      console.warn('⚠️ Pagination returned no pages, using fallback');
      contentPages = ['<div class="empty-content">No content to display</div>'];
    }
    const totalPages = 1 + contentPages.length; // 1 template page + content pages

    // Generate enhanced document data with accurate page count
    const enhancedDocumentData = generateEnhancedDocumentData(
      documentData,
      extractedContent.metadata,
      totalPages
    );

    // Generate template page (page 1) using image overlay service
    let templateHTML;
    try {
      console.log('🎨 Generating template preview with image overlay service');
      const templateCanvas = await imageOverlayService.renderTemplate(template, enhancedDocumentData);
      const templateImageData = imageOverlayService.exportAsImage(templateCanvas, 'png', 0.9);

      templateHTML = `<div class="template-page page-break-after" style="width: 100%; height: 100%;">
        <img src="${templateImageData}" alt="Document Cover" style="width: 100%; height: 100%; object-fit: cover; display: block;" />
      </div>`;

      console.log('✅ Template preview generated successfully');
    } catch (error) {
      console.error('❌ Failed to generate template preview:', error);

      // Fallback to basic HTML template
      templateHTML = `<div class="template-page fallback page-break-after" style="text-align: center; padding: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px; margin: 20px; height: calc(100% - 40px); display: flex; flex-direction: column; justify-content: center;">
        <h1 style="font-size: 2.5em; margin-bottom: 20px; font-weight: bold;">${enhancedDocumentData.title || 'Untitled Document'}</h1>
        ${enhancedDocumentData.author ? `<p style="font-size: 1.2em; margin-bottom: 15px; opacity: 0.9;">by ${enhancedDocumentData.author}</p>` : ''}
        ${enhancedDocumentData.description ? `<p style="font-size: 1em; line-height: 1.5; opacity: 0.8; max-width: 600px; margin: 0 auto;">${enhancedDocumentData.description}</p>` : ''}
        <p style="font-size: 0.8em; margin-top: 30px; opacity: 0.6;">Template preview unavailable - using fallback design</p>
      </div>`;
    }

    // Combine all pages
    const allPages = [templateHTML, ...contentPages];

    return {
      pages: allPages,
      metadata: {
        totalPages,
        templatePage: 1,
        contentStartPage: 2,
        contentPageCount: contentPages.length,
        template: {
          id: template.id,
          name: template.name,
          category: template.category
        },
        content: extractedContent.metadata,
        document: enhancedDocumentData
      }
    };

  } catch (error) {
    console.error('Error generating preview data:', error);
    errorMonitor.captureError(
      error,
      { template: template?.id, documentData, options },
      ErrorSeverity.HIGH
    );

    // Return fallback preview data
    return {
      pages: ['<div class="error-message">Preview generation failed</div>'],
      metadata: {
        totalPages: 1,
        templatePage: 1,
        contentStartPage: 2,
        contentPageCount: 0,
        error: error.message
      }
    };
  }
};

/**
 * Update preview data with new template (for template switching)
 * @param {Object} currentPreviewData - Current preview data
 * @param {Object} newTemplate - New template to apply
 * @param {Object} documentData - Document metadata
 * @returns {Promise<Object>} Updated preview data
 */
export const updatePreviewWithNewTemplate = async (currentPreviewData, newTemplate, documentData) => {
  try {
    // Extract content pages (skip template page)
    const contentPages = currentPreviewData.pages.slice(1);
    
    // Generate new enhanced document data
    const enhancedDocumentData = generateEnhancedDocumentData(
      documentData,
      currentPreviewData.metadata.content,
      currentPreviewData.metadata.totalPages
    );

    // Generate new template page using image overlay service
    let newTemplateHTML;
    try {
      console.log('🔄 Updating template preview with new template');
      const newTemplateCanvas = await imageOverlayService.renderTemplate(newTemplate, enhancedDocumentData);
      const newTemplateImageData = imageOverlayService.exportAsImage(newTemplateCanvas, 'png', 0.9);

      newTemplateHTML = `<div class="template-page" style="page-break-after: always; width: 100%; height: 100%;">
        <img src="${newTemplateImageData}" alt="Document Cover" style="width: 100%; height: 100%; object-fit: cover; display: block;" />
      </div>`;

      console.log('✅ Template preview updated successfully');
    } catch (error) {
      console.error('❌ Failed to update template preview:', error);

      // Fallback to basic HTML template
      newTemplateHTML = `<div class="template-page fallback" style="text-align: center; page-break-after: always; padding: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px; margin: 20px;">
        <h1 style="font-size: 2.5em; margin-bottom: 20px; font-weight: bold;">${enhancedDocumentData.title || 'Untitled Document'}</h1>
        ${enhancedDocumentData.author ? `<p style="font-size: 1.2em; margin-bottom: 15px; opacity: 0.9;">by ${enhancedDocumentData.author}</p>` : ''}
        ${enhancedDocumentData.description ? `<p style="font-size: 1em; line-height: 1.5; opacity: 0.8; max-width: 600px; margin: 0 auto;">${enhancedDocumentData.description}</p>` : ''}
        <p style="font-size: 0.8em; margin-top: 30px; opacity: 0.6;">Template preview unavailable - using fallback design</p>
      </div>`;
    }

    // Combine new template with existing content pages
    const updatedPages = [newTemplateHTML, ...contentPages];

    return {
      pages: updatedPages,
      metadata: {
        ...currentPreviewData.metadata,
        template: {
          id: newTemplate.id,
          name: newTemplate.name,
          category: newTemplate.category
        }
      }
    };

  } catch (error) {
    console.error('Error updating preview with new template:', error);
    errorMonitor.captureError(
      error,
      { newTemplate: newTemplate?.id, currentPreviewData },
      ErrorSeverity.MEDIUM
    );

    // Return current preview data if update fails
    return currentPreviewData;
  }
};

/**
 * Generate thumbnail preview for a template
 * @param {Object} template - Template configuration
 * @param {Object} sampleData - Sample document data
 * @returns {Promise<string>} Base64 image data URL for thumbnail
 */
export const generateTemplateThumbnail = async (template, sampleData = null) => {
  try {
    console.log(`🖼️ Generating thumbnail for template: ${template.name}`);

    const defaultSampleData = {
      title: 'Sample Title',
      author: 'Author Name',
      description: 'This is a sample description to show how the template will look.'
    };

    const thumbnailData = sampleData || defaultSampleData;

    // Generate thumbnail using image overlay service
    const thumbnailImage = await imageOverlayService.generatePreview(template, thumbnailData);

    console.log(`✅ Thumbnail generated for template: ${template.name}`);
    return thumbnailImage;

  } catch (error) {
    console.error('Error generating template thumbnail:', error);
    errorMonitor.captureError(
      error,
      { template: template?.id },
      ErrorSeverity.MEDIUM
    );

    // Return null if thumbnail generation fails
    return null;
  }
};

/**
 * Batch generate thumbnails for multiple templates
 * @param {Array} templates - Array of template configurations
 * @param {Object} sampleData - Sample document data
 * @returns {Promise<Object>} Map of template IDs to thumbnail URLs
 */
export const batchGenerateThumbnails = async (templates, sampleData = null) => {
  try {
    console.log(`🖼️ Batch generating thumbnails for ${templates.length} templates`);

    const thumbnailPromises = templates.map(async (template) => {
      try {
        const thumbnail = await generateTemplateThumbnail(template, sampleData);
        return { templateId: template.id, thumbnail };
      } catch (error) {
        console.error(`Failed to generate thumbnail for template ${template.id}:`, error);
        return { templateId: template.id, thumbnail: null };
      }
    });

    const results = await Promise.all(thumbnailPromises);

    // Convert to map
    const thumbnailMap = {};
    results.forEach(({ templateId, thumbnail }) => {
      thumbnailMap[templateId] = thumbnail;
    });

    console.log(`✅ Batch thumbnail generation complete: ${Object.keys(thumbnailMap).length} thumbnails`);
    return thumbnailMap;

  } catch (error) {
    console.error('Error in batch thumbnail generation:', error);
    return {};
  }
};
