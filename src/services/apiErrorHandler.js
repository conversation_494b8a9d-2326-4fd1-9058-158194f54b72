/**
 * Production API Error Handler
 * 
 * Centralized error handling for all API services without mock fallbacks.
 * Provides clear user feedback and actionable guidance for different failure scenarios.
 */

// Fallback logger for initialization issues
const logger = {
  error: (message, data) => {
    if (import.meta.env.DEV) {
      console.error(`[API Error Handler] ${message}`, data);
    }
  },
  warn: (message, data) => {
    if (import.meta.env.DEV) {
      console.warn(`[API Error Handler] ${message}`, data);
    }
  },
  info: (message, data) => {
    if (import.meta.env.DEV) {
      console.info(`[API Error Handler] ${message}`, data);
    }
  },
  debug: (message, data) => {
    if (import.meta.env.DEV) {
      console.debug(`[API Error Handler] ${message}`, data);
    }
  }
};

// Error types for classification
export const API_ERROR_TYPES = {
  QUOTA_EXHAUSTED: 'quota_exhausted',
  RATE_LIMITED: 'rate_limited',
  AUTHENTICATION: 'authentication',
  NETWORK: 'network',
  SERVICE_UNAVAILABLE: 'service_unavailable',
  INVALID_REQUEST: 'invalid_request',
  UNKNOWN: 'unknown'
};

// User-facing error messages
export const ERROR_MESSAGES = {
  [API_ERROR_TYPES.QUOTA_EXHAUSTED]: {
    title: 'API Quota Exhausted',
    message: 'The daily API quota has been reached. Please try again tomorrow or upgrade your plan.',
    actionable: true,
    actions: ['Try again tomorrow', 'Contact support for quota increase']
  },
  [API_ERROR_TYPES.RATE_LIMITED]: {
    title: 'Rate Limit Exceeded',
    message: 'Too many requests in a short time. Please wait a moment before trying again.',
    actionable: true,
    actions: ['Wait 1-2 minutes', 'Try again later']
  },
  [API_ERROR_TYPES.AUTHENTICATION]: {
    title: 'Authentication Error',
    message: 'API authentication failed. Please check your API configuration.',
    actionable: true,
    actions: ['Check API key configuration', 'Contact administrator']
  },
  [API_ERROR_TYPES.NETWORK]: {
    title: 'Network Error',
    message: 'Unable to connect to the service. Please check your internet connection.',
    actionable: true,
    actions: ['Check internet connection', 'Try again in a moment']
  },
  [API_ERROR_TYPES.SERVICE_UNAVAILABLE]: {
    title: 'Service Temporarily Unavailable',
    message: 'The AI service is temporarily unavailable. Please try again later.',
    actionable: true,
    actions: ['Try again in a few minutes', 'Check service status']
  },
  [API_ERROR_TYPES.INVALID_REQUEST]: {
    title: 'Invalid Request',
    message: 'The request could not be processed. Please check your input and try again.',
    actionable: true,
    actions: ['Review your input', 'Try with different parameters']
  },
  [API_ERROR_TYPES.UNKNOWN]: {
    title: 'Unexpected Error',
    message: 'An unexpected error occurred. Please try again or contact support.',
    actionable: true,
    actions: ['Try again', 'Contact support if problem persists']
  }
};

/**
 * Classify API error based on error details
 * @param {Error} error - The error object
 * @param {Object} context - Additional context (service name, etc.)
 * @returns {string} Error type from API_ERROR_TYPES
 */
export const classifyApiError = (error, context = {}) => {
  const errorMessage = error.message?.toLowerCase() || '';
  const statusCode = error.status || error.statusCode;

  // Check for quota exhaustion
  if (errorMessage.includes('quota') || 
      errorMessage.includes('limit exceeded') ||
      statusCode === 429) {
    return API_ERROR_TYPES.QUOTA_EXHAUSTED;
  }

  // Check for rate limiting
  if (errorMessage.includes('rate limit') || 
      errorMessage.includes('too many requests') ||
      statusCode === 429) {
    return API_ERROR_TYPES.RATE_LIMITED;
  }

  // Check for authentication errors
  if (errorMessage.includes('unauthorized') ||
      errorMessage.includes('invalid api key') ||
      errorMessage.includes('authentication') ||
      statusCode === 401 || statusCode === 403) {
    return API_ERROR_TYPES.AUTHENTICATION;
  }

  // Check for network errors
  if (errorMessage.includes('network') ||
      errorMessage.includes('timeout') ||
      errorMessage.includes('connection') ||
      error.name === 'NetworkError' ||
      error.name === 'TimeoutError') {
    return API_ERROR_TYPES.NETWORK;
  }

  // Check for service unavailable
  if (statusCode >= 500 || 
      errorMessage.includes('service unavailable') ||
      errorMessage.includes('internal server error')) {
    return API_ERROR_TYPES.SERVICE_UNAVAILABLE;
  }

  // Check for client errors
  if (statusCode >= 400 && statusCode < 500) {
    return API_ERROR_TYPES.INVALID_REQUEST;
  }

  return API_ERROR_TYPES.UNKNOWN;
};

/**
 * Create standardized API error response
 * @param {Error} error - The original error
 * @param {Object} context - Additional context
 * @returns {Object} Standardized error response
 */
export const createApiErrorResponse = (error, context = {}) => {
  const errorType = classifyApiError(error, context);
  const errorInfo = ERROR_MESSAGES[errorType];

  // Log the error for monitoring
  logger.error('API Error Occurred', {
    errorType,
    originalError: error.message,
    context,
    timestamp: new Date().toISOString(),
    stack: error.stack
  });

  return {
    success: false,
    error: {
      type: errorType,
      title: errorInfo.title,
      message: errorInfo.message,
      actionable: errorInfo.actionable,
      actions: errorInfo.actions,
      timestamp: new Date().toISOString(),
      context: context.service || 'unknown'
    }
  };
};

/**
 * Handle API errors consistently across services
 * @param {Error} error - The error to handle
 * @param {Object} context - Service context
 * @throws {Error} Always throws with standardized error response
 */
export const handleApiError = (error, context = {}) => {
  const errorResponse = createApiErrorResponse(error, context);
  
  // Create a new error with the standardized response
  const apiError = new Error(errorResponse.error.message);
  apiError.apiErrorResponse = errorResponse;
  apiError.type = errorResponse.error.type;
  
  throw apiError;
};

/**
 * Check if error is retryable (for automatic retry logic)
 * @param {string} errorType - Error type from API_ERROR_TYPES
 * @returns {boolean} Whether the error should be retried
 */
export const isRetryableError = (errorType) => {
  return [
    API_ERROR_TYPES.NETWORK,
    API_ERROR_TYPES.SERVICE_UNAVAILABLE,
    API_ERROR_TYPES.RATE_LIMITED
  ].includes(errorType);
};

/**
 * Get retry delay for retryable errors
 * @param {string} errorType - Error type
 * @param {number} attemptNumber - Current attempt number
 * @returns {number} Delay in milliseconds
 */
export const getRetryDelay = (errorType, attemptNumber = 1) => {
  const baseDelays = {
    [API_ERROR_TYPES.RATE_LIMITED]: 60000, // 1 minute
    [API_ERROR_TYPES.NETWORK]: 5000, // 5 seconds
    [API_ERROR_TYPES.SERVICE_UNAVAILABLE]: 30000 // 30 seconds
  };

  const baseDelay = baseDelays[errorType] || 5000;
  return baseDelay * Math.pow(2, attemptNumber - 1); // Exponential backoff
};
