/**
 * Enhanced Content Extraction Service
 * Extracts TipTap content with proper formatting preservation
 * 
 * Features:
 * - Preserve TipTap JSON structure
 * - Extract HTML with proper CSS classes
 * - Include TipTap styles for preview
 * - Calculate comprehensive metadata
 * - Handle content cleaning for export
 */

import errorMonitor, { ErrorSeverity } from '../utils/errorMonitor.js';

/**
 * TipTap CSS styles to include in preview
 * These styles ensure proper formatting in preview and export
 */
export const getTipTapStyles = () => `
  /* TipTap Core Styles */
  .ProseMirror {
    outline: none;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #374151;
  }

  .ProseMirror h1 {
    font-size: 2.25rem;
    font-weight: 700;
    line-height: 1.2;
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: #000000;
    letter-spacing: -0.02em; /* Tighten letter spacing for better readability */
  }

  .ProseMirror h2 {
    font-size: 1.875rem;
    font-weight: 600;
    line-height: 1.3;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    color: #000000;
    letter-spacing: -0.02em; /* Tighten letter spacing for better readability */
  }

  .ProseMirror h3 {
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.4;
    margin-top: 1.25rem;
    margin-bottom: 0.5rem;
    color: #000000;
    letter-spacing: -0.02em; /* Tighten letter spacing for better readability */
  }

  .ProseMirror h4 {
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.4;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    color: #000000;
  }

  .ProseMirror h5 {
    font-size: 1.125rem;
    font-weight: 600;
    line-height: 1.4;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    color: #000000;
  }

  .ProseMirror h6 {
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    color: #000000;
  }

  .ProseMirror p {
    margin-bottom: 1rem;
    line-height: 1.7;
  }

  .ProseMirror ul, .ProseMirror ol {
    list-style: initial !important;
    margin: 1rem 0 !important;
    padding-left: 1.5rem !important;
  }

  .ProseMirror ul {
    list-style-type: disc !important;
  }

  .ProseMirror ol {
    list-style-type: decimal !important;
  }

  .ProseMirror li {
    display: list-item !important;
    margin-bottom: 0.25rem !important;
    line-height: 1.6 !important;
  }

  /* Nested list styling for TipTap */
  .ProseMirror ul ul {
    list-style-type: circle !important;
    margin: 0.5rem 0 !important;
  }

  .ProseMirror ul ul ul {
    list-style-type: square !important;
  }

  .ProseMirror ol ol {
    list-style-type: lower-alpha !important;
    margin: 0.5rem 0 !important;
  }

  .ProseMirror ol ol ol {
    list-style-type: lower-roman !important;
  }

  .ProseMirror blockquote {
    border-left: 4px solid #e5e7eb;
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    color: #6b7280;
  }

  .ProseMirror code {
    background-color: #f3f4f6;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
  }

  .ProseMirror pre {
    background-color: #f3f4f6;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1rem 0;
  }

  .ProseMirror pre code {
    background: none;
    padding: 0;
  }

  .ProseMirror strong {
    font-weight: 700;
  }

  .ProseMirror em {
    font-style: italic;
  }

  .ProseMirror a {
    color: #3b82f6;
    text-decoration: underline;
  }

  .ProseMirror a:hover {
    color: #1d4ed8;
  }

  .ProseMirror img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1rem 0;
  }

  .ProseMirror hr {
    border: none;
    border-top: 2px solid #e5e7eb;
    margin: 2rem 0;
  }

  /* Table styles */
  .ProseMirror table {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
  }

  .ProseMirror th, .ProseMirror td {
    border: 1px solid #e5e7eb;
    padding: 0.5rem;
    text-align: left;
  }

  .ProseMirror th {
    background-color: #f9fafb;
    font-weight: 600;
  }

  /* Placeholder styles */
  .ProseMirror .is-editor-empty:first-child::before {
    content: attr(data-placeholder);
    float: left;
    color: #9ca3af;
    pointer-events: none;
    height: 0;
  }

  /* Page break styles for preview and print */
  .page-break {
    page-break-before: always;
    break-before: page;
  }

  .page-break-after {
    page-break-after: always;
    break-after: page;
  }

  .page-break-avoid {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  /* Ensure headings don't break awkwardly */
  .ProseMirror h1, .ProseMirror h2, .ProseMirror h3 {
    page-break-after: avoid;
    break-after: avoid;
    page-break-inside: avoid;
    break-inside: avoid;
  }

  /* Prevent orphaned content */
  .ProseMirror p {
    orphans: 2;
    widows: 2;
  }

  /* Image handling for page breaks */
  .ProseMirror img {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  /* Table handling for page breaks */
  .ProseMirror table {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  /* Blockquote handling */
  .ProseMirror blockquote {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  /* Code block handling */
  .ProseMirror pre {
    page-break-inside: avoid;
    break-inside: avoid;
  }
`;

/**
 * Extract comprehensive content data from TipTap editor
 * @param {Object} editorInstance - TipTap editor instance
 * @param {Object} options - Extraction options
 * @returns {Object} Enhanced content data with formatting preservation
 */
export const extractEnhancedContent = (editorInstance, options = {}) => {
  try {
    const {
      includeJSON = true,
      includeStyles = true,
      cleanForExport = true
    } = options;

    if (!editorInstance) {
      throw new Error('Editor instance is required');
    }

    // Extract raw content
    const html = editorInstance.getHTML();
    const text = editorInstance.getText();
    const json = includeJSON ? editorInstance.getJSON() : null;

    // Calculate comprehensive metadata
    const metadata = calculateContentMetadata(html, text, json);

    // Process HTML for different use cases
    const processedHTML = {
      raw: html,
      cleaned: cleanForExport ? cleanHTMLForExport(html) : html,
      preview: prepareHTMLForPreview(html)
    };

    // Include styles if requested
    const styles = includeStyles ? {
      tipTap: getTipTapStyles(),
      custom: extractCustomStyles(html)
    } : null;

    return {
      html: processedHTML,
      text: text,
      json: json,
      metadata: metadata,
      styles: styles,
      extractedAt: new Date().toISOString()
    };

  } catch (error) {
    console.error('Error extracting enhanced content:', error);
    errorMonitor.captureError(
      error,
      { operation: 'extractEnhancedContent', options },
      ErrorSeverity.MEDIUM
    );

    // Return fallback content
    return {
      html: { raw: '', cleaned: '', preview: '' },
      text: '',
      json: null,
      metadata: getEmptyMetadata(),
      styles: null,
      extractedAt: new Date().toISOString(),
      error: error.message
    };
  }
};

/**
 * Calculate comprehensive content metadata
 * @param {string} html - HTML content
 * @param {string} text - Plain text content
 * @param {Object} json - TipTap JSON structure
 * @returns {Object} Content metadata
 */
const calculateContentMetadata = (html, text, json) => {
  try {
    // Basic text statistics
    const words = text.trim().split(/\s+/).filter(word => word.length > 0);
    const wordCount = words.length;
    const characterCount = text.length;
    const characterCountNoSpaces = text.replace(/\s/g, '').length;

    // Reading time calculation (200 words per minute average)
    const estimatedReadTime = Math.ceil(wordCount / 200);

    // HTML structure analysis
    const htmlStats = analyzeHTMLStructure(html);

    // JSON structure analysis (if available)
    const jsonStats = json ? analyzeJSONStructure(json) : null;

    // Content complexity scoring
    const complexityScore = calculateComplexityScore(htmlStats, wordCount);

    return {
      // Basic statistics
      wordCount,
      characterCount,
      characterCountNoSpaces,
      estimatedReadTime,
      
      // Structure analysis
      paragraphCount: htmlStats.paragraphs,
      headingCount: htmlStats.headings.total,
      headingDistribution: htmlStats.headings.distribution,
      listCount: htmlStats.lists,
      imageCount: htmlStats.images,
      linkCount: htmlStats.links,
      tableCount: htmlStats.tables,
      
      // Content analysis
      averageWordsPerParagraph: htmlStats.paragraphs > 0 ? Math.round(wordCount / htmlStats.paragraphs) : 0,
      complexityScore,
      
      // JSON structure (if available)
      jsonStructure: jsonStats,
      
      // Timestamps
      analyzedAt: new Date().toISOString()
    };

  } catch (error) {
    console.error('Error calculating content metadata:', error);
    return getEmptyMetadata();
  }
};

/**
 * Analyze HTML structure for metadata
 * @param {string} html - HTML content
 * @returns {Object} HTML structure analysis
 */
const analyzeHTMLStructure = (html) => {
  try {
    const headingMatches = {
      h1: (html.match(/<h1[^>]*>/g) || []).length,
      h2: (html.match(/<h2[^>]*>/g) || []).length,
      h3: (html.match(/<h3[^>]*>/g) || []).length,
      h4: (html.match(/<h4[^>]*>/g) || []).length,
      h5: (html.match(/<h5[^>]*>/g) || []).length,
      h6: (html.match(/<h6[^>]*>/g) || []).length
    };

    return {
      paragraphs: (html.match(/<p[^>]*>/g) || []).length,
      headings: {
        total: Object.values(headingMatches).reduce((sum, count) => sum + count, 0),
        distribution: headingMatches
      },
      lists: (html.match(/<[uo]l[^>]*>/g) || []).length,
      images: (html.match(/<img[^>]*>/g) || []).length,
      links: (html.match(/<a[^>]*>/g) || []).length,
      tables: (html.match(/<table[^>]*>/g) || []).length,
      blockquotes: (html.match(/<blockquote[^>]*>/g) || []).length,
      codeBlocks: (html.match(/<pre[^>]*>/g) || []).length
    };

  } catch (error) {
    console.error('Error analyzing HTML structure:', error);
    return {
      paragraphs: 0,
      headings: { total: 0, distribution: {} },
      lists: 0,
      images: 0,
      links: 0,
      tables: 0,
      blockquotes: 0,
      codeBlocks: 0
    };
  }
};

/**
 * Analyze TipTap JSON structure
 * @param {Object} json - TipTap JSON structure
 * @returns {Object} JSON structure analysis
 */
const analyzeJSONStructure = (json) => {
  try {
    const nodeTypes = {};
    const markTypes = {};

    const analyzeNode = (node) => {
      if (node.type) {
        nodeTypes[node.type] = (nodeTypes[node.type] || 0) + 1;
      }

      if (node.marks) {
        node.marks.forEach(mark => {
          markTypes[mark.type] = (markTypes[mark.type] || 0) + 1;
        });
      }

      if (node.content) {
        node.content.forEach(analyzeNode);
      }
    };

    if (json.content) {
      json.content.forEach(analyzeNode);
    }

    return {
      nodeTypes,
      markTypes,
      totalNodes: Object.values(nodeTypes).reduce((sum, count) => sum + count, 0),
      totalMarks: Object.values(markTypes).reduce((sum, count) => sum + count, 0)
    };

  } catch (error) {
    console.error('Error analyzing JSON structure:', error);
    return null;
  }
};

/**
 * Calculate content complexity score
 * @param {Object} htmlStats - HTML structure statistics
 * @param {number} wordCount - Total word count
 * @returns {number} Complexity score (0-100)
 */
const calculateComplexityScore = (htmlStats, wordCount) => {
  try {
    let score = 0;

    // Base score from word count
    score += Math.min(wordCount / 100, 30); // Max 30 points for length

    // Structure complexity
    score += htmlStats.headings.total * 2; // 2 points per heading
    score += htmlStats.lists * 3; // 3 points per list
    score += htmlStats.tables * 5; // 5 points per table
    score += htmlStats.images * 2; // 2 points per image
    score += htmlStats.links * 1; // 1 point per link
    score += htmlStats.blockquotes * 2; // 2 points per blockquote
    score += htmlStats.codeBlocks * 4; // 4 points per code block

    return Math.min(Math.round(score), 100);

  } catch (error) {
    console.error('Error calculating complexity score:', error);
    return 0;
  }
};

/**
 * Clean HTML content for export
 * @param {string} html - Raw HTML content
 * @returns {string} Cleaned HTML
 */
const cleanHTMLForExport = (html) => {
  if (!html) return '';

  let cleanHTML = html;

  // Remove TipTap-specific attributes
  cleanHTML = cleanHTML.replace(/\s*data-pm-[^=]*="[^"]*"/g, '');
  cleanHTML = cleanHTML.replace(/\s*contenteditable="[^"]*"/g, '');
  cleanHTML = cleanHTML.replace(/\s*spellcheck="[^"]*"/g, '');

  // Remove empty paragraphs
  cleanHTML = cleanHTML.replace(/<p>\s*<\/p>/g, '');

  // Clean up excessive line breaks
  cleanHTML = cleanHTML.replace(/(<br\s*\/?>){3,}/gi, '<br><br>');

  // Remove line breaks after headings
  cleanHTML = cleanHTML.replace(/(<\/h[1-6]>)\s*(<br\s*\/?>)+/gi, '$1');

  // Handle problematic image sources
  cleanHTML = cleanHTML.replace(
    /<img([^>]+)src="data:[^"]*"([^>]*)>/gi,
    '<div class="image-placeholder">[Image: Base64 data]</div>'
  );

  return cleanHTML.trim();
};

/**
 * Prepare HTML for preview display
 * @param {string} html - Raw HTML content
 * @returns {string} Preview-ready HTML
 */
const prepareHTMLForPreview = (html) => {
  if (!html) return '';

  let previewHTML = html;

  // Wrap content in ProseMirror class for styling
  previewHTML = `<div class="ProseMirror">${previewHTML}</div>`;

  // Ensure proper spacing for preview
  previewHTML = previewHTML.replace(/<p><\/p>/g, '<p>&nbsp;</p>');

  return previewHTML;
};

/**
 * Extract custom styles from HTML content
 * @param {string} html - HTML content
 * @returns {string} Custom CSS styles
 */
const extractCustomStyles = (html) => {
  try {
    // Extract any inline styles or style attributes
    const styleMatches = html.match(/style="([^"]*)"/g) || [];
    const customStyles = styleMatches.map(match => {
      const style = match.match(/style="([^"]*)"/)[1];
      return `.custom-style { ${style} }`;
    }).join('\n');

    return customStyles;

  } catch (error) {
    console.error('Error extracting custom styles:', error);
    return '';
  }
};

/**
 * Get empty metadata structure
 * @returns {Object} Empty metadata
 */
const getEmptyMetadata = () => ({
  wordCount: 0,
  characterCount: 0,
  characterCountNoSpaces: 0,
  estimatedReadTime: 0,
  paragraphCount: 0,
  headingCount: 0,
  headingDistribution: {},
  listCount: 0,
  imageCount: 0,
  linkCount: 0,
  tableCount: 0,
  averageWordsPerParagraph: 0,
  complexityScore: 0,
  jsonStructure: null,
  analyzedAt: new Date().toISOString()
});

/**
 * Convert enhanced content to preview data
 * @param {Object} enhancedContent - Enhanced content from extraction
 * @param {Object} documentData - Document metadata
 * @returns {Object} Preview-ready data
 */
export const convertToPreviewData = (enhancedContent, documentData) => {
  try {
    // Enhanced document data with content metadata
    const enhancedDocumentData = {
      ...documentData,
      // Content-derived metadata
      wordCount: enhancedContent.metadata.wordCount,
      characterCount: enhancedContent.metadata.characterCount,
      estimatedReadTime: enhancedContent.metadata.estimatedReadTime,
      complexityScore: enhancedContent.metadata.complexityScore,

      // Formatted display values
      wordCountFormatted: enhancedContent.metadata.wordCount.toLocaleString(),
      readTimeFormatted: `${enhancedContent.metadata.estimatedReadTime} min read`,

      // Date information
      date: new Date().toLocaleDateString(),
      year: new Date().getFullYear()
    };

    return {
      content: enhancedContent.html.preview,
      rawContent: enhancedContent.html.raw,
      cleanContent: enhancedContent.html.cleaned,
      text: enhancedContent.text,
      json: enhancedContent.json,
      metadata: enhancedContent.metadata,
      documentData: enhancedDocumentData,
      styles: enhancedContent.styles,
      extractedAt: enhancedContent.extractedAt
    };

  } catch (error) {
    console.error('Error converting to preview data:', error);
    errorMonitor.captureError(
      error,
      { enhancedContent, documentData },
      ErrorSeverity.MEDIUM
    );

    return {
      content: '',
      rawContent: '',
      cleanContent: '',
      text: '',
      json: null,
      metadata: getEmptyMetadata(),
      documentData: documentData,
      styles: null,
      extractedAt: new Date().toISOString(),
      error: error.message
    };
  }
};

/**
 * Validate enhanced content structure
 * @param {Object} enhancedContent - Enhanced content to validate
 * @returns {Object} Validation result
 */
export const validateEnhancedContent = (enhancedContent) => {
  const errors = [];
  const warnings = [];

  try {
    // Check required properties
    if (!enhancedContent.html) {
      errors.push('Missing HTML content');
    } else {
      if (!enhancedContent.html.raw) warnings.push('Missing raw HTML');
      if (!enhancedContent.html.cleaned) warnings.push('Missing cleaned HTML');
      if (!enhancedContent.html.preview) warnings.push('Missing preview HTML');
    }

    if (!enhancedContent.metadata) {
      errors.push('Missing content metadata');
    } else {
      if (enhancedContent.metadata.wordCount === 0) {
        warnings.push('Content appears to be empty');
      }
    }

    if (!enhancedContent.text) {
      warnings.push('Missing plain text content');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      score: Math.max(0, 100 - (errors.length * 25) - (warnings.length * 5))
    };

  } catch (error) {
    return {
      valid: false,
      errors: [`Validation error: ${error.message}`],
      warnings: [],
      score: 0
    };
  }
};
