/**
 * User Notification Service
 * 
 * Handles user-facing notifications for API errors and service status.
 * Provides clear, actionable feedback without mock content fallbacks.
 */

import { API_ERROR_TYPES, ERROR_MESSAGES } from './apiErrorHandler.js';
// Fallback logger for initialization issues
const logger = {
  error: (message, data) => {
    if (import.meta.env.DEV) {
      console.error(`[User Notification Service] ${message}`, data);
    }
  },
  warn: (message, data) => {
    if (import.meta.env.DEV) {
      console.warn(`[User Notification Service] ${message}`, data);
    }
  },
  info: (message, data) => {
    if (import.meta.env.DEV) {
      console.info(`[User Notification Service] ${message}`, data);
    }
  }
};

// Notification types
export const NOTIFICATION_TYPES = {
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
  SUCCESS: 'success'
};

// Service status tracking
let serviceStatus = {
  gemini: { available: true, lastCheck: null, quotaExhausted: false },
  replicate: { available: true, lastCheck: null, quotaExhausted: false },
  unsplash: { available: true, lastCheck: null, quotaExhausted: false }
};

/**
 * Create user notification for API errors
 * @param {Object} apiErrorResponse - Standardized API error response
 * @param {string} serviceName - Name of the affected service
 * @returns {Object} User notification object
 */
export const createApiErrorNotification = (apiErrorResponse, serviceName) => {
  const { error } = apiErrorResponse;
  
  return {
    id: `api-error-${Date.now()}`,
    type: NOTIFICATION_TYPES.ERROR,
    title: error.title,
    message: error.message,
    serviceName,
    actions: error.actions,
    timestamp: new Date().toISOString(),
    persistent: true, // Don't auto-dismiss error notifications
    errorType: error.type
  };
};

/**
 * Create service unavailable notification
 * @param {string} serviceName - Name of the service
 * @param {string} feature - Affected feature
 * @returns {Object} User notification object
 */
export const createServiceUnavailableNotification = (serviceName, feature) => {
  return {
    id: `service-unavailable-${serviceName}-${Date.now()}`,
    type: NOTIFICATION_TYPES.WARNING,
    title: `${serviceName} Service Unavailable`,
    message: `${feature} is temporarily unavailable. Please try again later or use alternative options.`,
    serviceName,
    actions: [
      'Try again later',
      'Use manual upload instead',
      'Contact support if issue persists'
    ],
    timestamp: new Date().toISOString(),
    persistent: false,
    autoHide: 10000 // Hide after 10 seconds
  };
};

/**
 * Create quota exhausted notification
 * @param {string} serviceName - Name of the service
 * @param {string} resetTime - When quota resets (if known)
 * @returns {Object} User notification object
 */
export const createQuotaExhaustedNotification = (serviceName, resetTime = null) => {
  const resetMessage = resetTime 
    ? `Quota resets at ${resetTime}.`
    : 'Quota typically resets daily.';

  return {
    id: `quota-exhausted-${serviceName}-${Date.now()}`,
    type: NOTIFICATION_TYPES.ERROR,
    title: `${serviceName} Quota Exhausted`,
    message: `The daily API quota for ${serviceName} has been reached. ${resetMessage}`,
    serviceName,
    actions: [
      'Try again tomorrow',
      'Use alternative service',
      'Contact support for quota increase'
    ],
    timestamp: new Date().toISOString(),
    persistent: true
  };
};

/**
 * Update service status
 * @param {string} serviceName - Name of the service
 * @param {boolean} available - Whether service is available
 * @param {Object} details - Additional status details
 */
export const updateServiceStatus = (serviceName, available, details = {}) => {
  if (serviceStatus[serviceName]) {
    serviceStatus[serviceName] = {
      ...serviceStatus[serviceName],
      available,
      lastCheck: new Date().toISOString(),
      ...details
    };

    logger.info(`Service status updated: ${serviceName}`, {
      available,
      details,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get current service status
 * @param {string} serviceName - Name of the service
 * @returns {Object} Service status object
 */
export const getServiceStatus = (serviceName) => {
  return serviceStatus[serviceName] || { available: false, lastCheck: null };
};

/**
 * Get all service statuses
 * @returns {Object} All service statuses
 */
export const getAllServiceStatuses = () => {
  return { ...serviceStatus };
};

/**
 * Check if any critical services are unavailable
 * @returns {Object} Summary of service availability
 */
export const getCriticalServiceStatus = () => {
  const critical = ['gemini']; // Define critical services
  const unavailable = critical.filter(service => !serviceStatus[service]?.available);
  
  return {
    allAvailable: unavailable.length === 0,
    unavailableServices: unavailable,
    partiallyAvailable: unavailable.length > 0 && unavailable.length < critical.length
  };
};

/**
 * Create feature unavailable message for UI components
 * @param {string} featureName - Name of the feature
 * @param {string} serviceName - Name of the required service
 * @returns {Object} Feature unavailable message
 */
export const createFeatureUnavailableMessage = (featureName, serviceName) => {
  return {
    title: `${featureName} Temporarily Unavailable`,
    message: `This feature requires ${serviceName} which is currently unavailable.`,
    suggestions: [
      'Try again in a few minutes',
      'Use manual alternatives if available',
      'Contact support if the issue persists'
    ],
    showRetry: true,
    showAlternatives: true
  };
};

/**
 * Get user-friendly error message for UI display
 * @param {Error} error - The error object
 * @param {string} context - Context where error occurred
 * @returns {Object} User-friendly error message
 */
export const getUserFriendlyErrorMessage = (error, context = '') => {
  // Check if it's an API error with standardized response
  if (error.apiErrorResponse) {
    return {
      title: error.apiErrorResponse.error.title,
      message: error.apiErrorResponse.error.message,
      actions: error.apiErrorResponse.error.actions,
      type: 'api-error'
    };
  }

  // Generic error handling
  return {
    title: 'Something went wrong',
    message: `An error occurred${context ? ` while ${context}` : ''}. Please try again or contact support if the problem persists.`,
    actions: ['Try again', 'Contact support'],
    type: 'generic-error'
  };
};

/**
 * Log user notification for analytics
 * @param {Object} notification - The notification object
 * @param {string} action - User action taken (viewed, dismissed, etc.)
 */
export const logNotificationAction = (notification, action) => {
  logger.info('User notification action', {
    notificationId: notification.id,
    notificationType: notification.type,
    serviceName: notification.serviceName,
    action,
    timestamp: new Date().toISOString()
  });
};

// Export service status for external monitoring
export { serviceStatus };
