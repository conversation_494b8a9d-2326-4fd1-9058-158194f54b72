/**
 * Gemini Image Generation Service for DocForge AI
 * Handles AI-powered image generation using Google Gemini 2.0 Flash Preview Image Generation
 * 
 * This service replaces the Replicate-based image generation with Gemini's native image generation,
 * while maintaining the same interface for seamless integration with existing components.
 */

import { GoogleGenerativeAI } from "@google/generative-ai";
import errorMonitor from "../utils/errorMonitor";
import { handleApiError, createApiErrorResponse } from './apiErrorHandler.js';
import { updateServiceStatus, createServiceUnavailableNotification } from './userNotificationService.js';

// Environment variables for Gemini API
const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;

// Create a logger for the Gemini Image service
const geminiImageLogger = errorMonitor.createContextLogger("GeminiImageService");

// Gemini Image Generation configuration
const GEMINI_IMAGE_CONFIG = {
  MODEL: 'gemini-2.0-flash-preview-image-generation',
  TIMEOUT: 60000, // 1 minute for image generation
  MAX_RETRIES: 2,
  RETRY_DELAY_BASE: 2000, // 2 seconds base delay
};

// Image styles configuration - adapted for Gemini prompts
export const IMAGE_STYLES = {
  photorealistic: {
    name: 'Photorealistic',
    prompt_suffix: ', photorealistic, high quality, detailed, professional photography',
    negative_prompt: 'cartoon, anime, drawing, painting, sketch, low quality, blurry'
  },
  artistic: {
    name: 'Artistic',
    prompt_suffix: ', artistic style, creative, expressive, beautiful art',
    negative_prompt: 'photorealistic, photograph, low quality, blurry'
  },
  illustration: {
    name: 'Illustration',
    prompt_suffix: ', digital illustration, clean lines, vibrant colors, modern design',
    negative_prompt: 'photorealistic, photograph, messy, low quality'
  },
  professional: {
    name: 'Professional',
    prompt_suffix: ', professional, clean, modern, business style, high quality',
    negative_prompt: 'casual, messy, unprofessional, low quality, blurry'
  },
  minimalist: {
    name: 'Minimalist',
    prompt_suffix: ', minimalist style, clean, simple, elegant, modern',
    negative_prompt: 'cluttered, busy, complex, ornate, low quality'
  }
};

// Image sizes configuration
export const IMAGE_SIZES = {
  landscape: { width: 1024, height: 768, label: 'Landscape (4:3)' },
  portrait: { width: 768, height: 1024, label: 'Portrait (3:4)' },
  square: { width: 1024, height: 1024, label: 'Square (1:1)' },
  wide: { width: 1280, height: 720, label: 'Wide (16:9)' },
  banner: { width: 1200, height: 400, label: 'Banner (3:1)' }
};

// Initialize Gemini AI for image generation
let genAI = null;
let imageModel = null;

// Lazy initialization - will be called when service is first used
let isImageInitialized = false;
const ensureImageInitialized = () => {
  if (isImageInitialized) return;

  if (GEMINI_API_KEY && GEMINI_API_KEY !== 'your-gemini-api-key-here') {
    try {
      geminiImageLogger.info("Initializing Gemini AI for image generation");
      genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
      imageModel = genAI.getGenerativeModel({ model: GEMINI_IMAGE_CONFIG.MODEL });
      geminiImageLogger.info("Gemini Image AI initialized successfully");
      try {
        updateServiceStatus('gemini', true);
      } catch (statusError) {
        console.warn('Could not update service status:', statusError.message);
      }
    } catch (error) {
      geminiImageLogger.error(error, {
        action: "initialize_gemini_image",
        message: "Failed to initialize Gemini Image AI",
      });
      try {
        updateServiceStatus('gemini', false, { error: error.message });
      } catch (statusError) {
        console.warn('Could not update service status:', statusError.message);
      }
    }
  } else {
    geminiImageLogger.warn("Gemini API key not configured - image generation will not be available", {
      source: "initialization",
    });
    try {
      updateServiceStatus('gemini', false, { reason: 'API key not configured' });
    } catch (statusError) {
      console.warn('Could not update service status:', statusError.message);
    }
  }

  isImageInitialized = true;
};

/**
 * Check if Gemini Image Generation is properly configured
 * @returns {boolean} True if configured and ready
 */
export const isGeminiImageConfigured = () => {
  return !!(GEMINI_API_KEY && imageModel);
};

/**
 * Generate image using Gemini API
 * @param {string} prompt - Text prompt for image generation
 * @param {Object} options - Generation options
 * @param {Function} onProgress - Progress callback function
 * @returns {Promise<Object>} Generated image data
 */
export const generateImage = async (prompt, options = {}, onProgress = null) => {
  ensureImageInitialized();
  try {
    // Validate inputs
    if (!prompt || typeof prompt !== 'string' || prompt.trim().length === 0) {
      throw new Error('Valid prompt is required for image generation');
    }

    // Check if Gemini is configured
    if (!isGeminiImageConfigured()) {
      const error = new Error('Gemini image generation is not available. Please configure your API key.');
      geminiImageLogger.error(error, {
        action: 'generate_image',
        prompt: prompt.substring(0, 50),
        reason: 'service_not_configured'
      });
      handleApiError(error, { service: 'Gemini Image Generation' });
    }

    geminiImageLogger.info("Starting Gemini image generation request", {
      prompt: prompt.substring(0, 100),
      options
    });

    // Simulate progress start
    if (onProgress) {
      onProgress({
        status: 'starting',
        progress: 0,
        elapsed: 0
      });
    }

    // Prepare the enhanced prompt with style
    const enhancedPrompt = buildEnhancedPrompt(prompt, options);
    
    // Simulate progress
    if (onProgress) {
      onProgress({
        status: 'processing',
        progress: 0.3,
        elapsed: 1000
      });
    }

    // Generate image with Gemini
    const startTime = Date.now();
    const result = await generateWithGemini(enhancedPrompt, options);
    const elapsed = Date.now() - startTime;

    // Simulate progress completion
    if (onProgress) {
      onProgress({
        status: 'completed',
        progress: 1.0,
        elapsed
      });
    }

    geminiImageLogger.info("Gemini image generation completed", {
      prompt: prompt.substring(0, 50),
      elapsed,
      hasImage: !!result.imageUrl
    });

    return {
      success: true,
      imageUrl: result.imageUrl,
      prompt,
      predictionId: `gemini_${Date.now()}`,
      metadata: {
        model: GEMINI_IMAGE_CONFIG.MODEL,
        style: options.style || 'photorealistic',
        size: options.size || 'landscape',
        generatedAt: new Date().toISOString(),
        provider: 'gemini',
        elapsed
      }
    };

  } catch (error) {
    geminiImageLogger.error(error, {
      action: 'generate_image',
      prompt: prompt.substring(0, 50),
      message: 'Failed to generate image with Gemini'
    });

    // Update service status based on error type
    if (error.message.includes('quota') || error.message.includes('rate limit')) {
      updateServiceStatus('gemini', false, { quotaExhausted: true });
    } else if (error.message.includes('timeout') || error.message.includes('network')) {
      updateServiceStatus('gemini', false, { temporaryFailure: true });
    }

    // Handle the error with proper user messaging
    handleApiError(error, { service: 'Gemini Image Generation' });
  }
};

/**
 * Build enhanced prompt with style modifications
 * @param {string} prompt - Original prompt
 * @param {Object} options - Generation options
 * @returns {string} Enhanced prompt
 */
const buildEnhancedPrompt = (prompt, options = {}) => {
  const style = options.style || 'photorealistic';
  const styleConfig = IMAGE_STYLES[style] || IMAGE_STYLES.photorealistic;
  
  // Build the enhanced prompt
  let enhancedPrompt = prompt.trim();
  
  // Add style suffix
  if (styleConfig.prompt_suffix) {
    enhancedPrompt += styleConfig.prompt_suffix;
  }
  
  // Add size context if needed
  const sizeConfig = IMAGE_SIZES[options.size || 'landscape'];
  if (sizeConfig) {
    enhancedPrompt += `, ${sizeConfig.label} aspect ratio`;
  }
  
  return enhancedPrompt;
};

/**
 * Generate image with Gemini API
 * @param {string} enhancedPrompt - Enhanced prompt with style
 * @param {Object} options - Generation options
 * @returns {Promise<Object>} Generated image result
 */
const generateWithGemini = async (enhancedPrompt, options = {}) => {
  try {
    const response = await imageModel.generateContent({
      contents: [{
        parts: [{ text: enhancedPrompt }]
      }],
      generationConfig: {
        responseModalities: ['TEXT', 'IMAGE']
      }
    });

    // Extract image from response
    const candidate = response.response.candidates[0];
    if (!candidate || !candidate.content || !candidate.content.parts) {
      throw new Error('No content generated in response');
    }

    // Find the image part
    let imageData = null;
    let imagePart = null;
    for (const part of candidate.content.parts) {
      if (part.inlineData && part.inlineData.data) {
        imageData = part.inlineData.data;
        imagePart = part;
        break;
      }
    }

    if (!imageData || !imagePart) {
      throw new Error('No image generated in response');
    }

    // Convert base64 to blob URL for immediate use
    const mimeType = imagePart.inlineData.mimeType || 'image/png';

    // 🗜️ COMPRESS AI-GENERATED IMAGE to reduce document size
    try {
      // Import compression utility
      const { compressBase64Image } = await import('../utils/imageOptimization.js');

      // Compress the image with optimized settings for AI images
      const compressionResult = await compressBase64Image(imageData, {
        format: 'webp', // WebP for best compression
        quality: 0.75,  // Good balance of quality vs size
        maxWidth: 1024, // Reasonable max size for documents
        maxHeight: 1024
      });

      const compressedImageUrl = `data:${compressionResult.mimeType};base64,${compressionResult.base64Data}`;

      geminiImageLogger.info('AI image compressed successfully', {
        originalSize: compressionResult.originalSize,
        compressedSize: compressionResult.compressedSize,
        compressionRatio: compressionResult.compressionRatio,
        dimensions: compressionResult.dimensions,
        format: compressionResult.mimeType
      });

      return {
        imageUrl: compressedImageUrl,
        imageData: compressionResult.base64Data, // Compressed base64 data
        mimeType: compressionResult.mimeType,
        compressionInfo: {
          originalSize: compressionResult.originalSize,
          compressedSize: compressionResult.compressedSize,
          compressionRatio: compressionResult.compressionRatio,
          dimensions: compressionResult.dimensions
        }
      };

    } catch (compressionError) {
      // Fallback to original image if compression fails
      geminiImageLogger.warn('Image compression failed, using original', {
        error: compressionError.message
      });

      const imageUrl = `data:${mimeType};base64,${imageData}`;
      return {
        imageUrl,
        imageData, // Keep raw base64 for potential storage
        mimeType
      };
    }

  } catch (error) {
    geminiImageLogger.error(error, {
      action: 'generate_with_gemini',
      message: 'Gemini API call failed'
    });
    throw error;
  }
};



/**
 * Validate image generation options
 * @param {Object} options - Options to validate
 * @returns {Object} Validated options with defaults
 */
export const validateGenerationOptions = (options = {}) => {
  const validated = { ...options };

  // Validate style
  if (validated.style && !IMAGE_STYLES[validated.style]) {
    validated.style = 'photorealistic';
  }

  // Validate size
  if (validated.size && !IMAGE_SIZES[validated.size]) {
    validated.size = 'landscape';
  }

  // Set defaults
  validated.style = validated.style || 'photorealistic';
  validated.size = validated.size || 'landscape';

  return validated;
};

/**
 * Cancel an ongoing image generation (not applicable for Gemini but kept for interface compatibility)
 * @param {string} predictionId - Prediction ID to cancel
 * @returns {Promise<Object>} Cancellation result
 */
export const cancelGeneration = async (predictionId) => {
  geminiImageLogger.info("Cancel generation requested", { predictionId });

  // Gemini image generation is synchronous, so we can't really cancel it
  // But we return a success response for interface compatibility
  return {
    success: true,
    message: 'Generation cancellation requested',
    predictionId
  };
};

/**
 * Check service health
 * @returns {Promise<Object>} Health check result
 */
export const checkServiceHealth = async () => {
  try {
    if (!isGeminiImageConfigured()) {
      return {
        status: 'unavailable',
        message: 'Gemini API key not configured',
        configured: false
      };
    }

    // Test with a simple generation request
    const testPrompt = 'A simple test image';
    const startTime = Date.now();

    // We'll just check if the model is accessible without actually generating
    const response = await imageModel.generateContent({
      contents: [{
        parts: [{ text: 'Test connection' }]
      }],
      generationConfig: {
        responseModalities: ['TEXT'] // Just text for health check
      }
    });

    const elapsed = Date.now() - startTime;

    return {
      status: 'healthy',
      message: 'Gemini Image Generation service is operational',
      configured: true,
      responseTime: elapsed,
      model: GEMINI_IMAGE_CONFIG.MODEL
    };

  } catch (error) {
    geminiImageLogger.error(error, {
      action: 'health_check',
      message: 'Health check failed'
    });

    return {
      status: 'error',
      message: error.message,
      configured: isGeminiImageConfigured(),
      error: error.message
    };
  }
};

/**
 * Get generation cost estimate (free for Gemini in current tier)
 * @param {Object} options - Generation options
 * @returns {Object} Cost estimate information
 */
export const getGenerationCostEstimate = (options = {}) => {
  // Gemini image generation is currently free in the API tier we're using
  return {
    estimatedCost: 0,
    currency: 'USD',
    breakdown: {
      baseGeneration: 0,
      styleModifier: 0,
      sizeModifier: 0
    },
    note: 'Gemini image generation is currently free within API limits'
  };
};

/**
 * Get service availability information
 * @returns {Object} Service status and capabilities
 */
export const getServiceInfo = () => {
  const isConfigured = isGeminiImageConfigured();

  return {
    available: isConfigured,
    serviceName: 'Gemini Image Generation',
    supportedStyles: Object.keys(IMAGE_STYLES),
    supportedSizes: Object.keys(IMAGE_SIZES),
    capabilities: {
      imageGeneration: isConfigured,
      styleVariations: isConfigured,
      customSizes: isConfigured
    },
    message: isConfigured
      ? 'Gemini image generation is available'
      : 'Gemini image generation requires API key configuration'
  };
};
