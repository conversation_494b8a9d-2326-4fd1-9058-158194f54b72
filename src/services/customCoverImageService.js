/**
 * Custom Cover Image Service
 * Handles upload, storage, and management of user-uploaded cover images
 */

import { supabase } from '../lib/supabase.js';
import errorMonitor, { ErrorSeverity } from '../utils/errorMonitor.js';

// Configuration for custom cover images
const COVER_IMAGE_CONFIG = {
  // Storage bucket for custom cover images
  STORAGE_BUCKET: 'custom-cover-images',
  
  // File size limits (in bytes)
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  
  // Supported image formats
  SUPPORTED_FORMATS: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  
  // Image optimization settings
  OPTIMIZATION: {
    maxWidth: 1200,
    maxHeight: 1600,
    quality: 0.9,
    format: 'png'
  },
  
  // File naming convention
  FILE_PREFIX: 'cover-image-'
};

/**
 * Validate uploaded image file
 * @param {File} file - The uploaded file
 * @returns {Object} Validation result with success flag and error message
 */
export const validateCoverImage = (file) => {
  try {
    // Check if file exists
    if (!file) {
      return { success: false, error: 'No file provided' };
    }

    // Check file size
    if (file.size > COVER_IMAGE_CONFIG.MAX_FILE_SIZE) {
      const maxSizeMB = COVER_IMAGE_CONFIG.MAX_FILE_SIZE / (1024 * 1024);
      return { 
        success: false, 
        error: `File size exceeds ${maxSizeMB}MB limit. Current size: ${(file.size / (1024 * 1024)).toFixed(2)}MB` 
      };
    }

    // Check file type
    if (!COVER_IMAGE_CONFIG.SUPPORTED_FORMATS.includes(file.type)) {
      return { 
        success: false, 
        error: `Unsupported file format. Supported formats: ${COVER_IMAGE_CONFIG.SUPPORTED_FORMATS.join(', ')}` 
      };
    }

    return { success: true };

  } catch (error) {
    console.error('❌ Error validating cover image:', error);
    return { success: false, error: 'Failed to validate image file' };
  }
};

/**
 * Optimize image for cover use
 * @param {File} file - The original image file
 * @returns {Promise<Blob>} Optimized image blob
 */
export const optimizeCoverImage = async (file) => {
  return new Promise((resolve, reject) => {
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        try {
          const { maxWidth, maxHeight, quality } = COVER_IMAGE_CONFIG.OPTIMIZATION;
          
          // Calculate optimal dimensions while maintaining aspect ratio
          let { width, height } = img;
          
          if (width > maxWidth || height > maxHeight) {
            const aspectRatio = width / height;
            
            if (width > height) {
              width = maxWidth;
              height = width / aspectRatio;
            } else {
              height = maxHeight;
              width = height * aspectRatio;
            }
          }

          // Set canvas dimensions
          canvas.width = width;
          canvas.height = height;

          // Draw and optimize image
          ctx.drawImage(img, 0, 0, width, height);

          // Convert to blob
          canvas.toBlob(
            (blob) => {
              if (blob) {
                resolve(blob);
              } else {
                reject(new Error('Failed to optimize image'));
              }
            },
            `image/${COVER_IMAGE_CONFIG.OPTIMIZATION.format}`,
            quality
          );

        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => {
        reject(new Error('Failed to load image for optimization'));
      };

      // Load image
      img.src = URL.createObjectURL(file);

    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Upload custom cover image to Supabase storage
 * @param {File} file - The image file to upload
 * @param {string} documentId - Document ID for file naming
 * @param {string} userId - User ID for access control
 * @returns {Promise<Object>} Upload result with image URL and metadata
 */
export const uploadCustomCoverImage = async (file, documentId, userId) => {
  try {
    console.log('🔄 Starting custom cover image upload', {
      fileName: file.name,
      fileSize: file.size,
      documentId,
      userId
    });

    // Validate the image
    const validation = validateCoverImage(file);
    if (!validation.success) {
      throw new Error(validation.error);
    }

    // Optimize the image
    const optimizedBlob = await optimizeCoverImage(file);
    
    // Generate unique filename
    const timestamp = Date.now();
    const fileExtension = COVER_IMAGE_CONFIG.OPTIMIZATION.format;
    const fileName = `${COVER_IMAGE_CONFIG.FILE_PREFIX}${documentId}-${timestamp}.${fileExtension}`;
    const filePath = `${userId}/${fileName}`;

    // Upload to Supabase storage
    const { data, error } = await supabase.storage
      .from(COVER_IMAGE_CONFIG.STORAGE_BUCKET)
      .upload(filePath, optimizedBlob, {
        contentType: `image/${fileExtension}`,
        upsert: true,
        cacheControl: '3600'
      });

    if (error) {
      throw error;
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from(COVER_IMAGE_CONFIG.STORAGE_BUCKET)
      .getPublicUrl(filePath);

    // Get image dimensions
    const dimensions = await getImageDimensions(optimizedBlob);

    const result = {
      success: true,
      imageUrl: publicUrl,
      metadata: {
        originalName: file.name,
        size: optimizedBlob.size,
        dimensions,
        uploadedAt: new Date().toISOString(),
        filePath,
        fileName
      }
    };

    console.log('✅ Custom cover image uploaded successfully', result);
    return result;

  } catch (error) {
    console.error('❌ Error uploading custom cover image:', error);
    
    errorMonitor.captureError(
      error,
      {
        documentId,
        userId,
        fileName: file?.name,
        fileSize: file?.size
      },
      ErrorSeverity.MEDIUM
    );

    return {
      success: false,
      error: error.message || 'Failed to upload custom cover image'
    };
  }
};

/**
 * Get image dimensions from blob
 * @param {Blob} blob - Image blob
 * @returns {Promise<Object>} Image dimensions
 */
const getImageDimensions = (blob) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.width, height: img.height });
    };
    img.onerror = () => {
      resolve({ width: 1200, height: 1600 }); // Default dimensions
    };
    img.src = URL.createObjectURL(blob);
  });
};

/**
 * Delete custom cover image from storage
 * @param {string} filePath - Path to the file in storage
 * @returns {Promise<Object>} Deletion result
 */
export const deleteCustomCoverImage = async (filePath) => {
  try {
    const { error } = await supabase.storage
      .from(COVER_IMAGE_CONFIG.STORAGE_BUCKET)
      .remove([filePath]);

    if (error) {
      throw error;
    }

    console.log('✅ Custom cover image deleted successfully:', filePath);
    return { success: true };

  } catch (error) {
    console.error('❌ Error deleting custom cover image:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Create storage bucket for custom cover images if it doesn't exist
 * @returns {Promise<Object>} Creation result
 */
export const ensureStorageBucket = async () => {
  try {
    // Check if bucket exists
    const { data: buckets } = await supabase.storage.listBuckets();
    const bucketExists = buckets?.some(bucket => bucket.id === COVER_IMAGE_CONFIG.STORAGE_BUCKET);

    if (!bucketExists) {
      // Create bucket
      const { error } = await supabase.storage.createBucket(COVER_IMAGE_CONFIG.STORAGE_BUCKET, {
        public: true,
        allowedMimeTypes: COVER_IMAGE_CONFIG.SUPPORTED_FORMATS,
        fileSizeLimit: COVER_IMAGE_CONFIG.MAX_FILE_SIZE
      });

      if (error) {
        throw error;
      }

      console.log('✅ Custom cover images storage bucket created');
    }

    return { success: true };

  } catch (error) {
    console.error('❌ Error ensuring storage bucket:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Retry mechanism for failed operations
 * @param {Function} operation - The operation to retry
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} delay - Delay between retries in milliseconds
 * @returns {Promise<any>} Operation result
 */
const retryOperation = async (operation, maxRetries = 3, delay = 1000) => {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      console.warn(`❌ Attempt ${attempt}/${maxRetries} failed:`, error.message);

      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
  }

  throw lastError;
};

/**
 * Check if custom cover image is accessible
 * @param {string} imageUrl - URL to check
 * @returns {Promise<boolean>} Whether the image is accessible
 */
export const validateImageAccessibility = async (imageUrl) => {
  try {
    const response = await fetch(imageUrl, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error('❌ Image accessibility check failed:', error);
    return false;
  }
};

/**
 * Get fallback cover options when custom cover fails
 * @param {Object} documentData - Document metadata
 * @returns {Object} Fallback options
 */
export const getFallbackCoverOptions = (documentData) => {
  return {
    useSimpleTextCover: true,
    suggestedTemplates: [
      'plain-template', // Always available fallback
    ],
    fallbackImageUrl: null, // Could be a default cover image
    fallbackMessage: 'Using default cover due to image loading issues'
  };
};

/**
 * Enhanced upload with retry and validation
 * @param {File} file - Image file to upload
 * @param {string} documentId - Document ID
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Upload result with enhanced error handling
 */
export const uploadCustomCoverImageWithRetry = async (file, documentId, userId) => {
  try {
    return await retryOperation(
      () => uploadCustomCoverImage(file, documentId, userId),
      3, // 3 retries
      2000 // 2 second delay
    );
  } catch (error) {
    console.error('❌ All upload attempts failed:', error);

    return {
      success: false,
      error: 'Failed to upload cover image after multiple attempts. Please try again later.',
      fallbackOptions: getFallbackCoverOptions({ id: documentId }),
      retryable: true
    };
  }
};

export default {
  validateCoverImage,
  optimizeCoverImage,
  uploadCustomCoverImage,
  uploadCustomCoverImageWithRetry,
  deleteCustomCoverImage,
  ensureStorageBucket,
  validateImageAccessibility,
  getFallbackCoverOptions,
  retryOperation,
  COVER_IMAGE_CONFIG
};
