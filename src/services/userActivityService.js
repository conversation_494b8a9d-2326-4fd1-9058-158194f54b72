import { supabase, dbHelpers } from '../lib/supabase';

/**
 * Service for tracking user activities and updating usage statistics
 */
export const userActivityService = {
  /**
   * Track document creation
   */
  trackDocumentCreation: async (userId) => {
    try {
      const { error } = await supabase.rpc('increment_usage_counter', {
        user_id: userId,
        counter_type: 'documents_created'
      });
      
      if (error) {
        console.error('Failed to track document creation:', error);
      }
    } catch (err) {
      console.error('Error tracking document creation:', err);
    }
  },

  /**
   * Track AI generation usage
   */
  trackAIGeneration: async (userId) => {
    try {
      const { error } = await supabase.rpc('increment_usage_counter', {
        user_id: userId,
        counter_type: 'ai_generations_used'
      });
      
      if (error) {
        console.error('Failed to track AI generation:', error);
      }
    } catch (err) {
      console.error('Error tracking AI generation:', err);
    }
  },

  /**
   * Update storage usage
   */
  updateStorageUsage: async (userId, sizeInMB) => {
    try {
      const { error } = await dbHelpers.updateUserUsageStats(userId, {
        storage_used_mb: sizeInMB
      });
      
      if (error) {
        console.error('Failed to update storage usage:', error);
      }
    } catch (err) {
      console.error('Error updating storage usage:', err);
    }
  },

  /**
   * Get user activity history (mock for now, can be implemented with real activity table)
   */
  getUserActivityHistory: async (userId, limit = 10) => {
    try {
      // For now, return mock data based on user profile
      const { data: profile } = await dbHelpers.getUserProfile(userId);
      
      if (!profile) return [];

      const activities = [];
      
      if (profile.documents_created > 0) {
        activities.push({
          id: 1,
          type: "document_created",
          description: `Created ${profile.documents_created} documents`,
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // 1 day ago
        });
      }
      
      if (profile.ai_generations_used > 0) {
        activities.push({
          id: 2,
          type: "ai_generation",
          description: `Used AI generation ${profile.ai_generations_used} times`,
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() // 2 days ago
        });
      }
      
      if (profile.storage_used_mb > 0) {
        activities.push({
          id: 3,
          type: "storage_usage",
          description: `Using ${profile.storage_used_mb} MB of storage`,
          timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString() // 3 days ago
        });
      }

      return activities.slice(0, limit);
    } catch (err) {
      console.error('Error getting user activity history:', err);
      return [];
    }
  },

  /**
   * Get user usage statistics
   */
  getUserUsageStats: async (userId) => {
    try {
      const { data: profile, error } = await dbHelpers.getUserProfile(userId);
      
      if (error || !profile) {
        return {
          documentsCreated: 0,
          documentsLimit: 10,
          aiGenerationsUsed: 0,
          aiGenerationsLimit: 50,
          storageUsedMB: 0,
          storageLimitMB: 100
        };
      }

      return {
        documentsCreated: profile.documents_created || 0,
        documentsLimit: profile.documents_limit || 10,
        aiGenerationsUsed: profile.ai_generations_used || 0,
        aiGenerationsLimit: profile.ai_generations_limit || 50,
        storageUsedMB: profile.storage_used_mb || 0,
        storageLimitMB: profile.storage_limit_mb || 100
      };
    } catch (err) {
      console.error('Error getting user usage stats:', err);
      return {
        documentsCreated: 0,
        documentsLimit: 10,
        aiGenerationsUsed: 0,
        aiGenerationsLimit: 50,
        storageUsedMB: 0,
        storageLimitMB: 100
      };
    }
  }
};

export default userActivityService;
