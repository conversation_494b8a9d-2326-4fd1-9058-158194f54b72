/**
 * Test script for pagination functionality
 * Run this script to validate the pagination fix
 */

// Mock DOM environment for Node.js testing
const { JSDOM } = require('jsdom');
const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
global.document = dom.window.document;
global.window = dom.window;

// Import the pagination function and test utilities
import { paginateContentForPreview } from './src/services/previewService.js';
import { runPaginationTests, generateTestContent, testWithRealContent } from './src/utils/paginationTestUtils.js';

/**
 * Main test function
 */
async function testPaginationFix() {
  console.log('🚀 Testing Pagination Fix');
  console.log('==========================\n');

  // Test 1: Basic pagination functionality
  console.log('Test 1: Basic Pagination Functionality');
  const basicResults = runPaginationTests(paginateContentForPreview);
  
  // Test 2: Height-based pagination
  console.log('\nTest 2: Height-based Pagination');
  const heightBasedOptions = {
    useHeightBasedPagination: true,
    maxPageHeight: 950
  };
  
  const testContent = generateTestContent('long');
  try {
    const heightBasedPages = paginateContentForPreview(testContent, heightBasedOptions);
    console.log(`✅ Height-based pagination: ${heightBasedPages.length} pages`);
  } catch (error) {
    console.log(`❌ Height-based pagination failed: ${error.message}`);
  }

  // Test 3: Word-based fallback
  console.log('\nTest 3: Word-based Fallback');
  const wordBasedOptions = {
    useHeightBasedPagination: false,
    wordsPerPage: 300
  };
  
  try {
    const wordBasedPages = paginateContentForPreview(testContent, wordBasedOptions);
    console.log(`✅ Word-based pagination: ${wordBasedPages.length} pages`);
  } catch (error) {
    console.log(`❌ Word-based pagination failed: ${error.message}`);
  }

  // Test 4: Page break handling
  console.log('\nTest 4: Page Break Handling');
  const pageBreakContent = `
    <h1>Chapter 1</h1>
    <p>This is the first chapter content.</p>
    <div class="page-break"></div>
    <h1>Chapter 2</h1>
    <p>This is the second chapter content.</p>
  `;
  
  try {
    const pageBreakPages = paginateContentForPreview(pageBreakContent);
    console.log(`✅ Page break handling: ${pageBreakPages.length} pages`);
  } catch (error) {
    console.log(`❌ Page break handling failed: ${error.message}`);
  }

  // Test 5: Empty content handling
  console.log('\nTest 5: Empty Content Handling');
  try {
    const emptyPages = paginateContentForPreview('');
    console.log(`✅ Empty content handling: ${emptyPages.length} pages`);
    console.log(`   Content: "${emptyPages[0]}"`);
  } catch (error) {
    console.log(`❌ Empty content handling failed: ${error.message}`);
  }

  // Test 6: Large content stress test
  console.log('\nTest 6: Large Content Stress Test');
  const largeContent = Array(100).fill('<p>This is a paragraph with substantial content that should be properly paginated across multiple pages without causing performance issues or memory problems.</p>').join('');
  
  try {
    const start = Date.now();
    const largePages = paginateContentForPreview(largeContent, { useHeightBasedPagination: true });
    const duration = Date.now() - start;
    console.log(`✅ Large content test: ${largePages.length} pages in ${duration}ms`);
  } catch (error) {
    console.log(`❌ Large content test failed: ${error.message}`);
  }

  console.log('\n🎉 Pagination testing complete!');
  console.log('==========================');
}

// Run the tests
testPaginationFix().catch(console.error);
