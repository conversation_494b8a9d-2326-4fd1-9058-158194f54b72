/**
 * Test script to verify heading styles in exported documents
 * This script tests that headings appear with black text and no blue underlines
 */

import fs from 'fs';
import path from 'path';

/**
 * Test HTML export styling by checking the source file
 */
function testHtmlExport() {
  try {
    console.log("🧪 Testing HTML export heading styles...");

    // Read the export service file
    const exportServicePath = path.join(process.cwd(), 'src', 'services', 'exportService.js');
    const exportServiceContent = fs.readFileSync(exportServicePath, 'utf8');

    // Check for blue underlines (should not exist)
    const hasBlueBorder = exportServiceContent.includes('border-bottom: 2px solid #3498db');
    const hasBlueUnderline = exportServiceContent.includes('border-bottom') && exportServiceContent.includes('#3498db');

    // Check for black text color
    const hasBlackColor = exportServiceContent.includes('color: #000000');

    // Check for old blue-gray colors (should not exist)
    const hasOldColors = exportServiceContent.includes('color: #2c3e50');

    console.log("📊 HTML Export Test Results:");
    console.log(`  ❌ Blue underlines found: ${hasBlueBorder || hasBlueUnderline}`);
    console.log(`  ❌ Old blue-gray colors found: ${hasOldColors}`);
    console.log(`  ✅ Black text color found: ${hasBlackColor}`);

    if (!hasBlueBorder && !hasBlueUnderline && hasBlackColor && !hasOldColors) {
      console.log("✅ HTML export heading styles are correct!");
      return true;
    } else {
      console.log("❌ HTML export heading styles need fixing!");
      return false;
    }

  } catch (error) {
    console.error("❌ HTML export test failed:", error.message);
    return false;
  }
}

/**
 * Test DOCX export styling by checking the source file
 */
function testDocxExport() {
  try {
    console.log("🧪 Testing DOCX export heading styles...");

    // Read the DOCX generation service file
    const docxServicePath = path.join(process.cwd(), 'src', 'services', 'docxGenerationService.js');
    const docxServiceContent = fs.readFileSync(docxServicePath, 'utf8');

    // Check for black color in heading styles
    const hasBlackHeading1 = docxServiceContent.includes('color: "000000"');

    // Check for old colors (should not exist)
    const hasOldColors = docxServiceContent.includes('color: "2c3e50"') || docxServiceContent.includes('color: "34495e"');

    console.log("📊 DOCX Export Test Results:");
    console.log(`  ✅ Black color found in headings: ${hasBlackHeading1}`);
    console.log(`  ❌ Old gray colors found: ${hasOldColors}`);

    if (hasBlackHeading1 && !hasOldColors) {
      console.log("✅ DOCX export heading styles are correct!");
      return true;
    } else {
      console.log("❌ DOCX export heading styles need fixing!");
      return false;
    }

  } catch (error) {
    console.error("❌ DOCX export test failed:", error.message);
    return false;
  }
}

/**
 * Test TipTap editor styles by checking the source file
 */
function testTipTapStyles() {
  try {
    console.log("🧪 Testing TipTap editor heading styles...");

    // Read the enhanced content extraction service file
    const tipTapServicePath = path.join(process.cwd(), 'src', 'services', 'enhancedContentExtraction.js');
    const tipTapServiceContent = fs.readFileSync(tipTapServicePath, 'utf8');

    // Check for black color in headings
    const hasBlackColor = tipTapServiceContent.includes('color: #000000');

    // Check for absence of old gray colors
    const hasOldGrayColors = tipTapServiceContent.includes('#111827') || tipTapServiceContent.includes('#2c3e50');

    console.log("📊 TipTap Styles Test Results:");
    console.log(`  ✅ Black color found in headings: ${hasBlackColor}`);
    console.log(`  ❌ Old gray colors found: ${hasOldGrayColors}`);

    if (hasBlackColor && !hasOldGrayColors) {
      console.log("✅ TipTap heading styles are correct!");
      return true;
    } else {
      console.log("❌ TipTap heading styles need fixing!");
      return false;
    }

  } catch (error) {
    console.error("❌ TipTap styles test failed:", error.message);
    return false;
  }
}

/**
 * Run all tests
 */
function runAllTests() {
  console.log("🚀 Starting heading styles tests...\n");

  const htmlTest = testHtmlExport();
  console.log("");

  const docxTest = testDocxExport();
  console.log("");

  const tipTapTest = testTipTapStyles();
  console.log("");

  const allTestsPassed = htmlTest && docxTest && tipTapTest;

  console.log("📋 Final Test Summary:");
  console.log(`  HTML Export: ${htmlTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`  DOCX Export: ${docxTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`  TipTap Styles: ${tipTapTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log("");

  if (allTestsPassed) {
    console.log("🎉 All heading style tests passed! Headings should now appear with black text and no blue underlines.");
  } else {
    console.log("⚠️  Some tests failed. Please review the results above.");
  }

  return allTestsPassed;
}

// Run tests
runAllTests();
