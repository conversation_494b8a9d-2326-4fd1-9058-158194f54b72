{"name": "RapidDoc_ai", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@google/generative-ai": "^0.24.1", "@reduxjs/toolkit": "^2.6.1", "@supabase/supabase-js": "^2.50.2", "@tailwindcss/forms": "^0.5.7", "@testing-library/jest-dom": "^5.15.1", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "@tiptap/core": "^2.25.0", "@tiptap/extension-bubble-menu": "^2.25.0", "@tiptap/extension-character-count": "^2.25.0", "@tiptap/extension-floating-menu": "^2.25.0", "@tiptap/extension-image": "^2.25.0", "@tiptap/extension-placeholder": "^2.25.0", "@tiptap/html": "^2.26.1", "@tiptap/react": "^2.25.0", "@tiptap/starter-kit": "^2.25.0", "axios": "^1.8.4", "d3": "^7.9.0", "date-fns": "^4.1.0", "docx": "^9.5.1", "dompurify": "^3.2.6", "dotenv": "^16.6.1", "framer-motion": "^10.16.4", "image-size": "^2.0.2", "lucide-react": "^0.484.0", "mammoth": "^1.9.1", "pdfjs-dist": "^5.3.93", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.55.0", "react-router-dom": "6.0.2", "react-router-hash-link": "^2.4.3", "recharts": "^2.15.2", "redux": "^5.0.1", "rehype-parse": "^9.0.1", "rehype-remark": "^10.0.1", "rehype-stringify": "^10.0.1", "remark-gfm": "^4.0.1", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "remark-stringify": "^11.0.0", "tailwindcss-animate": "^1.0.7", "tailwindcss-elevation": "^2.0.0", "tailwindcss-fluid-type": "^2.0.7", "unified": "^11.0.5", "unsplash-js": "^7.0.19"}, "scripts": {"dev": "vite", "start": "vite", "build": "vite build --sourcemap", "serve": "vite preview", "test": "jest", "test:watch": "jest --watch", "db:setup": "node scripts/setup-database.js", "db:test": "node scripts/test-connection.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/line-clamp": "^0.1.0", "@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-react": "4.3.4", "autoprefixer": "10.4.2", "babel-jest": "^30.0.2", "eslint": "^9.31.0", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "jsdom": "^26.1.0", "postcss": "8.4.8", "tailwindcss": "3.4.6", "vite": "5.0.0", "vite-tsconfig-paths": "3.6.0"}}