/**
 * Test script to validate the critical pagination fix
 * This tests the data structure compatibility between old and new content extraction systems
 */

// Mock DOM environment for Node.js testing
const { JSDOM } = require('jsdom');
const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
global.document = dom.window.document;
global.window = dom.window;

// Mock the pagination function (simplified version for testing)
function mockPaginateContentForPreview(html, options = {}) {
  const { wordsPerPage = 500 } = options;
  
  if (!html || html.trim() === '') {
    return ['<div class="empty-content">No content to display</div>'];
  }

  // Simple word-based pagination for testing
  const words = html.split(/\s+/);
  const pages = [];
  
  for (let i = 0; i < words.length; i += wordsPerPage) {
    const pageWords = words.slice(i, i + wordsPerPage);
    pages.push(pageWords.join(' '));
  }
  
  return pages.length > 0 ? pages : [html];
}

// Mock content extraction functions
function mockExtractContentFromEditor(editorContent) {
  // Old format - returns html as string
  return {
    html: '<h1>Test Document</h1><p>This is test content from the old extraction system.</p>',
    text: 'Test Document This is test content from the old extraction system.',
    metadata: {
      wordCount: 12,
      characterCount: 65
    }
  };
}

function mockExtractEnhancedContent(editorContent) {
  // New format - returns html as object
  return {
    html: {
      raw: '<h1>Test Document</h1><p>This is test content from the new enhanced extraction system with more detailed processing.</p>',
      cleaned: '<h1>Test Document</h1><p>This is test content from the new enhanced extraction system with more detailed processing.</p>',
      preview: '<h1>Test Document</h1><p>This is test content from the new enhanced extraction system with more detailed processing.</p>'
    },
    text: 'Test Document This is test content from the new enhanced extraction system with more detailed processing.',
    metadata: {
      wordCount: 18,
      characterCount: 95,
      estimatedReadTime: 1,
      paragraphCount: 1,
      headingCount: 1
    }
  };
}

// Test the critical fix logic
function testContentExtractionCompatibility() {
  console.log('🧪 Testing Content Extraction Compatibility');
  console.log('===========================================\n');

  // Test 1: Old format (html as string)
  console.log('Test 1: Old Format Compatibility');
  const oldFormatContent = mockExtractContentFromEditor();
  
  let htmlContent = '';
  if (typeof oldFormatContent.html === 'string') {
    htmlContent = oldFormatContent.html;
    console.log('✅ Old format detected and handled correctly');
  } else {
    console.log('❌ Old format not detected properly');
  }
  
  const oldFormatPages = mockPaginateContentForPreview(htmlContent);
  console.log(`   Pages generated: ${oldFormatPages.length}`);
  console.log(`   First page preview: "${oldFormatPages[0].substring(0, 50)}..."`);
  console.log('');

  // Test 2: New format (html as object)
  console.log('Test 2: New Format Compatibility');
  const newFormatContent = mockExtractEnhancedContent();
  
  htmlContent = '';
  if (typeof newFormatContent.html === 'string') {
    htmlContent = newFormatContent.html;
    console.log('   Using old format logic');
  } else if (newFormatContent.html && typeof newFormatContent.html === 'object') {
    htmlContent = newFormatContent.html.preview || newFormatContent.html.raw || newFormatContent.html.cleaned || '';
    console.log('✅ New format detected and handled correctly');
  } else {
    htmlContent = '';
    console.log('❌ No valid HTML content found');
  }
  
  const newFormatPages = mockPaginateContentForPreview(htmlContent);
  console.log(`   Pages generated: ${newFormatPages.length}`);
  console.log(`   First page preview: "${newFormatPages[0].substring(0, 50)}..."`);
  console.log('');

  // Test 3: Empty/Invalid content
  console.log('Test 3: Empty/Invalid Content Handling');
  const invalidContent = { html: null };
  
  htmlContent = '';
  if (typeof invalidContent.html === 'string') {
    htmlContent = invalidContent.html;
  } else if (invalidContent.html && typeof invalidContent.html === 'object') {
    htmlContent = invalidContent.html.preview || invalidContent.html.raw || invalidContent.html.cleaned || '';
  } else {
    htmlContent = '';
    console.log('✅ Invalid content detected, using fallback');
  }
  
  const invalidPages = mockPaginateContentForPreview(htmlContent);
  console.log(`   Pages generated: ${invalidPages.length}`);
  console.log(`   Fallback content: "${invalidPages[0]}"`);
  console.log('');

  // Test 4: Large content pagination
  console.log('Test 4: Large Content Pagination');
  const largeContent = {
    html: {
      raw: Array(1000).fill('<p>This is a paragraph with substantial content for testing pagination.</p>').join(''),
      cleaned: Array(1000).fill('<p>This is a paragraph with substantial content for testing pagination.</p>').join(''),
      preview: Array(1000).fill('<p>This is a paragraph with substantial content for testing pagination.</p>').join('')
    }
  };
  
  htmlContent = '';
  if (typeof largeContent.html === 'string') {
    htmlContent = largeContent.html;
  } else if (largeContent.html && typeof largeContent.html === 'object') {
    htmlContent = largeContent.html.preview || largeContent.html.raw || largeContent.html.cleaned || '';
    console.log('✅ Large content processed successfully');
  }
  
  const largePages = mockPaginateContentForPreview(htmlContent, { wordsPerPage: 100 });
  console.log(`   Pages generated: ${largePages.length}`);
  console.log(`   Average page length: ${Math.round(largePages.reduce((sum, page) => sum + page.length, 0) / largePages.length)} characters`);
  console.log('');

  // Summary
  console.log('📊 Test Summary');
  console.log('===============');
  console.log(`✅ Old format compatibility: ${oldFormatPages.length > 0 ? 'PASS' : 'FAIL'}`);
  console.log(`✅ New format compatibility: ${newFormatPages.length > 0 ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Invalid content handling: ${invalidPages.length === 1 && invalidPages[0].includes('empty-content') ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Large content pagination: ${largePages.length > 1 ? 'PASS' : 'FAIL'}`);
  
  const allTestsPassed = oldFormatPages.length > 0 && 
                        newFormatPages.length > 0 && 
                        invalidPages.length === 1 && 
                        largePages.length > 1;
  
  console.log(`\n🎉 Overall Result: ${allTestsPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
  
  return allTestsPassed;
}

// Run the tests
try {
  const success = testContentExtractionCompatibility();
  process.exit(success ? 0 : 1);
} catch (error) {
  console.error('❌ Test execution failed:', error.message);
  process.exit(1);
}
