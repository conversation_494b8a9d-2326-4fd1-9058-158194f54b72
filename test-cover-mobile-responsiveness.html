<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cover Preview Mobile Responsiveness Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Simulate the cover preview mobile CSS */
        .cover-preview-container {
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            flex: 1;
            min-height: 0;
        }

        .cover-preview-page {
            max-width: 100vw;
            overflow: visible !important;
            margin-left: auto;
            margin-right: auto;
        }

        .cover-page-scaled {
            overflow: hidden !important;
        }

        /* Mobile breakpoints */
        @media (max-width: 480px) {
            .cover-preview-container {
                padding: 8px !important;
            }
            .cover-preview-page {
                max-width: calc(100vw - 16px);
            }
        }

        @media (min-width: 481px) and (max-width: 640px) {
            .cover-preview-container {
                padding: 12px !important;
            }
            .cover-preview-page {
                max-width: calc(100vw - 24px);
            }
        }

        @media (max-width: 768px) {
            .cover-preview-container {
                -webkit-overflow-scrolling: touch;
                overflow-x: hidden;
                overflow-y: auto;
            }
            .cover-preview-page {
                overflow: visible !important;
                max-width: 100vw !important;
            }
            .cover-page-scaled {
                overflow: hidden !important;
            }
        }

        /* Mock cover image */
        .mock-cover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 40px;
            box-sizing: border-box;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex flex-col">
        <!-- Header - Testing Mobile Responsive Header -->
        <div class="bg-white border-b border-gray-200">
            <!-- Mobile-First Responsive Header (Simulating CoverPreviewHeader) -->
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between p-2 sm:p-4 gap-2 sm:gap-4">

                <!-- Top Row on Mobile / Left Section on Desktop -->
                <div class="flex items-center justify-between sm:justify-start">
                    <!-- Back Button -->
                    <button class="flex items-center space-x-1 sm:space-x-2 text-gray-600 hover:text-gray-800 transition-colors p-2 sm:p-0 -ml-2 sm:ml-0">
                        <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                        <span class="text-sm sm:text-base hidden xs:inline sm:inline">Back</span>
                        <span class="text-sm hidden sm:inline">to Templates</span>
                    </button>

                    <!-- Export Button - Mobile Position -->
                    <button class="sm:hidden bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <span>Export</span>
                    </button>
                </div>

                <!-- Template Selector - Full Width on Mobile -->
                <div class="flex items-center space-x-2 min-w-0 flex-1 sm:flex-initial">
                    <label class="text-xs sm:text-sm font-medium text-gray-700 whitespace-nowrap">Template:</label>
                    <select class="border border-gray-300 rounded px-2 sm:px-3 py-1 text-xs sm:text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-0 flex-1 sm:flex-initial sm:min-w-[200px] max-w-full">
                        <option>Business Professional Template</option>
                        <option>Creative Design Template</option>
                        <option>Academic Research Template</option>
                        <option>Modern Minimalist Template</option>
                    </select>
                </div>

                <!-- Desktop-Only Right Section -->
                <div class="hidden sm:flex items-center space-x-3">
                    <div class="hidden md:flex items-center space-x-2 text-sm text-gray-600">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span class="whitespace-nowrap">Cover preview</span>
                    </div>

                    <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2 whitespace-nowrap">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <span>Proceed to Export</span>
                    </button>
                </div>
            </div>

            <div id="viewport-info" class="text-sm text-gray-600 px-4 pb-2"></div>
        </div>

        <!-- Cover Preview Content -->
        <div class="flex-1 bg-gray-100 overflow-auto">
            <div class="cover-preview-container p-4 sm:p-6 md:p-8 flex items-center justify-center min-h-full">
                <div class="cover-preview-wrapper w-full flex flex-col items-center">
                    <!-- Responsive Cover Preview -->
                    <div id="cover-preview-page" class="cover-preview-page shadow-lg overflow-hidden">
                        <div id="cover-page-scaled" class="cover-page-scaled">
                            <div class="cover-content w-full h-full">
                                <div class="mock-cover w-full h-full">
                                    <h1 class="text-4xl font-bold mb-4">Sample Document Title</h1>
                                    <div class="text-xl mb-4">by Author Name</div>
                                    <p class="text-base opacity-80 max-w-md">This is a sample cover preview to test mobile responsiveness and viewport scaling.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cover Info -->
                    <div class="mt-4 sm:mt-6 text-center max-w-2xl">
                        <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-2">Cover Design Preview</h3>
                        <p class="text-xs sm:text-sm text-gray-600 mb-4">
                            Testing mobile responsiveness and viewport scaling.
                        </p>
                        <div id="debug-info" class="flex flex-wrap justify-center gap-2 text-xs text-gray-500">
                            <!-- Debug info will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Responsive scaling logic (simplified version of the React component)
        function updateCoverPreview() {
            const baseWidth = 8.5; // inches
            const baseHeight = 11; // inches
            const baseWidthPx = baseWidth * 96; // 816px
            const baseHeightPx = baseHeight * 96; // 1056px

            const viewportWidth = document.documentElement.clientWidth || window.innerWidth;
            const viewportHeight = document.documentElement.clientHeight || window.innerHeight;

            let deviceType, margin;
            if (viewportWidth <= 480) {
                deviceType = 'small-mobile';
                margin = 16;
            } else if (viewportWidth <= 640) {
                deviceType = 'large-mobile';
                margin = 24;
            } else if (viewportWidth <= 768) {
                deviceType = 'tablet-portrait';
                margin = 32;
            } else {
                deviceType = 'desktop';
                margin = 64;
            }

            const isMobile = viewportWidth < 768;
            const maxWidth = viewportWidth - margin;
            const maxHeight = viewportHeight - 200; // Account for header and info

            let containerWidth, containerHeight, pageScale;

            if (isMobile) {
                const mobileMinScale = 0.4;
                const widthScale = maxWidth / baseWidthPx;
                const heightScale = maxHeight / baseHeightPx;
                const calculatedScale = Math.min(widthScale, heightScale);
                pageScale = Math.max(mobileMinScale, calculatedScale);
                
                containerWidth = baseWidthPx * pageScale;
                containerHeight = baseHeightPx * pageScale;
            } else {
                const desktopScale = 0.75;
                const widthScale = maxWidth / baseWidthPx;
                pageScale = Math.min(desktopScale, widthScale);
                containerWidth = baseWidthPx * pageScale;
                containerHeight = baseHeightPx * pageScale;
            }

            // Apply styles
            const coverPage = document.getElementById('cover-preview-page');
            const scaledPage = document.getElementById('cover-page-scaled');

            coverPage.style.width = `${containerWidth}px`;
            coverPage.style.height = `${containerHeight}px`;

            scaledPage.style.width = `${baseWidthPx}px`;
            scaledPage.style.height = `${baseHeightPx}px`;
            scaledPage.style.transform = `scale(${pageScale})`;
            scaledPage.style.transformOrigin = 'center top';
            scaledPage.style.position = 'absolute';
            scaledPage.style.top = '0';
            scaledPage.style.left = '50%';
            scaledPage.style.marginLeft = `-${baseWidthPx / 2}px`;

            // Check for horizontal overflow
            const body = document.body;
            const html = document.documentElement;
            const documentWidth = Math.max(body.scrollWidth, body.offsetWidth, html.clientWidth, html.scrollWidth, html.offsetWidth);
            const hasHorizontalOverflow = documentWidth > viewportWidth;

            // Update debug info
            document.getElementById('viewport-info').innerHTML = `
                Viewport: ${viewportWidth}×${viewportHeight} | Device: ${deviceType} | Mobile: ${isMobile}<br>
                Document Width: ${documentWidth}px | Overflow: ${hasHorizontalOverflow ? '❌ YES' : '✅ NO'}
            `;

            document.getElementById('debug-info').innerHTML = `
                <span>Scale: ${Math.round(pageScale * 100)}%</span>
                <span>•</span>
                <span>Container: ${Math.round(containerWidth)}×${Math.round(containerHeight)}px</span>
                <span>•</span>
                <span>Base: ${baseWidthPx}×${baseHeightPx}px</span>
                <span>•</span>
                <span>Available: ${maxWidth}×${maxHeight}px</span>
                ${hasHorizontalOverflow ? '<br><span style="color: red;">⚠️ Horizontal overflow detected!</span>' : '<br><span style="color: green;">✅ No overflow</span>'}
            `;

            console.log('Cover Preview Updated:', {
                deviceType,
                viewport: { width: viewportWidth, height: viewportHeight },
                available: { width: maxWidth, height: maxHeight },
                base: { width: baseWidthPx, height: baseHeightPx },
                calculated: { scale: pageScale, width: containerWidth, height: containerHeight },
                isMobile
            });
        }

        // Update on load and resize
        window.addEventListener('load', updateCoverPreview);
        window.addEventListener('resize', updateCoverPreview);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateCoverPreview, 100); // Delay for orientation change
        });

        // Initial update
        updateCoverPreview();
    </script>
</body>
</html>
