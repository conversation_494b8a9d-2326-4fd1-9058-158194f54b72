-- RapidDoc AI Database Schema
-- This file contains the database schema for user management and authentication

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret-here';

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    user_type TEXT CHECK (user_type IN ('student', 'educator', 'researcher', 'business', 'entrepreneur', 'content_creator')),
    bio TEXT,
    organization TEXT,
    location TEXT,
    website TEXT,
    phone TEXT,
    timezone TEXT DEFAULT 'UTC',
    language TEXT DEFAULT 'en',
    
    -- Subscription and billing
    subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'pro', 'enterprise')),
    subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'expired', 'trial')),
    subscription_expires_at TIMESTAMPTZ,
    trial_ends_at TIMESTAMPTZ,

    -- Billing address
    billing_address TEXT,
    billing_city TEXT,
    billing_state TEXT,
    billing_country TEXT,
    billing_postal_code TEXT,
    
    -- Usage tracking
    documents_created INTEGER DEFAULT 0,
    documents_limit INTEGER DEFAULT 10,
    ai_generations_used INTEGER DEFAULT 0,
    ai_generations_limit INTEGER DEFAULT 50,
    storage_used_mb INTEGER DEFAULT 0,
    storage_limit_mb INTEGER DEFAULT 100,
    
    -- Preferences (MVP - Essential only)
    theme TEXT DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
    notifications_email BOOLEAN DEFAULT true,
    auto_save BOOLEAN DEFAULT true,
    
    -- Security
    two_factor_enabled BOOLEAN DEFAULT false,
    last_login_at TIMESTAMPTZ,
    last_login_ip INET,
    login_count INTEGER DEFAULT 0,
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON public.user_profiles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create user_sessions table for session management
CREATE TABLE IF NOT EXISTS public.user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    session_token TEXT UNIQUE NOT NULL,
    device_info JSONB,
    ip_address INET,
    user_agent TEXT,
    location TEXT,
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_accessed_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create login_history table for security tracking
CREATE TABLE IF NOT EXISTS public.login_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    login_method TEXT DEFAULT 'email' CHECK (login_method IN ('email', 'google', 'github')),
    ip_address INET,
    user_agent TEXT,
    location TEXT,
    device_info JSONB,
    status TEXT DEFAULT 'success' CHECK (status IN ('success', 'failed', 'blocked')),
    failure_reason TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Row Level Security Policies
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.login_history ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see and edit their own profile
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Policy: Users can only see their own sessions
CREATE POLICY "Users can view own sessions" ON public.user_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own sessions" ON public.user_sessions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own sessions" ON public.user_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Users can only see their own login history
CREATE POLICY "Users can view own login history" ON public.login_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own login history" ON public.login_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_subscription ON public.user_profiles(subscription_tier, subscription_status);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON public.user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON public.user_sessions(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_login_history_user_id ON public.login_history(user_id);
CREATE INDEX IF NOT EXISTS idx_login_history_created_at ON public.login_history(created_at);

-- Function to create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, email, full_name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create profile on user signup
CREATE OR REPLACE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update login statistics
CREATE OR REPLACE FUNCTION public.update_login_stats(user_uuid UUID, ip_addr INET)
RETURNS VOID AS $$
BEGIN
    UPDATE public.user_profiles
    SET
        last_login_at = NOW(),
        last_login_ip = ip_addr,
        login_count = login_count + 1
    WHERE id = user_uuid;

    INSERT INTO public.login_history (user_id, ip_address, status)
    VALUES (user_uuid, ip_addr, 'success');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to increment usage counters
CREATE OR REPLACE FUNCTION public.increment_usage_counter(user_id UUID, counter_type TEXT)
RETURNS VOID AS $$
BEGIN
    CASE counter_type
        WHEN 'documents_created' THEN
            UPDATE public.user_profiles
            SET documents_created = documents_created + 1
            WHERE id = user_id;
        WHEN 'ai_generations_used' THEN
            UPDATE public.user_profiles
            SET ai_generations_used = ai_generations_used + 1
            WHERE id = user_id;
        WHEN 'storage_used_mb' THEN
            -- This would need a specific amount parameter
            NULL;
        ELSE
            RAISE EXCEPTION 'Invalid counter type: %', counter_type;
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user sessions
CREATE OR REPLACE FUNCTION public.get_user_sessions(user_uuid UUID)
RETURNS TABLE (
    id UUID,
    session_token TEXT,
    device_info JSONB,
    ip_address INET,
    user_agent TEXT,
    location TEXT,
    is_active BOOLEAN,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ,
    last_accessed_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        s.id,
        s.session_token,
        s.device_info,
        s.ip_address,
        s.user_agent,
        s.location,
        s.is_active,
        s.expires_at,
        s.created_at,
        s.last_accessed_at
    FROM public.user_sessions s
    WHERE s.user_id = user_uuid
    AND s.is_active = true
    ORDER BY s.last_accessed_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user login history
CREATE OR REPLACE FUNCTION public.get_user_login_history(user_uuid UUID, limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
    id UUID,
    login_method TEXT,
    ip_address INET,
    user_agent TEXT,
    location TEXT,
    device_info JSONB,
    status TEXT,
    failure_reason TEXT,
    created_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        lh.id,
        lh.login_method,
        lh.ip_address,
        lh.user_agent,
        lh.location,
        lh.device_info,
        lh.status,
        lh.failure_reason,
        lh.created_at
    FROM public.login_history lh
    WHERE lh.user_id = user_uuid
    ORDER BY lh.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
