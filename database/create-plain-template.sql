-- Create Plain Template for DocForge AI
-- This template replicates the original pre-template export format exactly
-- Provides users with the familiar, clean aesthetic they're accustomed to

-- First, let's create a simple white background image data URL
-- This is a 1x1 white pixel that will be stretched by the canvas rendering
-- More efficient than uploading a large white image

-- Insert the Plain template
INSERT INTO public.cover_templates (
    id,
    name,
    description,
    category,
    tags,
    background_image_url,
    background_image_width,
    background_image_height,
    text_overlays,
    supported_formats,
    is_premium,
    status,
    created_at,
    updated_at
) VALUES (
    'plain-template',
    'Plain',
    'Simple, clean document cover matching the original export format - perfect for professional documents that need a minimal, unadorned appearance',
    'minimal',
    ARRAY['plain', 'simple', 'minimal', 'default', 'original', 'clean'],
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
    1200,
    1600,
    '{
        "overlays": [
            {
                "id": "title",
                "type": "text",
                "placeholder": "{{title}}",
                "position": {
                    "x": 100,
                    "y": 400,
                    "width": 1000,
                    "height": 120
                },
                "styling": {
                    "fontSize": 60,
                    "fontFamily": "Georgia",
                    "fontWeight": "bold",
                    "color": "#2c3e50",
                    "textAlign": "center",
                    "lineHeight": 1.2,
                    "maxLines": 3,
                    "overflow": "ellipsis",
                    "verticalAlign": "center"
                }
            },
            {
                "id": "author",
                "type": "text",
                "placeholder": "by {{author}}",
                "position": {
                    "x": 100,
                    "y": 580,
                    "width": 1000,
                    "height": 60
                },
                "styling": {
                    "fontSize": 28,
                    "fontFamily": "Georgia",
                    "fontWeight": "normal",
                    "color": "#7f8c8d",
                    "textAlign": "center",
                    "lineHeight": 1.2,
                    "maxLines": 1,
                    "overflow": "ellipsis",
                    "verticalAlign": "center"
                }
            },
            {
                "id": "description",
                "type": "text",
                "placeholder": "{{description}}",
                "position": {
                    "x": 200,
                    "y": 680,
                    "width": 800,
                    "height": 200
                },
                "styling": {
                    "fontSize": 24,
                    "fontFamily": "Georgia",
                    "fontWeight": "normal",
                    "color": "#95a5a6",
                    "textAlign": "center",
                    "lineHeight": 1.6,
                    "maxLines": 6,
                    "overflow": "ellipsis",
                    "verticalAlign": "top",
                    "fontStyle": "italic"
                }
            }
        ]
    }'::jsonb,
    ARRAY['pdf', 'png', 'jpg'],
    false,
    'active',
    NOW(),
    NOW()
)
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    category = EXCLUDED.category,
    tags = EXCLUDED.tags,
    background_image_url = EXCLUDED.background_image_url,
    background_image_width = EXCLUDED.background_image_width,
    background_image_height = EXCLUDED.background_image_height,
    text_overlays = EXCLUDED.text_overlays,
    supported_formats = EXCLUDED.supported_formats,
    is_premium = EXCLUDED.is_premium,
    status = EXCLUDED.status,
    updated_at = NOW();

-- Verify the template was created
SELECT 
    id,
    name,
    category,
    array_length(tags, 1) as tag_count,
    background_image_width,
    background_image_height,
    jsonb_array_length(text_overlays->'overlays') as overlay_count,
    status,
    created_at
FROM public.cover_templates 
WHERE id = 'plain-template';

-- Show the text overlay configuration for verification
SELECT 
    id,
    name,
    jsonb_pretty(text_overlays) as text_overlay_config
FROM public.cover_templates 
WHERE id = 'plain-template';

-- Success message
SELECT 'Plain template created successfully! Users can now select this template to get the original export format.' as result;
