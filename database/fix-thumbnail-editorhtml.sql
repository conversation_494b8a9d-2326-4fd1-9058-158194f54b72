-- Fix for thumbnail extraction trigger to handle editorHTML content
-- This addresses the bug where thumbnails disappear after editing a project

-- Drop the existing function first to ensure clean recreation
DROP FUNCTION IF EXISTS extract_thumbnail_from_content() CASCADE;

-- Create the updated thumbnail extraction function to also check editorHTML
CREATE OR <PERSON><PERSON>LACE FUNCTION extract_thumbnail_from_content()
R<PERSON><PERSON>NS TRIGGER AS $$
DECLARE
  content_text TEXT;
  image_url TEXT;
BEGIN
  -- Only run if content has changed and is not null
  IF NEW.generated_content IS NOT NULL THEN
    
    -- FIRST: Try to extract from editorHTML (TipTap editor content)
    IF NEW.generated_content->>'editorHTML' IS NOT NULL THEN
      content_text := NEW.generated_content->>'editorHTML';
      
      -- Try HTML image format first (most likely in editor HTML)
      SELECT substring(content_text FROM '<img[^>]+src="([^"]+)"[^>]*>') INTO image_url;
      
      -- If no HTML image, try markdown image format
      IF image_url IS NULL THEN
        SELECT substring(content_text FROM '!\[.*?\]\((.*?)\)') INTO image_url;
      END IF;
    END IF;
    
    -- If no image found in editorHTML, try introduction
    IF image_url IS NULL AND NEW.generated_content->>'introduction' IS NOT NULL AND 
       NEW.generated_content->'introduction'->>'content' IS NOT NULL THEN
      content_text := NEW.generated_content->'introduction'->>'content';
      
      -- Try markdown image format
      SELECT substring(content_text FROM '!\[.*?\]\((.*?)\)') INTO image_url;
      
      -- If no markdown image, try HTML image format
      IF image_url IS NULL THEN
        SELECT substring(content_text FROM '<img[^>]+src="([^"]+)"[^>]*>') INTO image_url;
      END IF;
    END IF;
    
    -- If no image found in introduction, try chapters
    IF image_url IS NULL AND NEW.generated_content->>'chapters' IS NOT NULL THEN
      -- Loop through chapters (simplified, may need adaptation based on your JSON structure)
      FOR i IN 0..jsonb_array_length(NEW.generated_content->'chapters')-1 LOOP
        content_text := NEW.generated_content->'chapters'->i->>'content';
        IF content_text IS NOT NULL THEN
          -- Try markdown image format
          SELECT substring(content_text FROM '!\[.*?\]\((.*?)\)') INTO image_url;
          
          -- If no markdown image, try HTML image format
          IF image_url IS NULL THEN
            SELECT substring(content_text FROM '<img[^>]+src="([^"]+)"[^>]*>') INTO image_url;
          END IF;
          
          -- Break loop if image found
          IF image_url IS NOT NULL THEN
            EXIT;
          END IF;
        END IF;
      END LOOP;
    END IF;
    
    -- If no image found in chapters, try conclusion
    IF image_url IS NULL AND 
       NEW.generated_content->>'conclusion' IS NOT NULL AND 
       NEW.generated_content->'conclusion'->>'content' IS NOT NULL THEN
      content_text := NEW.generated_content->'conclusion'->>'content';
      
      -- Try markdown image format
      SELECT substring(content_text FROM '!\[.*?\]\((.*?)\)') INTO image_url;
      
      -- If no markdown image, try HTML image format
      IF image_url IS NULL THEN
        SELECT substring(content_text FROM '<img[^>]+src="([^"]+)"[^>]*>') INTO image_url;
      END IF;
    END IF;
  END IF;

  -- Update the extracted_thumbnail_url
  NEW.extracted_thumbnail_url := image_url;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger to ensure it uses the updated function
DROP TRIGGER IF EXISTS extract_thumbnail_trigger ON public.projects;
CREATE TRIGGER extract_thumbnail_trigger
BEFORE INSERT OR UPDATE OF generated_content ON public.projects
FOR EACH ROW EXECUTE FUNCTION extract_thumbnail_from_content();
