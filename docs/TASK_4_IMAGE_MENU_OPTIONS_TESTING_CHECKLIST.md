# Task 4: Image-Specific Floating Menu Options Testing Checklist

## Objective
Verify that the floating menu system properly detects image nodes and shows comprehensive image-specific options (edit alt text, replace, resize, delete) while maintaining the existing two-stage interaction pattern.

## Implementation Summary
- ✅ Enhanced image case in floating menu with comprehensive options
- ✅ Added `handleEditImageAltText()` with prompt-based alt text editing
- ✅ Added `handleReplaceImage()` with URL replacement functionality
- ✅ Added `handleResizeImage()` with 4 size presets (small, medium, large, full)
- ✅ Added visual separator before delete option
- ✅ Fixed typo in delete button className
- ✅ Maintained existing two-stage interaction pattern (ellipsis → expanded menu)

## Testing Checklist

### ✅ Image Node Detection
- [ ] Insert an image using URL input (🖼️ from plus menu)
- [ ] Click on the inserted image
- [ ] Verify floating menu appears with ellipsis button (⋯)
- [ ] Verify menu type is detected as 'ellipsis' (not 'plus')
- [ ] Check console logs for correct node type detection: `nodeType: 'image'`

### ✅ Image Menu Expansion
- [ ] Click ellipsis button on image
- [ ] Verify expanded menu appears with image-specific options
- [ ] Verify menu header shows "Edit content" with ellipsis icon
- [ ] Check all expected options are present:
  - ✏️ Edit alt text
  - 🔄 Replace image  
  - 📏 Resize to small
  - 📐 Resize to medium
  - 📊 Resize to large
  - ↔️ Full width
  - (separator line)
  - 🗑️ Delete image

### ✅ Edit Alt Text Functionality
- [ ] Click "Edit alt text" option
- [ ] Verify prompt dialog appears with current alt text
- [ ] Test with existing alt text: modify and confirm
- [ ] Test with empty alt text: add new text and confirm
- [ ] Test cancel: click Cancel in prompt, verify no changes
- [ ] Verify alt text is updated in image attributes
- [ ] Check console log: "🖼️ Updated image alt text: [text]"

### ✅ Replace Image Functionality
- [ ] Click "Replace image" option
- [ ] Verify prompt dialog appears with current image URL
- [ ] Test with valid new URL: replace and confirm
- [ ] Verify image changes to new URL
- [ ] Verify alt text is preserved from original image
- [ ] Test cancel: click Cancel in prompt, verify no changes
- [ ] Test empty URL: enter empty string, verify no changes
- [ ] Check console log: "🖼️ Replaced image with: [URL]"

### ✅ Resize Functionality - Small
- [ ] Click "Resize to small" option
- [ ] Verify image resizes to smaller size (~320px max width)
- [ ] Check image classes include `max-w-xs`
- [ ] Verify styling is maintained (rounded corners, shadow)
- [ ] Check console log: "🖼️ Resized image to small"

### ✅ Resize Functionality - Medium
- [ ] Click "Resize to medium" option
- [ ] Verify image resizes to medium size (~448px max width)
- [ ] Check image classes include `max-w-md`
- [ ] Verify styling is maintained
- [ ] Check console log: "🖼️ Resized image to medium"

### ✅ Resize Functionality - Large
- [ ] Click "Resize to large" option
- [ ] Verify image resizes to large size (~672px max width)
- [ ] Check image classes include `max-w-2xl`
- [ ] Verify styling is maintained
- [ ] Check console log: "🖼️ Resized image to large"

### ✅ Resize Functionality - Full Width
- [ ] Click "Full width" option
- [ ] Verify image expands to full container width
- [ ] Check image classes include `max-w-full`
- [ ] Verify styling is maintained
- [ ] Check console log: "🖼️ Resized image to full"

### ✅ Delete Image Functionality
- [ ] Click "Delete image" option (red text)
- [ ] Verify image is immediately removed from editor
- [ ] Verify cursor is positioned where image was
- [ ] Verify no confirmation dialog (immediate deletion)
- [ ] Test undo: Ctrl+Z should restore the image

### ✅ Menu Interaction Behavior
- [ ] Verify menu closes after each action (except cancel operations)
- [ ] Test clicking outside menu: should close without action
- [ ] Test clicking on different image: menu should move to new image
- [ ] Test clicking on text: menu should disappear
- [ ] Verify menu positioning works on different screen sizes

### ✅ Visual Separator
- [ ] Verify thin gray line appears before "Delete image" option
- [ ] Check separator styling: `border-t border-gray-100 my-1`
- [ ] Verify it provides clear visual separation from other options

### ✅ Responsive Behavior
- [ ] Test on desktop (>1024px): Full menu with all options
- [ ] Test on tablet (768-1024px): Menu should fit properly
- [ ] Test on mobile (<768px): Menu should be accessible and readable
- [ ] Verify menu doesn't overflow screen edges

## Expected Results

### ✅ Success Criteria
- Image nodes are properly detected and show ellipsis menu
- All 6 image-specific options are present and functional
- Alt text editing works with prompt dialog
- Image replacement preserves alt text and updates URL
- All 4 resize options work with correct CSS classes
- Delete functionality works immediately
- Menu closes after actions and maintains proper positioning
- Visual separator provides clear organization
- No console errors or warnings

### ❌ Failure Indicators
- Image nodes show plus menu instead of ellipsis menu
- Missing or non-functional menu options
- Prompt dialogs don't appear or don't update attributes
- Resize options don't change image size
- Delete doesn't remove image
- Menu doesn't close after actions
- Console errors related to image operations
- Menu positioning issues or overflow

## Rollback Instructions

If critical issues are found:

1. **Revert Image Case**:
   ```javascript
   case 'image':
     return (
       <>
         <button onClick={() => { console.log('🖼️ Image editing not yet implemented'); setIsMenuExpanded(false); }}>
           <span className="mr-3">✏️</span>
           <span className="text-gray-600">Edit image</span>
         </button>
         <button onClick={() => { editor.chain().focus().deleteSelection().run(); setIsMenuExpanded(false); }}>
           <span className="mr-3">🗑️</span>
           <span>Delete image</span>
         </button>
       </>
     );
   ```

2. **Remove Handler Functions**:
   - Remove `handleEditImageAltText`
   - Remove `handleReplaceImage` 
   - Remove `handleResizeImage`

## Next Steps

Upon successful completion:
- Mark Task 4 as COMPLETE
- Begin Task 5: Extend Content Conversion for Images
- Consider UX improvements:
  - Replace prompt dialogs with inline editing
  - Add image alignment options (left, center, right)
  - Add image caption functionality

## Notes

- Uses browser prompt() for simplicity - can be enhanced with custom modals
- Resize classes use Tailwind CSS responsive utilities
- Alt text and URL replacement preserve existing attributes
- Delete operation is immediate (no confirmation) following common editor patterns
- Visual separator improves menu organization and hierarchy
