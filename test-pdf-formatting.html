<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Formatting Test</title>
    <style>
        /* Test the PDF formatting improvements */
        .title-page {
            text-align: center;
            page-break-after: always;
            width: 100%;
            height: 100vh;
            margin: 0;
            padding: 0;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        .title {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #000000;
        }
        .author {
            font-size: 1.2em;
            color: #7f8c8d;
            margin-bottom: 20px;
        }
        .description {
            font-style: italic;
            color: #95a5a6;
            max-width: 600px;
            margin: 0 auto;
        }
        .chapter {
            margin-bottom: 40px;
            page-break-before: always;
        }
        .chapter-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #000000;
            padding-bottom: 10px;
            letter-spacing: -0.02em; /* Tighten letter spacing for better readability */
        }
        .chapter-content {
            text-align: justify;
        }
        h1, h2, h3 {
            color: #000000;
            margin-top: 30px;
            margin-bottom: 15px;
            letter-spacing: -0.02em; /* Tighten letter spacing for better readability */
        }
        p {
            margin-bottom: 15px;
        }
        @media print {
            /* FULL-BLEED: Different page setup for cover vs content pages */
            @page {
                size: A4;
                margin: 0; /* No margin for full-bleed cover */
            }

            /* FULL-BLEED: Separate page setup for content pages */
            @page content {
                size: A4;
                margin: 0.4in; /* Reduced margins for better space utilization */
            }

            html, body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                margin: 0 !important;
                padding: 0 !important;
                height: auto !important;
                background: white !important;
            }

            body {
                font-size: 14pt; /* Increased from 12pt for better readability */
                line-height: 1.5;
                max-width: none !important; /* Remove width constraint for full-width text content */
                margin: 0 !important; /* Remove auto margins that center content */
                padding: 0 !important; /* Remove padding - margins handled by @page rule */
            }

            /* Content pages use optimized margins for better space utilization */
            .chapter-content {
                page: content;
                margin: 0; /* Remove redundant margin - page margins are handled by @page content rule */
                width: 100%; /* Ensure full width utilization */
            }

            .chapter {
                page-break-before: always;
                width: 100%; /* Ensure chapters use full width */
            }

            /* Ensure all text elements use full available width */
            p, h1, h2, h3, ul, ol, div {
                width: 100%;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    <div class="title-page">
        <div>
            <h1 class="title">Test Document</h1>
            <div class="author">by Test Author</div>
            <div class="description">This is a test document to verify PDF formatting improvements</div>
        </div>
    </div>
    
    <div class="chapter-content">
        <div class="chapter">
            <h1 class="chapter-title">Chapter 1: Introduction</h1>
            <p>This is the first chapter of our test document. The text should now have better formatting with:</p>
            <ul>
                <li>Improved letter spacing in headings (reduced from default)</li>
                <li>Better content width utilization (reduced margins from 0.5in to 0.4in)</li>
                <li>Larger font size for better readability (increased from 12pt to 14pt)</li>
            </ul>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>

            <!-- Visual width test -->
            <div style="background: #f0f0f0; padding: 10px; margin: 20px 0; border: 1px solid #ccc;">
                <strong>Width Test:</strong> This gray box should extend to the full width of the content area, matching the width that images would use. If this text appears in a narrow column with excessive white space on the right, the width constraint issue is still present.
            </div>
        </div>
        
        <div class="chapter">
            <h1 class="chapter-title">Chapter 2: Content Testing</h1>
            <p>This chapter tests the improved formatting. Notice how the headings have better letter spacing and the content uses more of the available page width.</p>
            <h2>Subheading Example</h2>
            <p>Subheadings also benefit from the improved letter spacing. The overall font size is now 14pt instead of 12pt, making the text more readable.</p>
            <h3>Another Subheading</h3>
            <p>All heading levels (h1, h2, h3) now have consistent letter-spacing improvements and use black color (#000000) for better contrast.</p>
        </div>
    </div>
</body>
</html>
