# Project Structure & Organization

## Root Structure
```
RapidDoc_ai/
├── public/             # Static assets and manifest
├── src/               # Source code
├── database/          # SQL schemas and migrations
├── scripts/           # Database and utility scripts
├── docs/              # Project documentation
├── build/             # Production build output
└── node_modules/      # Dependencies
```

## Source Code Organization (`src/`)

### Core Application
- `App.jsx` - Main app component with providers
- `Routes.jsx` - Route definitions using `useRoutes`
- `index.jsx` - Application entry point

### Component Architecture
```
src/components/
├── ui/                # Reusable UI components (Button, Modal, Input, etc.)
├── auth/              # Authentication-specific components
├── projects/          # Project management components
└── debug/             # Development and debugging components
```

### Page Structure
```
src/pages/
├── dashboard/         # Main dashboard with components/ subfolder
├── document-creator/  # Multi-step document creation wizard
├── document-editor/   # Rich text editor with TipTap integration
├── plagiarism-checker/# Plagiarism detection interface
├── projects/          # Project management interface
├── account-settings/  # User profile and preferences
└── auth/              # Login/register pages
```

### Supporting Modules
```
src/
├── contexts/          # React Context providers (Auth, Sidebar)
├── hooks/             # Custom React hooks
├── services/          # API services and external integrations
├── utils/             # Utility functions and helpers
├── lib/               # Third-party library configurations
└── styles/            # Global styles and Tailwind config
```

## Naming Conventions

### Files & Folders
- **Components**: PascalCase (e.g., `DocumentEditor.jsx`)
- **Pages**: PascalCase with index files (e.g., `dashboard/index.jsx`)
- **Utilities**: camelCase (e.g., `sessionManager.js`)
- **Services**: camelCase with Service suffix (e.g., `aiService.js`)
- **Hooks**: camelCase with use prefix (e.g., `useProjects.js`)

### Component Organization
- Each major page has its own folder with `components/` subfolder
- Shared components go in `src/components/ui/`
- Feature-specific components stay within their page folder
- Test files use `__tests__/` folders or `.test.jsx` suffix

## Import Patterns
- Use absolute imports from `src/` root (configured in jsconfig.json)
- Group imports: React, third-party, local components, utilities
- Prefer named exports for utilities, default exports for components

## Database Structure
- `database/schema.sql` - Main database schema
- `database/*.sql` - Migration and setup scripts
- Row Level Security (RLS) enabled for data protection
- User profiles with subscription and usage tracking

## Configuration Files
- `vite.config.mjs` - Vite build configuration
- `tailwind.config.js` - Tailwind CSS customization
- `jest.config.js` - Testing configuration
- `jsconfig.json` - JavaScript project configuration
- `postcss.config.js` - PostCSS processing

## Key Architectural Patterns
- **Context + Hooks**: Auth and sidebar state management
- **Service Layer**: Separate API logic from components
- **Error Boundaries**: Graceful error handling at app and feature levels
- **Protected Routes**: Authentication-based route protection
- **Performance Tracking**: Built-in performance monitoring utilities