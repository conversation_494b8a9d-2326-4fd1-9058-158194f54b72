<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Export Pipeline</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .success { border-color: #4CAF50; background: #f0f8f0; }
        .error { border-color: #f44336; background: #fdf0f0; }
        .warning { border-color: #ff9800; background: #fff8f0; }
        pre {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 12px;
            max-height: 300px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .console-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #007cba;
            background: #f0f8ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Export Pipeline Debugger</h1>
        <p>This tool helps debug the AI image export issue by tracing the complete export pipeline with real data.</p>

        <div class="step">
            <h3>Step 1: Create Test Document with AI Image</h3>
            <p>First, we'll create a test document structure with a simulated AI-generated image using blob URL.</p>
            <button onclick="createTestDocument()">Create Test Document</button>
        </div>

        <div class="step">
            <h3>Step 2: Test Export Pipeline</h3>
            <p>Run the export pipeline functions with debugging enabled to see where blob URLs are lost.</p>
            <button onclick="testExportPipeline()">Test Export Pipeline</button>
            <button onclick="testRealBlobUrl()">Test with Real Blob URL</button>
        </div>

        <div class="step">
            <h3>Step 3: Analyze Results</h3>
            <p>Review the console output to identify the exact failure point.</p>
            <button onclick="clearConsole()">Clear Console</button>
        </div>

        <div id="test-results"></div>
        <div id="console-output" class="console-output" style="display: none;"></div>
    </div>

    <script>
        let testDocument = null;
        let consoleOutput = [];

        // Override console.log to capture output
        const originalConsoleLog = console.log;
        const originalConsoleWarn = console.warn;
        const originalConsoleError = console.error;

        function captureConsole() {
            console.log = function(...args) {
                const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)).join(' ');
                consoleOutput.push(`[LOG] ${new Date().toLocaleTimeString()}: ${message}`);
                originalConsoleLog.apply(console, args);
                updateConsoleDisplay();
            };

            console.warn = function(...args) {
                const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)).join(' ');
                consoleOutput.push(`[WARN] ${new Date().toLocaleTimeString()}: ${message}`);
                originalConsoleWarn.apply(console, args);
                updateConsoleDisplay();
            };

            console.error = function(...args) {
                const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)).join(' ');
                consoleOutput.push(`[ERROR] ${new Date().toLocaleTimeString()}: ${message}`);
                originalConsoleError.apply(console, args);
                updateConsoleDisplay();
            };
        }

        function updateConsoleDisplay() {
            const consoleDiv = document.getElementById('console-output');
            consoleDiv.style.display = 'block';
            consoleDiv.textContent = consoleOutput.join('\n');
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }

        function clearConsole() {
            consoleOutput = [];
            document.getElementById('console-output').style.display = 'none';
            document.getElementById('test-results').innerHTML = '';
        }

        // Mock blob storage service
        class MockBlobStorageService {
            constructor() {
                this.blobMap = new Map();
            }

            createBlobUrl(base64Data, mimeType, metadata = {}) {
                const binaryString = atob(base64Data);
                const bytes = new Uint8Array(binaryString.length);
                for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }
                const blob = new Blob([bytes], { type: mimeType });
                const blobUrl = URL.createObjectURL(blob);
                this.blobMap.set(blobUrl, { base64Data, mimeType, blob, metadata, createdAt: Date.now(), size: bytes.length });
                return blobUrl;
            }

            getBlobAsDataUrl(blobUrl) {
                const data = this.blobMap.get(blobUrl);
                if (data) {
                    return `data:${data.mimeType};base64,${data.base64Data}`;
                }

                // For real blob URLs that aren't in our map, try to convert them
                if (blobUrl.startsWith('blob:')) {
                    console.log('⚠️ MOCK BLOB STORAGE: Real blob URL not in storage map:', blobUrl);
                    // In a real implementation, this would fetch the blob and convert it
                    // For testing, we'll return null to simulate the failure
                    return null;
                }

                return null;
            }

            isManagedBlobUrl(url) {
                return url && url.startsWith('blob:') && this.blobMap.has(url);
            }
        }

        const mockBlobStorageService = new MockBlobStorageService();

        function createTestDocument() {
            console.log('🏗️ Creating test document with AI-generated image...');
            
            // Create a test base64 image (1x1 pixel PNG)
            const testBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==";
            const blobUrl = mockBlobStorageService.createBlobUrl(testBase64, 'image/png', {
                source: 'gemini',
                prompt: 'Test AI image',
                generatedAt: Date.now()
            });

            console.log('🔗 Created blob URL:', blobUrl);

            // Create test document structure
            testDocument = {
                documentData: {
                    title: 'Test Document',
                    author: 'Test Author',
                    description: 'Test document with AI-generated image'
                },
                generatedContent: {
                    editorHTML: `
                        <div>
                            <h1>Test Document</h1>
                            <p>This document contains an AI-generated image:</p>
                            <img src="${blobUrl}" 
                                 alt="AI generated image: Test AI image" 
                                 data-ai-generated="true" 
                                 data-generation-id="test_123" 
                                 data-prompt="Test AI image" 
                                 data-style="photorealistic" 
                                 class="tiptap-image max-w-full h-auto rounded-lg shadow-sm" />
                            <p>End of document.</p>
                        </div>
                    `,
                    chapters: []
                }
            };

            console.log('✅ Test document created successfully');
            addTestResult('Test Document Created', 'success', 'Document with AI-generated blob URL image created', 
                `Blob URL: ${blobUrl}\nHTML length: ${testDocument.generatedContent.editorHTML.length}`);
        }

        function testExportPipeline() {
            if (!testDocument) {
                addTestResult('Error', 'error', 'Please create test document first');
                return;
            }

            console.log('🧪 Starting export pipeline test...');

            // Test the cleaning function (from our fixed export service)
            const cleanedHTML = cleanEditorHTMLForExport(testDocument.generatedContent.editorHTML);

            console.log('🧹 Cleaned HTML result:', cleanedHTML.substring(0, 200) + '...');

            // Test image extraction
            const extractedImages = extractImagesFromHTML(cleanedHTML);

            console.log('🖼️ Extracted images:', extractedImages);

            // Analyze results
            const hasBlobUrls = cleanedHTML.includes('blob:');
            const hasDataUrls = cleanedHTML.includes('data:image/');
            const hasPlaceholders = cleanedHTML.includes('[Image:');

            let status = 'error';
            let message = 'Unknown result';

            if (hasDataUrls && !hasBlobUrls && !hasPlaceholders) {
                status = 'success';
                message = '✅ Export pipeline working correctly - blob URL converted to data URL';
            } else if (hasPlaceholders) {
                status = 'error';
                message = '❌ Export pipeline failed - blob URL replaced with placeholder';
            } else if (hasBlobUrls) {
                status = 'warning';
                message = '⚠️ Export pipeline incomplete - blob URL not converted';
            }

            addTestResult('Export Pipeline Test', status, message,
                `Original HTML length: ${testDocument.generatedContent.editorHTML.length}\n` +
                `Cleaned HTML length: ${cleanedHTML.length}\n` +
                `Has blob URLs: ${hasBlobUrls}\n` +
                `Has data URLs: ${hasDataUrls}\n` +
                `Has placeholders: ${hasPlaceholders}\n` +
                `Extracted images: ${extractedImages.length}\n\n` +
                `Cleaned HTML preview:\n${cleanedHTML.substring(0, 500)}...`);
        }

        function testRealBlobUrl() {
            console.log('🔬 Testing with real blob URL...');

            // Create a real blob URL from canvas
            const canvas = document.createElement('canvas');
            canvas.width = 100;
            canvas.height = 100;
            const ctx = canvas.getContext('2d');

            // Draw a simple test image
            ctx.fillStyle = '#ff0000';
            ctx.fillRect(0, 0, 50, 50);
            ctx.fillStyle = '#00ff00';
            ctx.fillRect(50, 0, 50, 50);
            ctx.fillStyle = '#0000ff';
            ctx.fillRect(0, 50, 50, 50);
            ctx.fillStyle = '#ffff00';
            ctx.fillRect(50, 50, 50, 50);

            // Convert to blob and create blob URL
            canvas.toBlob((blob) => {
                const realBlobUrl = URL.createObjectURL(blob);
                console.log('🔗 Created real blob URL:', realBlobUrl);

                // Create test HTML with real blob URL
                const testHTML = `
                    <div>
                        <h1>Test Document with Real Blob URL</h1>
                        <p>This document contains a real blob URL image:</p>
                        <img src="${realBlobUrl}"
                             alt="AI generated image: Test real blob"
                             data-ai-generated="true"
                             data-generation-id="real_test_123"
                             data-prompt="Test real blob image"
                             data-style="photorealistic"
                             class="tiptap-image max-w-full h-auto rounded-lg shadow-sm" />
                        <p>End of document.</p>
                    </div>
                `;

                console.log('📝 Testing real blob URL in export pipeline...');

                // Test the export cleaning function
                const cleanedHTML = cleanEditorHTMLForExport(testHTML);

                // Analyze results
                const hasBlobUrls = cleanedHTML.includes('blob:');
                const hasDataUrls = cleanedHTML.includes('data:image/');
                const hasPlaceholders = cleanedHTML.includes('[Image:');

                let status = 'error';
                let message = 'Unknown result';

                if (hasDataUrls && !hasBlobUrls && !hasPlaceholders) {
                    status = 'success';
                    message = '✅ Real blob URL converted successfully';
                } else if (hasPlaceholders) {
                    status = 'error';
                    message = '❌ Real blob URL replaced with placeholder - THIS IS THE BUG!';
                } else if (hasBlobUrls) {
                    status = 'warning';
                    message = '⚠️ Real blob URL not converted';
                }

                addTestResult('Real Blob URL Test', status, message,
                    `Original blob URL: ${realBlobUrl}\n` +
                    `Original HTML length: ${testHTML.length}\n` +
                    `Cleaned HTML length: ${cleanedHTML.length}\n` +
                    `Has blob URLs: ${hasBlobUrls}\n` +
                    `Has data URLs: ${hasDataUrls}\n` +
                    `Has placeholders: ${hasPlaceholders}\n\n` +
                    `Cleaned HTML:\n${cleanedHTML}`);

                // Clean up the blob URL
                URL.revokeObjectURL(realBlobUrl);

            }, 'image/png');
        }

        // Mock functions based on the actual export service
        function cleanEditorHTMLForExport(editorHTML) {
            console.log('🔍 EXPORT DEBUG: cleanEditorHTMLForExport called');
            console.log('📝 EXPORT DEBUG: Input HTML length:', editorHTML?.length || 0);
            
            if (!editorHTML) {
                console.log('⚠️ EXPORT DEBUG: No editor HTML provided');
                return "";
            }

            // Log any blob URLs in the input
            const blobUrlMatches = editorHTML.match(/blob:[^"'\s>]+/g);
            if (blobUrlMatches) {
                console.log('🔗 EXPORT DEBUG: Found blob URLs in input:', blobUrlMatches.length);
                blobUrlMatches.forEach((url, index) => {
                    console.log(`   ${index + 1}. ${url.substring(0, 50)}...`);
                });
            } else {
                console.log('🔗 EXPORT DEBUG: No blob URLs found in input');
            }

            let cleanHTML = editorHTML;

            // Handle AI-generated blob URLs (fixed regex)
            cleanHTML = cleanHTML.replace(
                /<img[^>]*src="(blob:[^"]*)"[^>]*>/gi,
                (fullMatch, blobUrl) => {
                    console.log('🔄 EXPORT DEBUG: Processing blob URL:', blobUrl.substring(0, 50) + '...');
                    const isAIGenerated = /data-ai-generated\s*=\s*["']true["']/i.test(fullMatch);
                    console.log('🤖 EXPORT DEBUG: Is AI generated:', isAIGenerated);

                    if (isAIGenerated) {
                        try {
                            const dataUrl = mockBlobStorageService.getBlobAsDataUrl(blobUrl);
                            if (dataUrl) {
                                console.log('✅ EXPORT DEBUG: Successfully converted blob URL to data URL');
                                return fullMatch.replace(blobUrl, dataUrl).replace('>', ' data-export-base64="true">');
                            } else {
                                console.warn('⚠️ EXPORT DEBUG: Failed to convert blob URL to data URL');
                                return "<p><em>[Image: AI-generated image - conversion failed]</em></p>";
                            }
                        } catch (error) {
                            console.error('❌ EXPORT DEBUG: Error converting blob URL:', error);
                            return "<p><em>[Image: AI-generated image - conversion error]</em></p>";
                        }
                    } else {
                        console.log('🚫 EXPORT DEBUG: Non-AI blob URL, replacing with placeholder');
                        return "<p><em>[Image: Blob URL - not exported]</em></p>";
                    }
                }
            );

            console.log('📤 EXPORT DEBUG: cleanEditorHTMLForExport completed');
            return cleanHTML.trim();
        }

        function extractImagesFromHTML(htmlContent) {
            console.log('🖼️ EXTRACT DEBUG: extractImagesFromHTML called');
            
            if (!htmlContent) return [];

            const parser = new DOMParser();
            const doc = parser.parseFromString(htmlContent, 'text/html');
            const images = doc.querySelectorAll('img');
            
            console.log('🖼️ EXTRACT DEBUG: Found img elements:', images.length);

            return Array.from(images).map((img, index) => {
                const src = img.getAttribute('src');
                const isAIGenerated = img.getAttribute('data-ai-generated') === 'true';
                const isBlobUrl = src?.startsWith('blob:') || false;

                console.log(`🖼️ EXTRACT DEBUG: Image ${index + 1}:`, {
                    src: src?.substring(0, 50) + '...',
                    isAIGenerated,
                    isBlobUrl
                });

                return {
                    src,
                    alt: img.getAttribute('alt') || '',
                    isAIGenerated,
                    isBlobUrl,
                    index
                };
            }).filter(img => img.src);
        }

        function addTestResult(title, status, message, details = null) {
            const resultsDiv = document.getElementById('test-results');
            const testDiv = document.createElement('div');
            testDiv.className = `test-section ${status}`;
            
            let html = `<h3>${title}</h3><p>${message}</p>`;
            if (details) {
                html += `<pre>${details}</pre>`;
            }
            
            testDiv.innerHTML = html;
            resultsDiv.appendChild(testDiv);
        }

        // Initialize console capture
        captureConsole();

        // Add instructions
        addTestResult('Instructions', 'success', 
            'Follow the steps above to debug the export pipeline. Check the browser console and the captured output below for detailed logging.',
            'This tool will help identify exactly where in the export pipeline blob URLs are being replaced with placeholder text.');
    </script>
</body>
</html>
